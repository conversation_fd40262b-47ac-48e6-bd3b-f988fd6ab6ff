{$pageTitle = 'Poradna'}

{block #content}

  <div class="article help">

    <h1>Poradna</h1>

    {* vyhledavani form *}
    <fieldset>

      <legend>Vyhledávání</legend>

      {form commentSearchForm}

      {* vypise chyby formulare *}
      {include ../@formErrors.latte form=>$form}

      <p>
        {label proname /}<br>
        {input proname}
      </p>
      <p>
        {label fulltext /}<br>
        {input fulltext}
      </p>
      <p>
      {foreach $form['comcat']->controls as $item}
        <span class="help__checkbox">
        <?php echo $item->getControl()?>
        <?php echo $item->getLabel()?>
        </span>
      {/foreach}
      </p>

      {input commentSearch}

      {/form}

    </fieldset>

    <fieldset>

      <legend>Váš dotaz</legend>

      {form commentForm}

      {* vypise chyby formulare *}
      {include ../@formErrors.latte form=>$form}

      <p>
        {label cmtnick /}<br>
        {input cmtnick}
      </p>
      <p>
        {label cmtmail /}<br>
        {input cmtmail}
      </p>
      <p>
        {label cmtsubj /}<br>
        {input cmtsubj}
      </p>
      <p>
        {label cmttext /}<br>
        {input cmttext}
      </p>

      <p>Pokud máte dotaz ke konkrétnímu zboží, vkládejte je prosím v detailu tohoto zboží.</p>

      {input submit}

      {/form}

    </fieldset>

    {* vypis komentaru *}
    <div class="comments">

      {foreach $rows as $row}

      <div class="comments__item" id="g{$row->cmtid}">

        {if !empty($row->proid)}
        <h2 class="comments__header"><a href="{plink Product:detail, $row->proid, $template->getProKey($row)}">{$row->proname}</a></h2>
        {/if}
        <p>
          <strong>
          Autor: {$row->cmtnick},
          {$row->cmtsubj}
          </strong>
          {$row->cmtdatec|date:'d.m.Y H:m'} {if $adminLogIn}<a href="{plink :Admin:Comment:edit, $row->cmtid}" target="admin" class="control control--success"><i class="icon icon--wheel"></i></a>{/if}<br>
        </p>
        <p class="help__text">
          {!$row->cmttext|commentStripTags:($row->cmtmail==$presenter->config["SERVER_MAIL"])}
        </p>
        <p>
          <a href="{plink 'this', 'reid'=>$row->cmtid, 'subj'=>'Re: '.$row->cmtsubj}">Reagovat</a>
          {if !empty($row->proid)}<a href="{plink Product:detail, $row->proid, $template->getProKey($row)}">Detail zboží</a>{/if}
        </p>

          {if $row->res}
            {foreach $row->res as $skey => $srow}
            {if $iterator->isFirst()}
            <div class="comments__item comments__item--reply">
            {/if}
              <p>
                <strong>Autor: {$srow->cmtnick}, {$srow->cmtsubj}</strong> {$srow->cmtdatec|date:'d.m.Y H:m'} {if $adminLogIn}<a href="{plink :Admin:Comment:edit, $srow->cmtid}" target="admin" class="control control--success"><i class="icon icon--wheel"></i></a>{/if}
              </p>
              <p class="help__text">
                {!$srow->cmttext|commentStripTags:($srow->cmtmail==$presenter->config["SERVER_MAIL"])}
              </p>
            {if $iterator->isLast()}
            </div>
            {/if}
            {/foreach}

          {/if}

      </div>

      {/foreach}

    </div>

  <!-- stránkování start -->
  {control paginator}
  <!-- stránkování end -->

</div>
{/block}
