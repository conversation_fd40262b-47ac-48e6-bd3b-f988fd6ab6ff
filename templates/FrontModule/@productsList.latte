<?php
  $proIds = array();
?>
{* <!-- výpis produktů start --> *}
<section role="region">
  {* stránkování, řazení *}
  {include paginatorSortBlock style=>'top'}

  {if !empty($title)}
  <h2 class="category__header">{$title}</h2>
  {/if}

  {foreach $products as $row}
    <?php
    $proIds[] = $row->proid;
    $GLOBALS["ecommTotalValue"] += $row->proprice;
    ?>
    {if $iterator->isFirst()}
      <div class="row row--autoclear row--border row--flex">
    {/if}

    {* jedna položka *}
    {include @productItem.latte, product => $row}

    {if $iterator->isLast()}
      </div>
    {/if}
  {/foreach}

  {if count($products) == 0}<p>Nejsou zde žádné výrobky</p>{/if}

  {* str<PERSON>kování, řazen<PERSON> dole *}
  {include paginatorSortBlock style=>'bottom'}

</section>
{* <!-- výpis produktů end --> *}


<?php
  $GLOBALS["ecommProId"] = '["'.implode('","', $proIds).'"]';
  ?>

{define paginatorSortBlock}
  {default $showPagination = FALSE}
  {if $showPagination}
  <div class="category__filters category__filters--{$style}">

    {* <!-- stránkování start --> *}
    {control paginator}
    {* <!-- stránkování end --> *}

    {* <!-- řazení start --> *}
    <div class="filter">
      <?php
        $o = $presenter->o;
      ?>
      <strong>Řazení:</strong>

      {if $o=='pa'}<span class="label label--sort label label--is-active">nejlevnější</span>
      {else}<a href="{plink 'this', 'o'=>'pa'}"><span class="label label--sort">nejlevnější</span></a>{/if}

      {if $o=='pd'}<span class="label label--sort label label--is-active">nejdražší</span>
      {else}<a href="{plink 'this', 'o'=>'pd'}"><span class="label label--sort">nejdražší</span></a>{/if}


      {if $o=='na'}<span class="label label--sort label label--is-active">A - Z</span>
      {else}<a href="{plink 'this', 'o'=>'na'}"><span class="label label--sort">A - Z</span></a>{/if}

      {if $o=='nd'}<span class="label label--sort label label--is-active">Z - A</span>
      {else}<a href="{plink 'this', 'o'=>'nd'}"><span class="label label--sort">Z - A</span></a>{/if}

    </div>
    {* <!-- řazení end --> *}

  </div>
  {/if}

{/define}
