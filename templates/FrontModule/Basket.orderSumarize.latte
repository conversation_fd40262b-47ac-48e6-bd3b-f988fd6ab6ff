{default $pageTitle=$template->translate('Souhrn objednávky')}
{default pageRobots      => "nofollow,noindex"}

{* v kosiku drobecky nejsou *}
{block #crumb}
{/block}

{block #content}
<div class="order order--4">

  <div class="row">

    <div class="col-xs-12">

      <h1 class="order__header">{_'Souhrn objednávky'}</h1>

    </div>

  </div>

  <div class="row order-progress">

    <div class="col-xs-12 col-sm-3 order-progress__item"><a href="{plink default}">{_'Nákupní koš<PERSON>'}</a></div>
    <div class="col-xs-12 col-sm-3 order-progress__item"><a href="{plink orderDelMode}">{_'Doprava a platba'}</a></div>
    <div class="col-xs-12 col-sm-3 order-progress__item"><a href="{plink orderContact}">{_'Dodac<PERSON> údaje'}</a></div>
    <div class="col-xs-12 col-sm-3 order-progress__item"><span class="is-active">{_'Souhrn objednávky'}</span></div>

  </div>

 <div class="order__article">

  <h2>{_'Souhrn informací Vaší objednávky'}</h2>
   <table class="order__table" cellpadding="3" cellspacing="0" border="1">
     <tbody>
     {foreach $basket->items as $id=>$value}
       <tr>
         <td class="order__product">
           <a href="{plink Product:detail, $productRows[$id]->proid, $template->getProKey($productRows[$id])}">
             <img src="{$baseUri}/{!$template->getProductPicNameMaster($productRows[$id], 'list')}" alt="{$productRows[$id]->proname}">
           </a>
           <a href="{plink Product:detail, $productRows[$id]->proid, $template->getProKey($productRows[$id])}">
             {$productRows[$id]->proname}
           </a>
           {*nezahrnuje se do slevy*}
           {if $productRows[$id]->pronotdisc == 1}<span class="order__star">*</span>{/if}
         </td>
         <td class="order__count">{$value} ks
           {if (int)$productRows[$id]->proqty > 0 && (int)$productRows[$id]->proqty < (int)$basket->items[$id]}<br />Pouze {$productRows[$id]->proqty}ks{/if}
         </td>
         <td class="order__price">
           {($productRows[$id]->proprice*$value)|formatPrice}
         </td>
       </tr>
     {/foreach}
     </tbody>
     <tfoot>
     <tr class="order__sumprice">
       <td  class="order__product" colspan="2">{$delname}</td>
       <td class="order__price">{$delprice|formatPrice}</td>
     </tr>
     {if !empty($basket->coupon)}
       <tr class="order__discount">
         <td class="order__product"><strong>{_'Slevový kupón'} {$basket->coupon->coucode}</strong></td>
         <td colspan="2">
           {if ((int)$basket->coupon->couvalue == 0)}
             Získáváte:<br>
             <ul>
               {if $basket->coupon->coupayfree == 1}<li>Platba zdarma</li>{/if}
               {if $basket->coupon->coudelfree == 1}<li>Doprava zdarma</li>{/if}
               {foreach $basket->coupon->products as $product}
                 <li>Sleva {$product["prodiscount"]|formatPrice} na {$product["proname"]} (původní cena: {$product["proprice"]|formatPrice})
                   {if $product["used"] == FALSE}<a href="{plink add $product["proid"]}">vložit do košíku a využít slevu</a>{/if}
                 </li>
               {/foreach}
             </ul>
           {/if}
         </td>
         <td></td>
       </tr>
     {/if}

     {if $basket->discountVal > 0}
       <tr class="order__discount">
         <td><strong>{_'Sleva'} {if $basket->discountVal > 0}{$basket->discountPer}%{/if}</strong></td>
         <td colspan="2" class="order__price"><strong>{if $basket->discountVal > 0}{$basket->discountVal|formatPrice}{else}bez slevy{/if}</strong></td>
       </tr>
     {/if}

     {if !empty($basket->coupon)}
       <tr class="order__discount">
         <td class="order__product"><strong>{_'Slevový kupón'} {$basket->coupon->coucode}</strong></td>
         <td colspan="2">
           {if ((int)$basket->coupon->couvalue == 0)}
             Získáváte:<br>
             <ul>
               {if $basket->coupon->coupayfree == 1}<li>Platba zdarma</li>{/if}
               {if $basket->coupon->coudelfree == 1}<li>Doprava zdarma</li>{/if}
               {foreach $basket->coupon->products as $product}
                 <li>Sleva {$product["prodiscount"]|formatPrice} na {$product["proname"]} (původní cena: {$product["proprice"]|formatPrice})
                   {if $product["used"] == FALSE}<a href="{plink add $product["proid"]}">vložit do košíku a využít slevu</a>{/if}
                 </li>
               {/foreach}
             </ul>
           {/if}
         </td>
         <td></td>
       </tr>
     {/if}

     {if $basket->discountVal > 0}
       <tr class="order__discount">
         <td><strong>{_'Sleva'} {if $basket->discountVal > 0}{$basket->discountPer}%{/if}</strong></td>
         <td colspan="2" class="order__price"><strong>{if $basket->discountVal > 0}{$basket->discountVal|formatPrice}{else}bez slevy{/if}</strong></td>
       </tr>
     {/if}
     </tfoot>
   </table>

  <h2>{if empty($formData['ordstname'])}{_'Fakturační a současně doručovací adresa'}{else}Fakturační adresa{/if}</h2>

  <table class="table table--vertical" cellpadding="3" cellspacing="0" border="1">
    <tbody>
      <tr>
        <th>Jméno, příjmení:</th>
        <td>{$formData['ordiname']} {$formData['ordilname']}</td>
      </tr><tr>
        <th>Název firmy:</th>
        <td>{$formData['ordifirname']}</td>
      </tr><tr>
        <th>Ulice:</th>
        <td>{$formData['ordistreet']} {$formData['ordistreetno']}</td>
      </tr><tr>
        <th>PSČ, město:</th>
        <td>{$formData['ordipostcode']} {$formData['ordicity']}</td>
      </tr><tr>
        <th>Email:</th>
        <td>{$formData['ordmail']}</td>
      </tr><tr>
        <th>Telefon:</th>
        <td>{$formData['ordtel']}</td>
      </tr><tr>
        <th>IČ:</th>
        <td>{$formData['ordic']}</td>
      </tr><tr>
        <th>DIČ:</th>
        <td>{$formData['orddic']}</td>
      </tr>
    </tbody>
  </table>

  {if !empty($formData['ordstname'])}

    <h2>{_'Adresa dodání'}</h2>

    <table class="table table--vertical" cellpadding="3" cellspacing="0" border="1">
      <tbody>
        <tr>
          <td>Jméno, příjmení:</td>
          <td>{$formData['ordstname']} {$formData['ordstlname']}</td>
        </tr><tr>
          <td>Název firmy:</td>
          <td>{$formData['ordstfirname']}</td>
        </tr><tr>
          <td>Ulice:</td>
          <td>{$formData['ordststreet']} {$formData['ordststreetno']}</td>
        </tr><tr>
          <td>PSČ, město:</td>
          <td>{$formData['ordstpostcode']} {$formData['ordstcity']}</td>
        </tr>
      </tbody>
    </table>

  {/if}

  {if !empty($formData['ordnote'])}

    <h2>{_'Vzkaz k objednávce'}</h2>

    <table class="table table--vertical" cellpadding="3" cellspacing="0" border="1">
      <tbody>
        <tr>
          <td>{!$formData['ordnote']|nl2br}</td>
        </tr>
      </tbody>
    </table>

  {/if}

   <div class="order__price-final">
  <div class="container-fluid">
    {_'CENA CELKEM'} (včetně DPH)

    <?php
      $priceSum = $basket->priceSumVat + $delprice - $basket->discountVal;
      $priceSumEur = 0;
      if ($delivery->delcouid == 2 && (double)$presenter->config["CURR_RATE"] > 0) {
        $priceSumEur = round($priceSum / (double)$presenter->config["CURR_RATE"], 1);
        $priceSumEurFormated = number_format($priceSumEur, 1, ",", " ");
      }
    ?>

    <strong>{$priceSum|formatPrice} {if $priceSumEur > 0} ({$priceSumEurFormated}0 €){/if}</strong>
  </div>
  </div>

  {form orderSumarizeForm}

    <div class="row order-controls">

      <div class="col-xs-12 col-sm-4">
        <a href="{plink 'orderContact'}" class="btn btn--back btn--big"><i class="icon icon--arrow-left"></i> {_'O krok zpět'}</a>
      </div>

      <div class="col-xs-12 col-sm-8">
        <p>{input agreement} potvrzuji, že souhlasím s <a href="{$baseUri}/obchodni-podminky-t26" target="_blank">Obchodními&nbsp;podmínkami</a> obchodu {$presenter->config["SERVER_NAMESHORT"]}.</p>
        <p>{label ordheurekagdpr:}{input ordheurekagdpr:}Souhlasím se zasláním dotazníku spokojenosti v rámci programu Ověřeno zákazníky,<br>který pomáhá zlepšovat vaše služby.{/label}</p>
        <button type="submit" id="frm-orderSumarizeForm-submit" name="_submit" class="btn--big btn--buy">Závazně objednat</button>
      </div>

    </div>

  {/form}

 </div>

</div>
{/block}
