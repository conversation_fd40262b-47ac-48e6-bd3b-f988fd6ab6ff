  {foreach $rows as $row}
    {if $iterator->isFirst()}
"objednávka";"j<PERSON><PERSON>";"p<PERSON><PERSON><PERSON><PERSON><PERSON>";"email";"telefon";"bal<PERSON><PERSON>";"dobírka";"heslo";"vyz<PERSON><PERSON><PERSON>";"podatelna";"variabil";"poznámka";"kód z<PERSON>.";"plnoletost";"prijimat_karty"
    {/if}
    <?php
      $number = trim($row->ordtel);
      $number = str_replace(' ', '', $number);
      $number = "+420".substr($number, -9);
    ?>
"{$row->ordcode}";"{$row->ordiname}";"{$row->ordilname}";"{$row->ordmail}";"{$number}";"1";{if $row->paycode=='dobirka'}"{$row->ordpricevat}"{/if};"";"{$row->orddelspec}";"";"{$row->ordcode}";"<PERSON><PERSON>ehk<PERSON>";"{$row->ordcode}";"0";""
  {/foreach}
