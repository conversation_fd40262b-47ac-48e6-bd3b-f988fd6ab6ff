{* Product.detail.latte *}

{* nastaveni promennych *}
{$pageTitle       = (!empty($product->protitle) ? $product->protitle : $product->proname)}
{$pageKeywords    = $product->prokeywords}
{$pageDescription = (empty($productMasterData->prodescription) ? $template->striptags($productMasterData->prodescs) : $productMasterData->prodescription)}
{$pageImage = $baseUri."/".$template->getProductPicName($productMasterData, 'detail')}
{* do description doplnim nazev zbozi tak aby se neorezal *}
<?php
  $len = strlen(trim($product->proname).' '.$product->manname);
  $len = 160-$len-1;
  $pageDescription = $template->truncate($pageDescription, $len, '').' '.trim($product->proname).' '.$product->manname;

  $GLOBALS["ecommProId"] = $product->proid;
  $GLOBALS["ecommPageType"] = 'product';
  $GLOBALS["ecommTotalValue"] = $product->proprice;
?>

{* drobky pro detal zbozi *}
{block #crumb}
<div class="breadcrumb">

  <a href="{$baseUri}">{$presenter->config["SERVER_NAMESHORT"]}</a>
  {ifset $catalogPath}
  {foreach $catalogPath as $row}
    <span>»</span>
    <a href="{plink Catalog:detail, $row->catid, $template->getCatKey($row)}">{$row->catname}</a>
  {/foreach}
  {/ifset}
  <span>»</span> <strong>{$product->proname}</strong>

</div>
{/block}

{block #content}

<script>

  fbq('track', 'ViewContent', {
    content_ids: ['{!$product->proid}'],
    content_type: 'product',
    value: {!$product->proprice},
    currency: '{!$curKey}'
  });
</script>

{*<!-- product detail start -->*}
<div class="product-detail">

{if $product->prostatus == 0}

  {* ritch snippets http://schema.org/Product *}
  <script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "Product",
    "name": {$product->proname},
    "image": {$baseUri.'/'.$template->getProductPicName($productMasterData, 'detail')},
    "description": {$pageDescription},
    "manufacturer": {$manufacturer->manname},
    "url": {plink '//this'},
    "offers": {
      "@type": "Offer",
      "availability": {if $product->proaccess == 0}"http://schema.org/InStock"{else}"http://schema.org/OutOfStock"{/if},
      "price": "{!$product->proprice}",
      "priceCurrency": "CZK"
    }
  }
  </script>

  <div class="row">

    <div class="col-xs-12 product-detail__content">

      <h1 class="product-detail__header">{$product->proname} {if $adminLogIn}<a href="{plink :Admin:Product:edit, $product->proid}" target="admin" class="control control--success"><i class="icon icon--wheel"></i></a>{/if}</h1>

      {*<!-- popis start -->*}
      <div class="product-detail__description">
        {!$productMasterData->prodescs}
      </div>
      {*<!-- popis end -->*}

    </div>

    {*<!-- hlavní část produktu start -->*}
    <div class="col-sm-6">

      {*<!-- foto produktu start -->*}
      <div class="product-detail__image gallery">
        <a href="{$baseUri}/{$productMasterData|getProductPicName:'big'}" title="{$product->proname}">
          <img src="{$baseUri}/{!$template->getProductPicName($productMasterData, 'detail')}" alt="{$product->proname}" itemprop="image">
        </a>
      </div>
      {*<!-- foto produktu end -->*}

      {*<!-- další fotografie start -->*}
      <div class="product-detail__gallery gallery">
        {foreach $images as $row}
        <a href="{$baseUri.'/pic/product/big/'.$row["name"]}" title="{$product->proname}">
          <img src="{$baseUri.'/pic/product/list/'.$row["name"]}" alt="{$product->proname}">
        </a>
        {/foreach}
      </div>
      {*<!-- další fotografie end -->*}

    </div>
    {*<!-- hlavní část produktu end -->*}

    {*<!-- boční lišta start -->*}
    <div class="col-sm-6">

      {* štítky *}
      {include @productLabel.latte, product => $product}

      {*<!-- price start -->*}
      <div class="product-detail__price">
        <strong>{_'Cena:'} {$product->proprice|formatPrice} {if $config["PRICEVAT"] == 'inclvat'} s DPH {else} bez DPH {/if}</strong>
      </div>
      {*<!-- price end -->*}

      {if count($subItems) == 0}
        <div class="product-detail__buy">
        {if ($this->config["CHECK_STOCK"] == 1 && $productMasterData->proaccess == 0) || $this->config["CHECK_STOCK"] == 0}
          <a href="{plink Basket:add $product->proid}" class="btn btn--buy btn--big">Koupit</a>
        {else}
        Zboží co není skladem nelze zakoupit.
        {/if}
        </div>
      {/if}

      {*<!-- product info start -->*}
      <div class="product-detail__info">

        <p>
          {_'Termín dodání'}:
          {if $product->proaccess != 100 OR ($product->proaccess == 100 AND empty($product->proaccesstext))}
            <span class="stock{if $product->proaccess == 0} stock--available{else} stock--unavailable{/if}">{$enum_proaccess[$product->proaccess]}</span>
          {else}
            <span class="stock stock--unavailable">{$product->proaccesstext}</span>
          {/if}
        </p>
        <p>
          {_'Výrobce'}: <a href="{plink Manufacturer:detail, $product->promanid, $template->getUrlKey('', $manufacturer->manname)}">{$manufacturer->manname}</a>
        </p>

      </div>
      {*<!-- product info end -->*}

      {*<!-- sociální sítě start -->*}
      <div class="share">
        <strong>{_'Sdílejte na:'}</strong>
        <a href="https://www.facebook.com/sharer/sharer.php?u={plink '//this'}" class="share--facebook" title="sdílet na Facebooku"><i class="icon icon--facebook"></i></a>
        <a href="https://plus.google.com/share?url={plink '//this'}" class="share--googleplus" title="sdílet na Google +"><i class="icon icon--googleplus"></i></a>
        <a href="https://twitter.com/intent/tweet?url={plink '//this'}&text={$product->proname}" class="share--twitter" title="sdílet na Twitteru"><i class="icon icon--twitter"></i></a>
      </div>
      {*<!-- sociální sítě end -->*}

      {*<!-- helpers start -->*}
      <div class="product-detail__helpers">

        <a class="btn btn--small" href="{plink compareAdd, $product->proid}">
          <i class="icon icon--wheel"></i>
          {_'Porovnat'}
        </a>
        <a class="btn btn--small" href="{plink bookmarkAdd, $product->proid}">
          <i class="icon icon--star"></i>
          {_'Přidat k oblíbeným'}
        </a>
        <a class="btn btn--small control--print" href="#">
          <i class="icon icon--print"></i>
          {_'Vytisknout'}
        </a>
        {*
        <a class="btn btn--small" href="#tab4" class="opentab">
          <i class="icon icon--chat"></i>
          {_'Dotazy a komentáře'}
        </a>
        *}
        {*<!-- watchdogs start -->
        <div class="product-detail__watchdog">

          {form watchDogPrice}
          <p>
            <strong><i class="icon icon--dog"></i> {_'Hlídat cenu:'}</strong><br>
            {input dogmail 'placeholder'=>'Zadejte Váš e-mail', class => 'input--50'}
            {input send class => 'btn btn--small'}
          </p>
          {/form}

          {if $product->proaccess > 0}
              {form watchDogStore}
              <p>
                <strong><i class="icon icon--dog"></i> {_'Hlídat naskladnění:'}</strong><br>
                {input dogmail 'placeholder'=>'Zadejte Váš e-mail', class => 'input--50'}
                {input send class => 'btn btn--small'}
              </p>
              {/form}
          {/if}

        </div>
        <!-- watchdogs end -->*}

      </div>
      {*<!-- helpers start -->*}

    </div>
    {*<!-- boční lišta end -->*}

  </div>

  <div class="row">

    {*<!-- variants start -->*}
    <div class="col-xs-12 product-detail__variants">

      {if count($subItems) > 0}
        {form basketAddFormVar}
        {* vypise chyby formulare *}
        {include ../@formErrors.latte form=>$form}

        {foreach $subItems as $row}
        <?php
          if ($productMasterData->proaccess == 100) $row->proaccess = $productMasterData->proaccess;
        ?>
        {if $iterator->isFirst()}
          {if $row->promasid > 0}<h2>Vyberte si variantu produktu</h2>{/if}
          <table class="table" cellpadding="3" cellspacing="0" border="1">
          <tr>
            <th>{if $row->promasid > 0}Příchuť / Název zboží{else}Název zboží{/if}</th>
            <th>Cena s DPH</th>
            <th>Dostupnost</th>
            <th>Počet kusů</th>
          </tr>
        {/if}
        <tr>
          <td>
            {if $row->proid != $product->proid}<a href="{plink Product:detail, $row->proid, $template->getProKey($row)}">{/if}
            {if $row->promasid > 0}
              <img src="{$baseUri}/{!$template->getProductPicName($row, 'usrsize')}" alt="{$row->proname}">
              {$row->proname}
            {else}
              {$product->proname}
            {/if}
            {if $row->proid != $product->proid}</a>{/if}
            {if $adminLogIn}<a href="{plink :Admin:Product:edit, $row->proid}" target="admin" class="control control--success"><i class="icon icon--wheel"></i></a>{/if}
          </td>
          <td><strong>{$row->proprice|formatPrice}</strong></td>
          <td>
            {if $row->proaccess == 100}
              <span class="stock stock--unavailable">{if !empty($row->proaccesstext)}{$row->proaccesstext}{else}Není skladem{/if}</span>
            {elseif $row->proaccess == 0}
              <span class="stock stock--available">Skladem {$row->proqty|getQty}</span>
            {/if}
          </td>
          <td>
            {ifset $form[$row->proid]}
            <span class="control--count"><?php echo $form[$row->proid]['oricnt']->control->size(3)->class('clearonfocus') ?></span>
            {/ifset}
          </td>
        </tr>
        {if $iterator->isLast()}
        </table>
        {if ($this->config["CHECK_STOCK"] == 1 && $productMasterData->proaccess == 0) || $this->config["CHECK_STOCK"] == 0}
        <p><?php echo $form['buy']->control->class('btn btn--buy btn--big'); ?></p>
        {/if}
        {/if}
        {/foreach}
        {/form basketAddFormVar}
      {/if}

    </div>
    {*<!-- variants end -->*}

  </div>

  {*<!-- tabs start -->*}
  <div class="product-detail__tabs">

    <?php $tab = (string)$presenter->getParam('tab'); ?>
    <div class="tabs" id="tabs">
      <div class="row">
        <div class="col-xs-12 col-sm-3"><a href="#tab1" class="tabs__name{if $tab == ''} is-active{/if}">{_'Popis'}</a></div>
        {*
        <div class="col-xs-12 col-sm-3"><a href="#tab2" class="tabs__name{if $tab == 'parametry'} is-active{/if}">{_'Parametry'}</a></div>
        <div class="col-xs-12 col-sm-3"><a href="#tab3" class="tabs__name{if $tab == 'prislusenstvi'} is-active{/if}">{_'Příslušenství'}</a></div>
        *}
      </div>
    </div>

  <div class="row tabs__content">

    {*<!-- tab text start -->    *}
    <div class="col-xs-12{if $tab == ''} is-active{/if}" id="tab1">

      <div class="tabs__into article">

        <h2 class="tabs__header">{$product->proname}</h2>

        {* text produktu *}
        {!$productMasterData->prodesc}

        {* youtube videa *}
        {if !empty($product->provideo)}
        <h2>Prohlédněte si video</h2>
        <div id="video"><div><iframe width="560" height="315" src="https://www.youtube.com/embed/{$product->provideo}"  allowfullscreen></iframe></div></div>
        {/if}

      </div>

    </div>
    {*<!-- tab text end -->*}

    {*<!-- tab parametry start -->
    <div class="col-xs-12{if $tab == 'parametry'} is-active{/if}" id="tab2">

      <div class="tabs__into article">

        <h3 class="tabs__header">{_'Parametry'}</h3>

        {foreach $proParams as $row}
          {if $iterator->isFirst()}
            <table class="table">
          {/if}
          <tr {if $iterator->isEven()} class="even"{/if}>
            <th>{$row->prpname}:</th>
            <td>{$row->prpvalue}</td>
          </tr>
          {if $iterator->isLast()}
            </table>
          {/if}
        {/foreach}

      </div>

    </div>
    <!-- tab parametry end -->*}

    {*<!-- tab příslušenství start -->
    <div class="col-xs-12{if $tab == 'prislusenstvi'} is-active{/if}" id="tab3">

      <div class="tabs__into article">

        <h3 class="tabs__header">{_'Příslušenství'}</h3>

      </div>

    </div>
    <!-- tab příslušenství end -->*}

  </div>

  {*<!-- tabs end -->*}

{else}
  <p><strong>Tato položka byla vyřazena z nabídky.</strong></p>

  {* výpis produktů *}
  {include @productsList.latte, products => $footerTopProducts, title => 'Nejoblíbenější produkty'}

{/if}

</div>
{*<!-- product detail end -->*}

{* <!-- prislusenstvi zbozi start --> *}
{if count($proAccess) > 0}
  <h2>Příslušenství</h2>
  {include @productsList.latte, products => $proAccess, title => 'Příslušenství'}
{/if}
{* <!-- prislusenstvi zbozi end --> *}

{/block}
