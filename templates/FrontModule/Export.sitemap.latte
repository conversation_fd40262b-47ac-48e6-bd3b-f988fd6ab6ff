{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  {foreach $products as $row}
  <url>
    <loc>{plink //Product:detail, $row->proid, $template->getProKey($row)}</loc>
    <lastmod>{$row->moddate|date:'Y-m-d'}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
  {/foreach}
  {foreach $catalogs as $catalog}
  <url>
    <loc>{plink //Catalog:detail, $catalog->catid, $template->getCatKey($catalog)}</loc>
    <lastmod>{$catalog->moddate|date:'Y-m-d'}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.7</priority>
  </url>
    {foreach $catalog->subCats as $item}
  <url>
    <loc>{plink //Catalog:detail, $item->catid, $template->getCatKey($item)}</loc>
    <lastmod>{$item->moddate|date:'Y-m-d'}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.6</priority>
  </url>
    {/foreach}
  {/foreach}
  {foreach $manufacturers as $mrow}
    <url>
      <loc>{plink //Manufacturer:detail, $mrow->manid, $template->getUrlKey('', $mrow->manname)}</loc>
      <changefreq>daily</changefreq>
      <priority>0.9</priority>
    </url>
  {/foreach}
  {foreach $articles as $row}
    <url>
      <loc>{plink //Article:detail, $row->artid, $template->getUrlKey('', $row->artname)}</loc>
      {if !empty($row->moddate)}<lastmod>{$row->moddate|date:'Y-m-d'}</lastmod>{/if}
      <changefreq>daily</changefreq>
      <priority>0.9</priority>
    </url>
  {/foreach}
  {foreach $menuTopExport as $row}
  <url>
    <loc>{plink //Page:detail, $row->pagid, $template->getUrlKey($row->pagurlkey, $row->pagname)}</loc>
    {if !empty($row->moddate)}<lastmod>{$row->moddate|date:'Y-m-d'}</lastmod>{/if}
    <changefreq>daily</changefreq>
    <priority>0.5</priority>
  </url>
  {/foreach}
  {foreach $menuIndexs as $iRow}
  <url>
    <loc>{plink //Page:detail, $row->pagid, $template->getUrlKey($row->pagurlkey, $row->pagname)}</loc>
    <lastmod>{$iRow->moddate|date:'Y-m-d'}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.8</priority>
  </url>
  {/foreach}
</urlset>
