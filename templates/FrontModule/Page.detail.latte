{$pageTitle       = (empty($page->pagtitle) ? $page->pagname :$page->pagtitle)}
{$pageKeywords    = $page->pagkeywords}
{$pageDescription = $page->pagdescription}

{block #content}

  <div class="article">

    <h1>{$page->pagname}</h1>

    {!$page->pagbody}

    {*
    {foreach $images as $row}
    {if $iterator->isFirst()}
      <div class="article__images">
    {/if}
        <img src="{$baseUri}/files/{$row->atafilename}" alt="{$row->ataname}">
    {if $iterator->isLast()}
      </div>
    {/if}
    {/foreach}
    *}

    {foreach $attachments as $row}
    {if $iterator->isFirst()}
    <div class="article__attachements">

      <h3>Přílohy</h3>

      <ul>
    {/if}
        <li><a href="{$baseUri}/files/{$row->atafilename}" target="_blank"><i class="icon icon--download"></i>{$row->ataname} ({$row->atasize|bytes})</a> </li>
    {if $iterator->isLast()}
      </ul>

    </div>
    {/if}
    {/foreach}

  </div>

{/block}
