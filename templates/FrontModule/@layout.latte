{default $urlkey => ''}
{default $lng    => $lang}

{default pageTitle       => $presenter->config["INDEX_TITLE"]}
{default pageDescription => $presenter->config["INDEX_DESC"]}
{default pageImage       => $baseUri."/img/logo.jpg"}

{* DOCTYPE a <head></head> *}
{include @pageHead.latte pageTitle => $pageTitle, pageDescription => $pageDescription, pageImage => $pageImage}

<body>
{* Page Header *}
{include @pageHeader.latte}

  <div class="content__back">
  <div class="container-fluid">

    <div class="row">

      {* <!-- produktové menu start --> *}
      <div class="col-sm-4 col-md-3 nav-product__wrapper">
        {include @pageMenuProducts.latte}
      </div>
      {* <!-- produktové menu end --> *}

      {if !$showMenuLeft}
      <div class="col-xs-12">
      {else}
      <div class="col-sm-8 col-md-9">
      {/if}

        {*<!-- obsah start -->*}
        <main class="content" role="main">

          {*<!-- drobečková navigace start -->*}
          {block #crumb}
          <div class="breadcrumb">

            <a href="{plink Homepage:default}">Úvod</a> <span>»</span> <strong>{$pageTitle}</strong>

          </div>
          {/block}
          {*<!-- drobečková navigace end -->*}

          {*<!-- chybové hlášky start -->*}
          {foreach $flashes as $flash}
            <div class="alert alert--{$flash->type}" role="alert"><p>{$flash->message}</p></div>
          {/foreach}
          {*<!-- chybové hlášky end -->*}

          {include #content}

        </main>
        {*<!-- obsah end -->*}

      </div>

    </div>

  </div>
  </div>

  {* patička *}
  {include @pageFooter.latte}

  <script>
    WebFontConfig = {
      google: { families: [ 'PT+Sans:400,700:latin,latin-ext' ] }
    };
  </script>
  <script src="{$baseUri}/js/webfont.js" async defer></script>

  <script src="{$baseUri}/js/jquery-1.12.4.min.js"></script>
  {ifCurrent Homepage:default}<script src="{$baseUri}/js/slick/slick.min.js"></script>{/ifCurrent}
  <script src="{$baseUri}/js/scripts.js"></script>
  <script src="https://www.google.com/recaptcha/api.js"></script>

  {ifCurrent Basket:orderDelMode}
  <script>

  function number_format(num) {
    return parseInt( num ).toLocaleString('cs-CZ');
  }

  //kliknul na dopravu - prepocitam celkovou cenu objednavky
  $("input:radio[name='orddelid']").change(function() {
    var payModeName = '';
    var payModePrice = 0;
    var payModeName = '';
    var delModePrice = 0;
    var priceSum = {$priceSumVat};
    var delid = $(this).val();
    {foreach $payModesJs as $pay}
      {if isset($delModes[$pay->delmasid])}
      if (delid == {!$pay->delid}) {
    payModeName = {$pay->delname};
    payModePrice = {!$pay->delprice};
    delModeName = {$delModes[$pay->delmasid]->delname};
    delModePrice = {!$delModes[$pay->delmasid]->delprice};
      }
      {/if}
    {/foreach}

    delPrice = delModePrice + payModePrice;
    priceSum = priceSum + delPrice;
    $( "#priceSumTotalVat" ).html(number_format(priceSum)+' '+{$presenter->curCodes[$presenter->curId]});
    $( "#priceDeliveryVat" ).html(number_format(delPrice)+' '+{$presenter->curCodes[$presenter->curId]});
    $( "#deliveryName" ).html(delModeName+', '+payModeName);
  })

  </script>
  {/ifCurrent}

  {if !empty($neonParameters["google"]["ua"])}
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id={$neonParameters["google"]["ua"]}"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){
        dataLayer.push(arguments);
      }
      gtag('js', new Date());

      gtag('config', '{$neonParameters["google"]["ua"]|noescape}');
    </script>
  {/if}
</body>
</html>
