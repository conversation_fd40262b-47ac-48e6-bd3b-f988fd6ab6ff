{$pageTitle       = 'Vyhledávání'}
{$pageDescription = 'Vyhledávání zboží'}
{$canonicalUrl    = $presenter->link('//Search:default', array('name'=>'', 'fulltext'=>'', 's'=>'', 'o'=>'', 'm'=>array()))}
{$pageRobots = "noindex,follow"}

<?php
  $GLOBALS["ecommProId"] = '';
  $GLOBALS["ecommPageType"] = 'searchresults';
  $GLOBALS["ecommTotalValue"] = 0;
?>

{block #content}
<div class="search article">

  <!-- search article start -->
  <div class="search__article">

  <h1>{_'Podrobné vyhledávání'}</h1>

  <!-- search filters start -->
  <div class="search__filters">

    {form detailSearchForm}
      {* vypise chyby formulare *}
      {include ../@formErrors.latte form=>$form}
      <fieldset>

        <legend>Vyberte výrobce</legend>

        <p>
          {foreach $form['mans']->controls as $item}
          <?php echo $item->getControl()?>
          <?php echo $item->getLabel()?>
          {/foreach}
        </p>

      </fieldset>

      <fieldset>

        <p>
          <?php echo $form["onstock"]->getControl()?>
          <?php echo $form["onstock"]->getLabel('Zobrazit jen zboží skladem')?>
        </p>

        <p>
          <?php echo $form["name"]->getLabel("Zadejte název nebo část názvu zboží")?>:
          <?php echo $form["name"]->getControl()?>
        </p>

        <p>
          <?php echo $form["fulltext"]->getLabel("Zadejte jakékoliv hledané slovo")?>:
          <?php echo $form["fulltext"]->getControl()?>
        </p>

        <p>
          {input detailSearch}
        </p>

      </fieldset>

    {/form}

    </div>

    <!-- sort start -->
    <fieldset>

      <legend>Řazení</legend>

      <div class="search__sort">

        <p>
        {if count($productsData) > 0}
        <?php
           $us_price_val = 'price';
           $us_price_text = 'Cena od nejlevnějšího';
           $us_name_val = 'name';
           $us_name_text = 'Název A-Z';
           $us_default_val = '';
           $us_default_text = 'Výchozí';
           $selPrice = False;
           $selName = False;
           $seldefault = True;
           if (!empty($presenter->o)) {
             switch ($presenter->o) {
                case '':
                  $us_default_val = '';
                  $us_default_text = 'Výchozí';
                  $seldefault = True;
                  break;
                case 'price':
                  $us_price_val = 'price_';
                  $us_price_text = 'Cena od nejlevnějšího';
                  $selPrice = True;
                  $seldefault = false;
                  break;
                case 'name':
                  $us_name_val = 'name_';
                  $us_name_text = 'Název A-Z';
                  $selName = True;
                  $seldefault = false;
                  break;
                case 'price_':
                  $us_price_val = 'price';
                  $us_price_text = 'Cena od nejdražšího';
                  $selPrice = True;
                  $seldefault = false;
                  break;
                case 'name_':
                  $us_name_val = 'name';
                  $us_name_text = 'Název Z-A';
                  $selName = True;
                  $seldefault = false;
                  break;
             }
           }
         ?>

        <a {if $seldefault}class="selected"{/if} href="{plink 'this', 'o'=>$us_default_val}">{$us_default_text}</a>
        <a {if $selName}class="selected"{/if} href="{plink 'this', 'o'=>$us_name_val}">{$us_name_text}</a>
        <a {if $selPrice}class="selected"{/if} href="{plink 'this', 'o'=>$us_price_val}">{$us_price_text}</a>
        {/if}
        </p>

      </div>

    </fieldset>
    <!-- sort end -->

  </div>
  <!-- search filters end -->

  </div>
  <!-- search article end -->

{* výpis produktů *}
{include @productsList.latte, products => $productsData, title => $pageTitle, showPagination => TRUE}

{/block}
