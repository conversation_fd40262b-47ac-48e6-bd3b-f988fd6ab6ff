{contentType application/xml; charset=utf-8}
<?xml version="1.0" encoding="utf-8"?>
<SHOP xmlns="http://www.zbozi.cz/ns/offer/1.0">
{foreach $rows as $row}
<SHOPITEM>
<ITEM_ID>{$row->proid}</ITEM_ID>
{if !empty($row->procode2)}<EAN>{$row->procode2}</EAN>{/if}
<MANUFACTURER>{$row->manname}</MANUFACTURER>
<?php
  $arr = explode('-', $row->proname2);
  $vol = "";
  if (!empty($arr[0]) && !empty($arr[1])) {
    $vol = ' '.trim($arr[0]);
  }
  if (!empty($row->pronames)) {
    $proname = $row->manname.' '.$row->pronames;
  } else {
    if ($row->promasid > 0) {
      $proname = $row->manname.' '.$row->proname.' '.$row->proname2;  
    } else {  
      $proname = $row->manname.' '.$row->proname.$vol;  
    }
  }  
?>
{if !empty($row->promasid)}
<ITEMGROUP_ID>{$row->promasid}</ITEMGROUP_ID>
{/if}
<PRODUCTNAME>{$proname}</PRODUCTNAME>
<PRODUCT>{$proname} {if !empty($row->progift)}({$row->progift}){/if}</PRODUCT>
<DESCRIPTION>{$row->prodescs}</DESCRIPTION>
<URL>{plink //:Front:Product:detail, $row->proid, $template->getProKey($row)}</URL>
<ITEM_TYPE>new</ITEM_TYPE>
<DELIVERY_DATE>{$row->proaccess}</DELIVERY_DATE>
<?php
  if (!empty($row->proidmas)) {
    $fileName = ($row->propicnamemas != "" ? trim($row->propicnamemas).'.jpg' : $row->procodemas.'.jpg');
  } else {
    $fileName = ($row->propicname != "" ? trim($row->propicname).'.jpg' : $row->procode.'.jpg');
  }
  $fileName = rawurlencode($fileName);
?>
<IMGURL>{$baseUri}/pic/product/detail/{$fileName}</IMGURL>
<PRICE_VAT>{$row->proprice1a}</PRICE_VAT>
{if $row->procpczbozi > 0}<MAX_CPC>{$row->procpczbozi|formatNumber:2}</MAX_CPC>{/if}
<?php
$catPath = ""; 
if (!empty($row->catid)) {
  $catPath = str_replace('|', ' | ', $row->catpath);
  $catPathIds = explode('|', trim($row->catpathids, '|'));
  $catPathIds = array_reverse($catPathIds); //zacnu prohledavat od konce vetve
  If (!isset($zboziPath[$row->catid])) {
    foreach ($catPathIds as $catid) {
      //prvni kategorii co ma zbozi cestu vezmu pro danou kategorii a vypadnu
      if (isset($zboziCatalogs[$catid])) {
        $zboziPath[$row->catid] = $zboziCatalogs[$catid]->catpathzbozi;
        break;
      }
    }
  }
}   
?>
{if isset($zboziPath[$row->catid])} 
<CATEGORYTEXT>{$zboziPath[$row->catid]}</CATEGORYTEXT>
{/if}
</SHOPITEM>
{/foreach}
</SHOP>
