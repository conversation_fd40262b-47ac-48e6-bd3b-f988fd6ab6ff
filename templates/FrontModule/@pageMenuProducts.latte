{*<!-- product menu start -->*}
<nav class="nav-product{if !$showMenuLeft} nav-product--hidden{/if}" role="navigation">

  <?php
      $mans = array();
      /*
      if (isset($presenter->m)) {
  $mans = $presenter->m;
  }
  */
  if ($presenter->name == "Front:Manufacturer" && $presenter->action == 'detail') {
  $manid = (int)$presenter->getParam('id');
  if ($manid > 0) $mans[$manid] = $manid;
  }
  ?>

  <div class="nav-product--switch{if count($mans) == 0} is-active{/if}">

    <h2 class="nav-product__header">{_'Katalog'}</h2>

    <div class="nav-product__menu">

      {default $thisCatId=0}
      {default $rootCatId=0}

      {define #menuCatalog}
        {foreach $items as $item}
          {if $iterator->isFirst()}
            <ul>
          {/if}
          <li class="{if $iterator->isLast()}last{/if}{if $thisCatId == $item->catid || $rootCatId == $item->catid}is-active{/if}">
            {if $thisCatId == $item->catid}
              <a href="{plink Catalog:detail, $item->catid, $template->getCatKey($item)}"><strong>{$item->catname}</strong></a>
            {else}
              <a href="{plink Catalog:detail, $item->catid, $template->getCatKey($item)}">{$item->catname}</a>
            {/if}

            {if !empty($menuCatalogSubItems[$item->catid])}
              {include #menuCatalog 'items'=>$menuCatalogSubItems[$item->catid]}{/if}

          </li>
          {if $iterator->isLast()}
            </ul>
          {/if}
        {/foreach}
      {/define}

      {include #menuCatalog 'items'=>$menuCatalog}

    </div>

  </div>

  <div class="nav-product--switch{if count($mans) > 0} is-active{/if}">

    <h2 class="nav-product__header nav-product__header--right">{_'Výrobci'}</h2>

    <div class="nav-product__menu">

    {foreach $manufacturers as $row}
      {if $iterator->isFirst()}
        <ul>
      {/if}
      <li {ifset $mans[$row->manid]}class="is-active"{/ifset}>
        <a href="{$presenter->link('Manufacturer:detail', $row->manid, $template->getUrlKey('', $row->manname))}">
          {ifset $mans[$row->manid]}<strong>{$row->manname}</strong>{else}{$row->manname}{/ifset}
          </a>
      </li>
      {if $iterator->isLast()}
        </ul>
      {/if}
    {/foreach}

    </div>

  </div>

  {ifset $menuShopInformation}
  <div class="nav-product__other">

      <h2 class="nav-product__header">Informace z eshopu</h2>

      {foreach $menuShopInformation as $row}
        {if $iterator->isFirst()}
          <ul>
        {/if}
        <li><a href="{plink Page:detail, $row->pagid, $template->getUrlKey($row->pagurlkey, $row->pagname)}">{$row->menname}</a></li>
        {if $iterator->isLast()}
          </ul>
        {/if}
      {/foreach}
  </div>
  {/ifset}

  {ifset $textBlocks["menuleft_add"]}
  <div class="nav-product__textblock">
    {!$textBlocks["menuleft_add"]}
  </div>
  {/ifset}

</nav>
{*<!-- product menu end -->*}
