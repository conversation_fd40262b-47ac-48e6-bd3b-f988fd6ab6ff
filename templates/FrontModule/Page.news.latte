{$pageTitle       = (empty($page->pagtitle) ? $page->pagname :$page->pagtitle)}
{$pageKeywords    = $page->pagkeywords}
{$pageDescription = $page->pagdescription}

{block #content}

  {!$page->pagbody}
   <h1><span>P<PERSON><PERSON><PERSON> novinek</span></h1>
  <div id="newsbox">
  {* vypis novinek *}
  {foreach $rows as $row}
 <div class="news">
  <a href="{plink New:detail $row->newid, $template->webalize($row->newtitle)}"><img src="{$baseUri}/{!$template->getNewPicName($row, 'list')}" alt="{$row->newtitle}" /></a>
  <h3><a href="{plink New:detail $row->newid, $template->webalize($row->newtitle)}">{$row->newtitle}</a></h3>
  <p>{$row->newannot}</p>
  <p class="detail"><a href="{plink New:detail $row->newid, $template->webalize($row->newtitle)}"><PERSON><PERSON><PERSON> více</a></p>
  </div>
  {/foreach}
  </div>

  <!-- stránkování start -->
  {control paginator}
  <!-- stránkování end -->

{/block}
