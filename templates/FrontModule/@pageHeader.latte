{*<!-- h<PERSON><PERSON><PERSON><PERSON> start -->*}
<header class="header" role="banner">

  <div class="container-fluid">

    <div class="row">

      <div class="col-sm-4 col-md-2">

        {*<!-- logo start -->*}
        <div class="header__logo">

          {ifCurrent Homepage:default}
          <img src="{$baseUri}/img/logo.png" alt="{$presenter->config["SERVER_NAME"]}">
          {else}
          <a href="{$baseUri}" title="{$presenter->config["SERVER_NAME"]}"><img src="{$baseUri}/img/logo.png" alt="{$presenter->config["SERVER_NAME"]}"></a>
          {/ifCurrent}

        </div>
        {*<!-- logo end -->*}

      </div>

      <div class="col-sm-8 col-md-5">

        {*<!-- vyhledávání start -->*}
        <div class="search">

          {form searchForm}
            {input fulltext 'id'=>'bfulltext', 'class'=>'search__input', placeholder => 'Hledej...'}
            <button type="submit" name="quickSearch" id="frm-searchForm-quickSearch" value="Hledat" class="search__submit">Vyhledat</button>
          {/form searchForm}

        </div>
        {*<!-- vyhledávání end -->*}

      </div>

      <div class="col-sm-6 col-md-3 login__wrapper">

        {*<!-- přihlášení uživatele start -->*}
        <div class="login">

          <p class="login__header">{_'Přihlášení'}</p>

          {if $userRow->usrid > 0}
          <p class="login__content">
            <a href="{plink User:default}">{if !empty($userRow->usriname)}{$userRow->usriname} {$userRow->usrilname}{else}{$userRow->usrmail}{/if}</a>
            <a href="{plink User:logout}">{_'Odhlásit'}</a>
          </p>
          {else}
          <p class="login__content">
            <a href="{plink User:login}">{_'Přihlášení'}</a>
            <a href="{plink User:add}">{_'Registrace'}</a>
          </p>
          {/if}

        </div>
        {*<!-- přihlášení uživatele end -->*}

      </div>

      <div class="col-sm-6 col-md-2 basket__wrapper">

        {*<!-- košík start -->*}
        <div class="basket">

        {snippet basketWindow}

          {if $basketItemsCnt > 0}
            <a href="{plink Basket:default}">
              <p class="basket__header">{_'Košík'}<span class="basket__count">{$basketItemsCnt}</span></p>
              <p class="basket__content">
                <strong>{$basketItemsCnt}&nbsp;{_'ks'}</strong>
                {_'za'}
                <strong>{$basketPriceSum|formatPrice}</strong>
              </p>
            </a>
          {else}
          <a href="#">
            <p class="basket__header">{_'Košík'}</p>
            <p class="basket__content">{_'je prázdný'}</p>
          </a>
          {/if}

        {/snippet}

        </div>
        {*<!-- košík end -->*}

      </div>

    </div>

    <div class="header__deco">
      {ifCurrent Homepage:default}
      {else}
      <a href="{$baseUri}" title="{$presenter->config["SERVER_NAME"]}">&nbsp;</a>
      {/ifCurrent}
    </div>

  </div>

</header>
{*<!-- hlavička end -->*}

{*<!-- hlavní menu start -->*}
{include @pageMenuTop.latte}
{*<!-- hlavní menu end -->*}
