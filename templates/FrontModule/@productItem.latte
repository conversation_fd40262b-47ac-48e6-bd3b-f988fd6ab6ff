<div class="col-xs-6 col-sm-6 col-md-4 col-lg-4 product__wrapper">

  <article class="product" role="article">

    <h2 class="product__header">
      <a href="{plink Product:detail, $product->proid, $template->getProKey($product)}" title="{$product->proname}">{$product->proname}</a>
    </h2>

    <div class="product__image">

      {* <!-- štítky --> *}
      {include @productLabel.latte, product => $product}
      {* <!-- štítky --> *}

      <a href="{plink Product:detail, $product->proid, $template->getProKey($product)}">
        <img src="{$baseUri}/img/no.jpg" data-src="{$baseUri}/{!$template->getProductPicName($product, 'list')}" alt="{$product->proname}">
      </a>

    </div>

    <div class="product__price">

      {*if $product->proismaster==1} od {/if*}<strong>{$product->proprice|formatPrice}</strong> {_'včetně DPH'}

      {if $product->proaccess == 0}
        <br><span class="stock stock--available">Skladem</span>
      {else}
        <br><span class="stock stock--unavailable">Nedostupné</span>
      {/if}

    </div>

    <div class="product__info">
      {$product->prodescs|truncate:200}
    </div>

    <div class="product__controls">
      <a href="{plink Product:detail, $product->proid, $template->getProKey($product)}" class="btn">{_'Detail'}</a>
      <a href="{plink Basket:add, $product->proid, 1}" class="btn btn--buy">{_'Koupit'}</a>
    </div>

  </article>

</div>
