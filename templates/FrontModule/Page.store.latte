{$pageTitle       = (empty($page->pagtitle) ? $page->pagname :$page->pagtitle)}
{$pageKeywords    = $page->pagkeywords}
{$pageDescription = $page->pagdescription}

{block #content}

  <div class="article article--store">

    <img src="{$baseUri}/img/obchod-big.jpg" alt="">

    <h1>{$page->pagname}</h1>

    {!$page->pagbody}

    {foreach $products as $row}
    {if $iterator->isFirst()}
    <div class="product-store">
    {/if}

      <div class="product-store__item{if $iterator->getCounter() == 4} cls{/if}">

        <h2 class="product-store__header">
          <strong>{$row->storename|truncate:60}</strong><br>{$row->manname|truncate:20}
        </h2>

        <div class="product-store__image">
          <img src="{$baseUri}/{!$template->getProductPicName($row, 'big')}" alt="{$row->proname}">
        </div>

        <div class="product-store__description">
          {$row->storedesc|truncate:105}
        </div>

        <div class="product-store__price">
          {ifset $row->storeprice}Cena: <strong>{$row->storeprice|formatPriceByCurId:1}</strong> s DPH{/ifset}
        </div>
      </div>

    {if $iterator->isLast()}
    </div>
    {/if}
    {/foreach}

  </div>

{/block}
