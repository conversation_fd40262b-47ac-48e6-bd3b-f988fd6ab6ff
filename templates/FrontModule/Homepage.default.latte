<?php
  $GLOBALS["ecommProId"] = '';
  $GLOBALS["ecommPageType"] = 'home';
  $GLOBALS["ecommTotalValue"] = 0;
?>

  {block #content}

    {* <!-- slideshow start --> *}
    <div class="slider">

      <div class="container-fluid slider__body">

        {foreach $menuIndexs as $row}
          <div class="slider__item" style="background: #000 url({$baseUri}/pic/menuindex/{$row->meiid}.jpg) center top no-repeat;">

            <?php
            //načtu text do array
            $arrTexts = array();
            if (!empty($row->meidesc)){
            $arrTexts = explode("\n", trim($row->meidesc));
            }
            ?>
            <div class="slider__content">

              {* toto je nazev banneru *}
              <h2>{$row->meiname}</h2>

              {* texty - co řádek v adminu, to jeden text *}
              <p>
                {foreach $arrTexts as $text}
                  {if $iterator->getCounter() == 1}
                    {$text}
                  {elseif $iterator->getCounter() == 2}
                    {$text}
                  {elseif $iterator->getCounter() == 3}
                    {$text}
                  {/if}
                {/foreach}
              </p>

              {* odkaz *}
              <p>
                <a class="btn btn--small" href="
                {if $row->meipagid > 0}
                  {plink Page:detail $row->pagurlkey}
                {elseif $row->meicatid > 0}
                  {plink Catalog:detail $row->catid, $template->getCatKey($row)}
                {elseif !empty($row->meiprocode)}
                  {plink Product:detail $row->proid, $template->getProKey($row)}
                {else}
                  {$row->meiurl}
                {/if}
                ">více
                </a>
              </p>

            </div>

          </div>

        {/foreach}

      </div>

    </div>
    {* <!-- slideshow end --> *}

    <div class="article">

      <h2>Kosmetika Biolage - vlasová kosmetika pro Vaši krásu</h2>
      <p>Stejně jako zubaři doporučují ty nejlepší zubní pasty, tak&nbsp;i profesionální kadeřník je jediným povolaným, který zná nejlepší kosmetické výrobky. Jedině a&nbsp;pouze on vám může bez ostychu doporučit skutečně kvalitní a&nbsp;ověřenou <strong>vlasovou&nbsp;kosmetiku</strong>. </p>
      <h2>Špičková vlasová kosmetika</h2>
      <p>Právě náš elektronický obchod je tím magickým místem, kde objevíte vyzkoušené přípravky pro vaše vlasy, které za nikterak veliký peníz umocní a zdůrazní vaši krásu. Za kvalitu nabízených produktů ručí <strong>Martin&nbsp;Gottland</strong>, <a href="http://www.salon-biolage.cz/">Salon Biolage</a>.</p>

      {if !empty($textBlocks["atention"])}
          <div class="order-info__item order-info--text">
            {!$textBlocks["atention"]}
          </div>
        {/if}

        {* výpis produktů *}
        {include @productsList.latte, products => $homepageProducts, title => ''}

      <h2>Vyberte si z nabídky kvalitní vlasové kosmetiky</h2>
      <p>Všechny kosmetické přípravky, které vám zde nabízíme, zároveň také používáme v našem Salonu Biolage a&nbsp;domníváme se, že spokojené tváře <a href="http://www.salon-biolage.cz/galerie-ucesu/">našich&nbsp;zákaznic</a> řeknou více, než zbytečné a&nbsp;sáhodlouhé úvody. </p>
      <p>Proto se pohodlně usaďte v&nbsp;bezpečí svých obývacích pokojů či pracoven a&nbsp;vyberte si z&nbsp;bohaté a&nbsp;jedinečné nabídky <strong>vlasové&nbsp;kosmetiky</strong> od výrobců zvučných jmen a&nbsp;značek. Půvab  ženy je totiž jedním z mála trvalých jevů v celém vesmíru a&nbsp;pokud můžeme alespoň troškou přispět k vaší kráse, kterou  zdravými vlasy a&nbsp;dokonalým účesem jednoznačně umocníte, pak budeme spokojeni i&nbsp;my  a&nbsp;nebudeme na webové pavučině tak docela zbytečně.</p>

    </div>

  {/block}

  {block #product-news}

    {* <!-- news home page start --> *}
    <div class="owl-carousel product-news">
      {foreach $homepageNewProducts as $row}
      <div>
        <a href="{plink Product:detail, $row->proid, $template->getProKey($row)}">
        <img src="{$baseUri}/{!$template->getProductPicName($row, 'list')}" alt="{$row->proname}">
        <div class="product-news__header">{$row->proname|truncate:20}</div>
        <div class="product-news__description"><p>{$row->prodescs|truncate:70}</p></div>
        <div class="product-news__price"><span>Cena: <b>{*if $row->proismaster==1} od {/if*}{$row->proprice|formatPrice}</b> s DPH</span></div>
        <span class="aside">Novinka v e-shopu</span>
        </a>
      </div>
      {/foreach}
    </div>
    {* <!-- news homepage end --> *}

  {/block}
