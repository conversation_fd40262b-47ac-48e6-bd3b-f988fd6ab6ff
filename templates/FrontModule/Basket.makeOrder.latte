{default $pageTitle=$template->translate('Odeslání objedná<PERSON>ky')}
{default pageRobots      => "nofollow,noindex"}

{block #crumb}
<div id="crumb">
  <p><PERSON>hl<PERSON>ží<PERSON> si: <a href="{$baseUri}">Úvod</a> /
    <a href="{plink Basket:default}">{_'Košík'}</a> /
    <strong>{_'Doprava a platba, doručovací údaje'}</strong>
  </p>
</div>
{/block}

{block #content}
<h1><span>{_'Doprava a platba, doručovací údaje'}</span></h1>
<div id="order">
 <div class="table basket">
 <table class="grid">
 <thead>
    <tr>
      <th class="name">{_'Název'}</th>
      <th>{_'Kusy'}</th>
      <th>{_'Cena/kus'}</th>
      <th>{_'Cena'}</th>
    </tr>
  </thead>
  <tfoot>
    <tr>
      <td class="name">{_'Celkem'}</td>
      <td></td>
      <td></td>
      <td>{$basket->priceSumTotalVat|formatPrice}</td>
    </tr>
  </tfoot>
  <tbody>
  {foreach $basket->items as $id=>$value}
    <tr>
      <td class="name">{$productRows[$id]->proname}</td>
      <td>{$value}</td>
      <td>{$productRows[$id]->proprice|formatPrice}</td>
      <td>{($productRows[$id]->proprice*$value)|formatPrice}</td>
    </tr>
  {/foreach}
  {if $basket->discountVal > 0}
    <tr class="background">
      <td class="name">{_'Sleva'} {$basket->discountPer}%</td>
      <td></td>
      <td></td>
      <td>{$basket->discountVal|formatPrice}</td>
    </tr>
    {/if}
    {if $basket->weightSum > 0}
    <tr class="background">
      <td colspan="4" class="name">{_'Hmotnost objednávky'} {$basket->weightSum|number:2:',':' '}&nbsp;Kg</td>
      <td></td>
    </tr>
    {/if}
  </tbody>
  </table>
  </div>
<div id="order1">
  <script type="text/javascript" src="{$baseUri}/js/live-form-validation.js"></script>

  {form makeOrderForm}
  {* vykresleni chyb pokud ma vypnuty JS *}
  <ul class="errors" n:if="$form->hasErrors()">
    <li n:foreach="$form->errors as $error">{$error}</li>
  </ul>
  <div class="adresa">

  <fieldset class="border delmodes">
    <legend>{_'Způsob dodání a platby'}</legend>
      {foreach $delModes as $row}
        {if $iterator->isOdd()}
        <div class="formbox">
        {/if}
        <h3>{$row->delname} ({$row->delprice|formatPrice})</h3>
        {if !empty($row->deltext)}<p>{$row->deltext}</p>{/if}
        {if $row->delcode == 'ULOZENKA'}
        <?php echo $form['orddelspec']->control ?><br />
        {/if}
        {foreach $payModes[$row->delid] as $irow}
          <?php echo $form['orddelid']->getControlPart($irow->delid) ?><?php echo $form['orddelid']->getLabelPart($irow->delid) ?> ({$irow->delprice|formatPrice})<br />
        {/foreach}

    {if $iterator->isEven()}
    </div>
    <div class="formbox">
    {else}
    <br />
    {/if}
      {/foreach}

  </fieldset>
  <fieldset class="border">
    <legend>{_'Adresa dodací a fakturační'}</legend>
    <table id="adresa">
      <tbody>
      <tr>
        <td colspan="4"><?php echo $form['ordiname']->getLabel() ?><br><?php echo $form['ordiname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td colspan="4"><?php echo $form['ordilname']->getLabel() ?><br><?php echo $form['ordilname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td colspan="4"><?php echo $form['ordifirname']->getLabel() ?><br><?php echo $form['ordifirname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td colspan="3"><?php echo $form['ordistreet']->getLabel() ?><br><?php echo $form['ordistreet']->control->cols(30) ?></td>
        <td class="form-psc"><?php echo $form['ordistreetno']->getLabel() ?><br><?php echo $form['ordistreetno']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td colspan="3"><?php echo $form['ordicity']->getLabel() ?><br><?php echo $form['ordicity']->control->cols(30) ?></td>
        <td><?php echo $form['ordipostcode']->getLabel() ?><br><?php echo $form['ordipostcode']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordmail']->getLabel() ?><br><?php echo $form['ordmail']->control->cols(30) ?></td>
        <td><?php echo $form['ordtel']->getLabel() ?><br><?php echo $form['ordtel']->control->cols(30) ?></td>
        <td><?php echo $form['ordic']->getLabel() ?><br><?php echo $form['ordic']->control->cols(30) ?></td>
        <td><?php echo $form['orddic']->getLabel() ?><br><?php echo $form['orddic']->control->cols(30) ?></td>
	  </tr>
      <tr>
        <td colspan="4"><?php echo $form['ordusrvat']->control ?> <?php echo $form['ordusrvat']->getLabel() ?></td>
	  </tr>
      </tbody>
  </table>
  </fieldset>
  <fieldset class="border">
    <legend>{_'Dodací adresa'}</legend>
    <table>
      <tbody>
      <tr>
        <td><?php echo $form['shipto']->control ?><?php echo $form['shipto']->getLabel() ?></td>
      </tr>
      </tbody>
    </table>
    <table  id="invoiceAddress">
      <tbody>
      <tr>
        <td colspan="2"><?php echo $form['ordstname']->getLabel() ?><br><?php echo $form['ordstname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td colspan="2"><?php echo $form['ordstlname']->getLabel() ?><br><?php echo $form['ordstlname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td colspan="2"><?php echo $form['ordstfirname']->getLabel() ?><br><?php echo $form['ordstfirname']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordststreet']->getLabel() ?><br><?php echo $form['ordststreet']->control->cols(30) ?></td>
        <td class="form-psc"><?php echo $form['ordststreetno']->getLabel() ?><br><?php echo $form['ordststreetno']->control->cols(30) ?></td>
      </tr>
      <tr>
        <td><?php echo $form['ordstcity']->getLabel() ?><br><?php echo $form['ordstcity']->control->cols(30) ?></td>
        <td><?php echo $form['ordstpostcode']->getLabel() ?><br><?php echo $form['ordstpostcode']->control->cols(30) ?></td>
      </tr>
      </tbody>
  </table>
  </fieldset>
  <fieldset class="border">
	<legend><?php echo $form['ordnote']->getLabel() ?></legend>
	  <table>
	   <tbody>
      <tr>
        <td><?php echo $form['ordnote']->control->cols(60)->rows(5) ?></td>
      </tr>
      {ifset $form['maillist']}
      <tr>
		<td><?php echo $form['maillist']->control ?> <?php echo $form['maillist']->getLabel() ?></td>
      </tr>
      {/ifset}
      </tbody>
  </table>
  </fieldset>
  <div class="submit"> <a class="backshop" href="{plink Basket:default}">zpět do košíku</a>
   <?php echo $form['submit']->getControl()->class('order') ?>
  </div>
  {/form}
 </div>
 </div>
 </div>
{/block}
