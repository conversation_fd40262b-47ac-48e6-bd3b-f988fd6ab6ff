{default $pageTitle=$template->translate('<PERSON><PERSON><PERSON> n<PERSON> ko<PERSON> - v<PERSON>b<PERSON>r dopravy a platby')}
{default pageRobots      => "nofollow,noindex"}

{* v kosiku drobecky nejsou *}
{block #crumb}
{/block}

{block #content}

<script>
  var delIdSelected={(!empty($delid) ? $delid : 0)};
  var payIdSelected={(!empty($payid) ? $payid : 0)};
</script>

<div class="order order--2">

  <div class="row">

    <div class="col-xs-12">

      <h1 class="order__header">{$pageTitle}</h1>

    </div>

  </div>

  <div class="row order-progress">

    <div class="col-xs-12 col-sm-3 order-progress__item"><a href="{plink default}">{_'Nákupní ko<PERSON>'}</a></div>
    <div class="col-xs-12 col-sm-3 order-progress__item"><span class="is-active">{_'Doprava a platba'}</span></div>
    <div class="col-xs-12 col-sm-3 order-progress__item"><span>{_'Dodací údaje'}</span></div>
    <div class="col-xs-12 col-sm-3 order-progress__item"><span>{_'Souhrn objednávky'}</span></div>

  </div>

  <div class="row">

    {form orderDelModeForm}
    {* vykresleni chyb pokud ma vypnuty JS *}

    <div class="alert alert--danger" n:if="$form->hasErrors()">
      <ul>
        <li n:foreach="$form->errors as $error">{$error}</li>
      </ul>
    </div>

    <div class="row row--inner order-delivery">

      <div class="col-xs-12 col-md-6">

        <h2>Vyberte si způsob doručení</h2>

        <div class="order-delivery__types">

          {foreach $delModes as $row}
          <div class="order-delivery__item">

            <div class="order-delivery__type" id="delid_{$row->delid}" rel="payments_{$row->delid}">

              <h3 class="order-delivery__name">
                <input type="radio" name="delid_type">
                <img src="{$baseUri}/{$row|getDelModePicName}" alt="{$row->delname}">
                {$row->delname}
                <small>{if $row->delprice == 0}(doprava ZDARMA){else}cena od {$row->delprice|formatPrice}{/if}</small>
                {if $row->delcode == 'ULOZENKA'}
                  <br /><br />Výběr pobočky:
                <?php echo $form['orddelspec_ulozenka']->control ?>
                {elseif $row->delcode == 'WEDO'}
                  <br /><br />Výběr pobočky:
                <?php echo $form['orddelspec_wedo']->control ?>
                {/if}
              </h3>

              <div class="order-delivery__content">
                {if !empty($row->deltext) || !empty($row->delurlmap)}
                <p class="order-delivery__description">
                  {if !empty($row->deltext)}{!$row->deltext|nl2br}{/if}
                  {if !empty($row->delurlmap)}<a class="map" href="{!$row->delurlmap}" target="_blank">Orientační mapa</a>{/if}
                </p>
                {/if}
              </div>

            </div>

          </div>
          {/foreach}

        </div>

      </div>

      <div class="col-xs-12 col-md-6">

        <h2>Vyberte si způsob platby</h2>

        <div class="order-delivery__payments">

          {foreach $delModes as $row}
            <div class="order-delivery__payment" id="payments_{$row->delid}">
                <ul>
                {foreach $payModes[$row->delid] as $irow}
                  <li>
                    {label orddelid:$irow->delid}
                      {input orddelid:$irow->delid}
                      {$irow->delname} - <strong>cena {$irow->delprice|formatPrice}</strong>
                    {/label}
                  </li>
                {/foreach}
                </ul>
            </div>
          {/foreach}

        </div>

      </div>

    </div>

    <div class="row row--inner">

      <div class="col-xs-12 order__price-delivery">
        Cena za dopravu a platbu:
        <strong id="priceDeliveryVat">{0|formatPrice}</strong>
      </div>

      <div class="col-xs-12 order__price-final">
        Cena celkem včetně dopravy
        <strong id="priceSumTotalVat">{$basket->priceSumTotalVat|formatPrice}</strong>
      </div>

    </div>

    <div class="row row--inner order-controls">

      <div class="col-xs-12 col-sm-6">
       <a href="{plink Basket:default}" class="btn btn--secondary btn--big"><i class="icon icon--arrow-left"></i> {_'zpět do košíku'}</a>
      </div>

      <div class="col-xs-12 col-sm-6">
        <button type="submit" id="frm-orderDelModeForm-submit" name="_submit" class="btn btn--buy btn--big">{_'Pokračovat k dodacím údajům'} <i class="icon icon--arrow-right"></i></button>
      </div>

    </div>

  {/form}

</div>

{/block}
