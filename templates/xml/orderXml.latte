<?xml version="1.0" encoding="UTF-8"?>
<order number="{$order->ordcode}" date="{$order->orddatec|date:'Y-m-d'}">
  <customer>
    <company>{$order->ordifirname}</company>
    <name>{$order->ordiname} {$order->ordilname}</name>
    <street>{$order->ordistreet} {$order->ordistreetno}</street>
    <city>{$order->ordicity}</city>
    <psc>{$order->ordipostcode}</psc>
    <ico>{$order->ordic}</ico>
    <dic>{$order->orddic}</dic>
    <tel>{$order->ordtel}</tel>
    <email>{$order->ordmail}</email>
    <note>{$order->ordnote}</note>
    {if !empty($order->ordstlname) OR !empty($order->ordstfirname)}
    <consignee>
      <company>{$order->ordstfirname}</company>
      <name>{$order->ordstname} {$order->ordstlname}</name>
      <street>{$order->ordststreet} {$order->ordststreetno}</street>
      <city>{$order->ordstcity}</city>
      <psc>{$order->ordstpostcode}</psc>
    </consignee>
    {/if}
  </customer>
  {foreach $ordItems as $row}
  <?php
    $rateVAT = 'high';
    if ((int)$row->orivatid === 1) $rateVAT = 'low';
    if ((int)$row->orivatid === 2) $rateVAT = 'third';
    if ((int)$row->orivatid === 3) $rateVAT = 'none';

    if ((int)$row->oritypid === 1) {
      $proCode = 'DOPRAVNE';
      $proCodeDel = '';
      $proCodePay = '';

      if ($delMode->delcode === 'OSOBNE') {
        $proCodeDel = 1;
      }
      if ($payMode->delcode === 'creditcard') {
        $proCodePay = 13;
      }
      if ($payMode->delcode === 'paybefore') {
        $proCodePay = 12;
      }
      if ($payMode->delcode === 'cash') {
        $proCodePay = 2;
      }

    } else {
      $proCode = (string)$row->procode;
    }
  ?>
  <orderItem {if $row->oritypid === 1}delid="{$proCodeDel}" payid="{$proCodePay}" {/if}code="{$proCode}" quantity="{$row->oriqty}" unit="ks" rateVAT="{$rateVAT}" priceVAT="{$row->oriprice}">{$row->oriname}</orderItem>
  {/foreach}
</order>
