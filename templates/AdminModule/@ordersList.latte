<script>
    function noteSH(id) {
        if ($('#'+id).is(':visible')) {
            $('#'+id).hide();
        } else {
            $('#'+id).show();
        }
        return false;
    }
</script>
<form method="get" action="{plink Order:batchAction}">
 <input type="checkbox" id="checkAll"> Zaškrtnout/odškrtnou vše
<table class="table table-condensed table-hover table-bordered">
  <tr>
    <th> </th>
    <th>Č. obj.</th>
    <th>Zákazník</th>
    <th>Firma</th>
    <th>Datum</th>
    <th>Cena s DPH</th>
    <th>Doprava, platba</th>
    <th>Pozn.</th>
    <th>Status</th>
    <th colspan="3"></th>
  </tr>
  <?php
    $colors[0] = "#FFFFFF"; //ceka na zpracovani
    $colors[1] = "#FFFFC0"; //Vyřizuje se
    $colors[2] = "#FFBE7D"; //Čeká na platbu
    $colors[3] = "#C0C0FF"; //Odeslána
    $colors[4] = "#C0FFC0"; //Uzavřená
    $colors[5] = "#FFC0C0"; //Stornovaná
    $colors[6] = "#800080"; //Zaplaceno
    $colors[7] = "#C0C0C0"; //cerna listina
    $colors[8] = "#BCDCB8"; //připraveno k odběru

    $sum = 0;
  ?>

  {foreach $dataRows as $row}
  <?php
    $sum += $row->ordpricevat;
  ?>
  {var $style= (!$iterator->isOdd() ? 'bgcolor="#D0D0D0"' : '')}

  <tr {$style}>
    <td><input class="ordid_chk" type="checkbox" name="ordid[{$row->ordid}]" value="{$row->ordid}" ></td>
    <td>{$row->ordcode}</td>
    <td>{$row->ordiname} {$row->ordilname}|{$row->ordstname} {$row->ordstlname}</td>
    <td>{$row->ordifirname}|{$row->ordstfirname}</td>
    <td>{$row->orddatec|date:'d.m.Y H:i'}</td>
    <td style="text-align: right;white-space:nowrap">{$row->ordpricevat|formatPriceByCurId:$row->ordcurid}</td>
    <td>{$row->delnamemas}, {$row->delname}</td>
    <td>{!$row->ordnote|nl2br}</td>
    <td style="color: black; background-color: {!$colors[$row->ordstatus]};">
    {foreach $enum_ordstatus as $key => $text}
      {if $iterator->isFirst()}
      <select name="ordstatus[{$row->ordid}]">
      {/if}
      <option value="{$key}" {if $key==$row->ordstatus} selected="selected"{/if}>{$text}</option>
      {if $iterator->isLast()}
      </select>
      {/if}
    {/foreach}
    </td>
    <td><a href="{plink Order:edit, $row->ordid}">{!$template->glyph('edit')}</a></td>
    <td>{if $row->ordusrid > 0}<a href="{plink User:edit, $row->ordusrid}">{!$template->glyph('user')}</a>{/if}</td>
    <td><a href="{plink Order:exportPohodaXph, $row->ordid}">{!$template->glyph('export')}</a></td>
  </tr>
  {/foreach}


  <tr>
    <td colspan="6" ><strong>Celkem:</strong></td>
    <td style="text-align: right;"><strong>{$sum|formatPrice}</strong></td>
    <td colspan="9" ></td>
  </tr>
  </table>
  <input type="submit" class="btn btn-primary btn-sm" name="change_status" value="Aktualizovat status">
  <input type="submit" class="btn btn-primary btn-sm" name="send_wedo" value="Export do WE|DO">
  </form>
  <script type="">

  $("#checkAll").click(function(){
    $('.ordid_chk').not(this).prop('checked', this.checked);
});
  </script>
