{var title = 'Editace mailování'}

{block #content}
  <script>
  $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2,
      select: function( event, ui ) {
        if (ui.item) {
          var id = this.id;
          var newId = "";
          this.value = ui.item.value;
          newId = id.replace('proname', 'proid');
          $("#"+newId).val(ui.item.id);
        }
        return false;
      }
    });
  });
  </script>

  {if $id>0}<p><a href="{plink :Front:Mailing:detail, $id, 1, '3907c2fb8de27f8858c0a9c1e362adaf'}" class="btn btn-primary btn-sm">Náhled</a></p>{/if}
  {form editForm}
  {* vypise chyby formulare *}
  {include ../@formErrors.latte form=>$form}

  <table>
  <tr><th>{label mamdate /}</th><td>{input mamdate class=>'form-control'} <small><PERSON>adejte ve formátu dd.mm.rrrr</small></td></tr>
  <tr class="required"><th>{label mamsubject /}</th><td>{input mamsubject class=>'form-control'}</td></tr>
  <tr class="required"><th>{label mambody /}</th><td>{input mambody class=>'form-control'}</td></tr>
  <tr class="required"><th></th><td>{input mammaillist class=>'form-control'} {label mammaillist /}</td></tr>
  <tr class="required"><th>Cenové hladiny:</th>
    <td>
      {input mampricea class=>'form-control'} {label mampricea /}<br>
      {input mampriceb class=>'form-control'} {label mampriceb /}<br>
      {input mampricec class=>'form-control'} {label mampricec /}<br>
      {input mampriced class=>'form-control'} {label mampriced /}
    </td>
  </tr>
  <tr><th colspan="2">
  <p>Produkty <br><small>vyplňte tak, aby každý blok (označený stejnou barvou) byl celý zaplněný produkty.</small></p>
  <table class="table table-condensed table-hover table-bordered">
    <?php for($i=0;$i<=11;$i++){
    if ($i % 2 == 0) {
      $color = "#C0FFC0";
    } else {
      $color = "#9FA5EC";
    }
    ?>
    <tr style="background: {!$color};">
      <td>{$i+1}.</td>
      <td>Id: <?php echo $form["products"][$i]['proid']->control->readonly('readonly')->size(5) ?></td>
      <td>
        Název: <?php echo $form["products"][$i]['proname']->control->size(70) ?> Dodatek: <?php echo $form["products"][$i]['prodesc1']->control->size(61) ?><br>
        Popis: <?php echo $form["products"][$i]['prodesc2']->control->size(150) ?>
      </td>
    </tr>
  <?php
    }
  ?>
  </table>
  </th></tr>
  <tr class="required"><th>{label mamfooter /}</th><td>{input mamfooter class=>'form-control'}</td></tr>
  <tr><th>{label mamstatus /}</th><td>{input mamstatus class=>'form-control'}</td></tr>

  </table>
  {input save class=>"btn btn-primary"}

  {/form}

  {if $id>0}
  <h3>Testovací mailování</h3>
  {control mailTestForm}
  {/if}
{/block}
