{var title = 'Definice parametrů zboží'}

{block #content}
 <p><a href="{plink edit 0}" class="btn btn-primary btn-sm">Nový parametr</a></p>

<div class="panel panel-primary panel-custom">

  <div class="panel-heading"><strong>Filtrace</strong></div>

  <div class="panel-body">

  {form searchForm class=>'form-inline'}
    <div class="input-group">
      <span class="input-group-addon">{label name /}</span>
      {input name class=>'form-control'}
    </div>

    <div class="input-group">
      <span class="input-group-addon">{label catid /}</span>
      {input catid class=>'form-control'}
    </div>

      <div class="input-group">
      <span class="input-group-addon">{label status /}</span>
      {input status class=>'form-control'}
    </div>

    {input search class=>"btn btn-success"}
    {input clear class=>"btn btn-default"}

  {/form}

  </div>

</div>

  <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>Název</th>
    <th>Katalog</th>
    <th>Filtrovat</th>
    <th>Status</th>
    <th colspan="2"></th>
  </tr>
  {foreach $dataRows as $row}

    <tr>
      <td>{$row->prdname}</td>
      <td>{$row->catname}</td>
      <td>{if $row->prdsearch == 1}ANO{else}NE{/if}</td>
      <td>{$enum_prdstatus[$row->prdstatus]}</td>
      <td><a href="{plink edit, $row->prdid}">{!$template->glyph('edit')}</a></td>
      <td><a href="{plink delete, $row->prdid}">{!$template->glyph('delete')}</a></td>
    </tr>
  {/foreach}
  </table>
  {* strankovani *}
  {control paginator}
{/block}
