{var title = 'Slevové kupóny'}

{block #content}
 <p><a href="{plink edit 0}">Nový slevový kupón</a></p>

<div class="panel panel-primary panel-custom">

  <div class="panel-heading"><strong>Filtrace</strong></div>

  <div class="panel-body">

  {form searchForm class=>'form-inline'}
    <div class="input-group">
      <span class="input-group-addon">{label code /}</span>
      {input code class=>'form-control'}
    </div>

    <div class="input-group">
      <span class="input-group-addon">{label mail /}</span>
      {input mail class=>'form-control'}
    </div>

    <div class="input-group">
      <span class="input-group-addon">{label status /}</span>
      {input status class=>'form-control'}
    </div>

    {input search class=>"btn btn-success"}
    {input clear class=>"btn btn-default"}

  {/form}

  </div>

</div>

  <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>Kód</th>
    <th>Email</th>
    <th>Platba zdarma</th>
    <th>Doprava zdarma</th>
    <th>Produkty</th>
    <th>Hodnota</th>
    <th>Platnost</th>
    <th>Počet použití</th>
    <th>Status</th>
    <th colspan="3"></th>
  </tr>
  {foreach $dataRows as $row}
    <?php
    $style = "";
    if ($row->coustatus == 0) {
    } else if ($row->coustatus == 1) {
      $style = "silver";
    }
    ?>
    <tr {if !empty($style)} style="background-color: {!$style}"{/if}>
      <td>{$row->coucode}</td>
      <td>{$row->coumail}</td>
      <td>{if $row->coupayfree == 1}ANO{else}NE{/if}</td>
      <td>{if $row->coudelfree == 1}ANO{else}NE{/if}</td>
      <td>{$row->couproducts}</td>
      <td>{if $row->couvalue > 0}{$row->couvalue|formatPrice}{/if}</td>
      <td>{$row->couvalidto|date:'d.m.Y'}</td>
    <td>{$row->coucounter}</td>
      <td>{$enum_coustatus[$row->coustatus]}</td>
      <td><a href="{plink edit, $row->couid}">{!$template->glyph('edit')}</a></td>
      <td><a href="{plink Order:default, 'sCoupon'=>$row->coucode}">{!$template->glyph('info', "Vypsat objednávky s tímto kupónem")}</a></td>
      <td><a href="{plink stats, $row->couid}">{!$template->glyph('stats')}</a></td>
    </tr>
  {/foreach}
  </table>
  {* strankovani *}
  {control paginator}
{/block}
