{var title = 'Seznam zboží'}

{block #content}
  {foreach $prosNotInCatalog as $row}
    {if $iterator->isFirst()}
    <strong style="color: red;">Položky které nejsou zařazeny v katalogu</strong><br>
    {/if}
    <a href="{plink Product:edit, $row->proid}">{$row->proname}</a> <a href="{plink Product:edit, $row->proid}">{!$template->glyph('edit')}<br>
    {if $iterator->isLast()}
    <br>
    {/if}
  {/foreach}

  <div class="panel panel-primary panel-custom">

    <div class="panel-heading"><strong>Filtrace</strong></div>

    <div class="panel-body">

    {form searchForm class=>'form-inline'}

      <div class="input-group">
        <span class="input-group-addon">{label code /}</span>
        {input code class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label code2 /}</span>
        {input code2 class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label name /}</span>
        {input name class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label manid /}</span>
        {input manid class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label catid /}</span>
        {input catid class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label status /}</span>
        {input status class=>'form-control'}
      </div>

      <div class="input-group">
        <span class="input-group-addon">{label orderby /}</span>
        {input orderby class=>'form-control'}
      </div>
       <br>
      {label typid class=>"checkbox-inline"} {input typid} {/label}
      {label typid2 class=>"checkbox-inline"} {input typid2} {/label}
      {label typid3 class=>"checkbox-inline"} {input typid3} {/label}
      {label typid4 class=>"checkbox-inline"} {input typid4} {/label}
      {label typid5 class=>"checkbox-inline"} {input typid5} {/label}

      {input search class=>"btn btn-success"}
      {input clear class=>"btn btn-default"}

    {/form}

    </div>

  </div>

 {control paginator}
  {snippet table-data}
{form listEditForm}
 <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>ID.</th>
    <th>Kat. č.</th>
    <th>EAN</th>
    <th>Název</th>
    <th>Výrobce</th>
    <th>Cena</th>
    <th>Dostupnost</th>
    <th>Prodáno</th>
    <th>Pořadí</th>
    <th>Status</th>
    <th colspan="3"></th>
  </tr>
  {foreach $dataRows as $row}
    {var $container = $form[$row->proid]}
    <?php
    $tabindex = 100 + $iterator->getCounter();
    ?>
    <tr{if !$iterator->isOdd()} bgcolor="#D0D0D0"{/if}>
      <td>{$row->proid}</td>
      <td>{$row->procode}</td>
      <td style="text-align: left">EAN:{$row->procode2}<br><?php echo $container["procode2"]->getControl()->addAttributes(array('size'=>13,'tabindex'=>$tabindex)) ?></td>
      <td style="{if $row->proismaster==1}background-color:#C0C0FF{elseif (int)$row->promasid > 0}background-color:#FCB633{/if}">{$row->proname} {$row->proname2}</td>
      <td>{$row->manname}</td>
      <td>{$row->proprice1a|formatPriceByCurId:1}</td>
      <td><?php echo $container["proaccess"]->getControl()->addAttributes(array('size'=>1)) ?></td>
      <td>{$row->prscnt} ks</td>
      <td>{$row->proorder}<?php echo $container["proorder"]->getControl() ?></td>
      <td><?php echo $container["prostatuschange"]->getControl()->addAttributes(array('title'=>'změnit status')) ?> {if $row->prostatus == 0} {!$template->glyph('active')} {else} {!$template->glyph('blocked')} {/if}</td>
      <td><a href="{plink Product:edit, $row->proid}">{!$template->glyph('edit')}</a></td>
      <td><a href="{plink :Front:Product:detail, $row->proid, $template->getProKey($row)}">{!$template->glyph('front')}</a></td>
      <td><a href="{plink Product:delete, $row->proid}" onclick="return DeleteConfirm('položku {!$row->proname}');">{!$template->glyph('delete')}</a></td>
    </tr>
  {/foreach}
  </table>
  {input save class=>"btn btn-primary"}
  {/form listEditForm}
    {/snippet}
  {control paginator}
{/block}
