{var title = ($dataRow ? $dataRow->proname . ' (ID:'.$dataRow->proid.')' :'Nová položka')}

{block #content}
  <script>
  $(function() {
    $( ".autocomplete" ).autocomplete({
      source: {plink 'autocompleteProducts'},
      minLength: 2
    });
  });

  window.onload = function() {
    var url = document.location.toString();

    if (url.match('#')) {
      $('.nav-tabs a[href=#' + url.split('#')[1] + ']').tab('show');
    }
  }
  </script>


  {default $dataRow = false}
  {var $tab = $presenter->getParam('tab')}

  {if $dataRow}
  <p>
  <a href="{plink :Front:Product:detail, $dataRow->proid, $template->getProKey($dataRow)}" class="btn btn-primary btn-sm">Detail zboží ve ve<PERSON>ej<PERSON></a>
  </p>
  {/if}
  <script type="text/javascript">

    $(document).ready(function(){
      var whatTab = window.location.hash;
      activateTab( whatTab );
    });

    function activateTab(tab){
      $('.nav-tabs a[href="' + tab + '"]').tab('show');
    };

  </script>

  <div>
    <ul class="nav nav-tabs">
     <li class="active"><a href="#tabs_editmain" data-toggle="tab">Základní údaje</a></li>
     <li><a href="#tabs_editcatalog" data-toggle="tab">Zařazení do katalogu</a></li>
     <li><a href="#tabs_editdesc" data-toggle="tab">Dlouhý popis</a></li>
     <li><a href="#tabs_seo" data-toggle="tab">SEO</a></li>
     <li><a href="#tabs_editrem" data-toggle="tab">Rozšířené údaje</a></li>
     <li><a href="#tabs_accessories" data-toggle="tab">Příslušenství</a></li>
     <li><a href="#tabs_pic" data-toggle="tab">Obrázky</a></li>
     <li><a href="#tabs_attachment" data-toggle="tab">Přílohy</a></li>
     {if $id > 0}<li><a href="#tabs_param" data-toggle="tab">Parametry</a></li>{/if}
     {if $identity->isAllowed('Admin:ProductCA', 'ActionPrices')}<li><a href="#product_prices" data-toggle="tab">Cenové akce</a></li>{/if}
     {if $subItems}<li><a href="#tabs_subitems" data-toggle="tab">Podřízené položky</a></li>{/if}
    </ul>
  <div class="tab-content">

  {form productEditForm class=>"form-horizontal"}
    {php $form->render('errors') }
    {* Základní údaje *}
    <div id="tabs_editmain" class="tab-pane fade in active">

      <div class="form-group">
        <div class="col-sm-2 control-label">{label procatrootid /}</div>

        <div class="col-sm-10">{input procatrootid}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label procode /}</div>

        <div class="col-sm-10">{input procode}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label procode2 /}</div>

        <div class="col-sm-10">{input procode2}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label mastercode /}</div>

        <div class="col-sm-10">{input mastercode}</div>
      </div>

      <div class="form-group required">
        <div class="col-sm-2 control-label">{label proname class=>"required" /}</div>

        <div class="col-sm-10">{input proname}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label proname2 /} </div>

        <div class="col-sm-10">{input proname2}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label pronames /} </div>

        <div class="col-sm-10">{input pronames}</div>
      </div>

      <div class="form-group required">
        <div class="col-sm-2 control-label">{label promanid class=>"required"}Výrobce:{/label} </div>

        <div class="col-sm-10">{input promanid}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>

        <div class="col-sm-10"><div class="checkbox">{label protypid}{input protypid}{/label} </div></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>

        <div class="col-sm-10"><div class="checkbox">{label protypid2}{input protypid2}{/label} </div></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>

        <div class="col-sm-10"><div class="checkbox">{label protypid3}{input protypid3}{/label} </div></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>

        <div class="col-sm-10"><div class="checkbox">{label protypid4}{input protypid4}{/label}</div></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>

        <div class="col-sm-10"><div class="checkbox">{label protypid5}{input protypid5}{/label}</div></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label prorating /}</div>

        <div class="col-sm-10">{input prorating}</div>
      </div>

      {if $identity->isAllowed('Admin:ProductCA', 'gifts')}
      <div class="form-group">
        <div class="col-sm-2 control-label">{label progifts /}</div>

        <div class="col-sm-10">{input progifts} <span class="help-block">Zadejte výčet kódů zboží oddělené čárkou</span></div>
      </div>
      {/if}

      <div class="form-group">
        <div class="col-sm-2 control-label">{label prodescs /} </div>

        <div class="col-sm-10">{input prodescs}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label proprice1com /} </div>

        <div class="col-sm-10">{input proprice1com}</div>
      </div>

      <div class="form-group required">
        <div class="col-sm-2 control-label">{label proprice1a class=>"required" /} </div>

        <div class="col-sm-10">{input proprice1a}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label proprice1b class=>"required" /} </div>

        <div class="col-sm-10">{input proprice1b}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label proprice1c class=>"required" /} </div>

        <div class="col-sm-10">{input proprice1c}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label proprice1d class=>"required" /} </div>

        <div class="col-sm-10">{input proprice1d}</div>
      </div>
      {if $secondCurrency}
      <div class="form-group">
        <div class="col-sm-2 control-label">{label proprice2com /} </div>

        <div class="col-sm-10">{input proprice2com}</div>
      </div>

      <div class="form-group required">
        <div class="col-sm-2 control-label">{label proprice2a class=>"required" /} </div>

        <div class="col-sm-10">{input proprice2a}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label proprice2b class=>"required" /} </div>

        <div class="col-sm-10">{input proprice2b}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label proprice2c class=>"required" /} </div>

        <div class="col-sm-10">{input proprice2c}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label proprice2d class=>"required" /} </div>

        <div class="col-sm-10">{input proprice2d}</div>
      </div>
      {/if}
      <div class="form-group">
        <div class="col-sm-2 control-label">{label procpcheureka /} </div>

        <div class="col-sm-10">{input procpcheureka}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label procpczbozi /} </div>

        <div class="col-sm-10">{input procpczbozi}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>

        <div class="col-sm-10"><div class="checkbox">{label pronotdisc}{input pronotdisc}{/label}</div></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>

        <div class="col-sm-10"><div class="checkbox">{label prodelfree}{input prodelfree}{/label}</div></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>

        <div class="col-sm-10"><div class="checkbox">{label copyprice}{input copyprice}{/label}</div></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>

        <div class="col-sm-10"><div class="checkbox">{label progoogleoff}{input progoogleoff}{/label}</div></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label provatid /} </div>

        <div class="col-sm-10">{input provatid}</div>
      </div>

      <div class="form-group required">
        <div class="col-sm-2 control-label">{label proaccess class=>"required" /} </div>

        <div class="col-sm-10">{input proaccess}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label proaccesstext /} </div>

        <div class="col-sm-10">{input proaccesstext}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label proqty /} </div>

        <div class="col-sm-10">{input proqty}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label proorder /} </div>

        <div class="col-sm-10">{input proorder} <span class="help-block">Číslo podle kterého je možno řadit zboží v katalogu</span></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label proweight /} </div>

        <div class="col-sm-10">{input proweight} <span class="help-block">Kg</span></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label protrarestr /} </div>

        <div class="col-sm-10">{input protrarestr} <span class="help-block">ID doprav oddělené čárkou které nejsou vhodné pro přepravu položky</span></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label prostatus /} </div>

        <div class="col-sm-10">{input prostatus}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label pronoteint /} </div>

        <div class="col-sm-10">{input pronoteint}</div>
      </div>

      {if $id > 0}
      <div class="form-group">
        <div class="col-sm-2 control-label"></div>
        <div class="col-sm-10"><div class="checkbox">{label watchdog_store}{input watchdog_store}{/label}</div></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>
        <div class="col-sm-10"><div class="checkbox">{label watchdog_price}{input watchdog_price}{/label}</div></div>
      </div>
      {/if}

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>

        <div class="col-sm-10">{input tabs_editmain}</div>
      </div>

      </div>

    {* Zařazení do katalogu *}
    <div id="tabs_editcatalog" class="tab-pane fade">

      <div class="form-group">
        <div class="col-sm-3 control-label"><label>Aktuální zařazení</label></div>
        <div class="col-sm-9">
        {foreach $form["catPlaces"]->controls as $catplace}
          <div class="checkbox">{label $catplace}{input $catplace}{/label}</div>
        {/foreach}
        </div>
      </div>

      <div class="form-group">
        <div class="col-sm-3 control-label">{label cat_0 /} </div>
        <div class="col-sm-9">{input cat_0}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>

        <div class="col-sm-10">{input tabs_editcatalog}</div>
      </div>

    </div>

    {* popis *}
    <div id="tabs_editdesc" class="tab-pane fade">

    <div class="form-group">
      <div class="col-sm-2 control-label">{label prodesc /}</div>

      <div class="col-sm-10">{input prodesc}</div>
    </div>

    <div class="form-group">
      <div class="col-sm-2 control-label"></div>

      <div class="col-sm-10">{input tabs_editdesc}</div>
    </div>

    </div>

    {* SEO *}
    <div id="tabs_seo" class="tab-pane fade">

      <div class="form-group">
        <div class="col-sm-2 control-label">{label prokey /}</div>

        <div class="col-sm-10">{input prokey} <span class="help-block">Pokud ponecháte prázdné, generuje se z názvu zboží</span></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label protitle /}</div>

        <div class="col-sm-10">{input protitle} <span class="help-block">Zadávejte pokud chcete jiné TITLE nez je název zboží</span></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label prokeywords /}</div>

        <div class="col-sm-10">{input prokeywords} <span class="help-block">Zadávejte výčet klíčových slov, které nejsou v názvu zboží oddělený čárkou</span></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label prodescription /}</div>

        <div class="col-sm-10">{input prodescription} <p class="charsRemaining"></p></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>

        <div class="col-sm-10">{input tabs_seo}</div>
      </div>

      </div>

    {* rozšířené údaje *}
    <div id="tabs_editrem" class="tab-pane fade">

      <div class="form-group">
        <div class="col-sm-2 control-label">{label provideo /}</div>

        <div class="col-sm-10">{input provideo}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label proico /}</div>

        <div class="col-sm-10">{input proico}</div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label prooptionskeywords /}</div>

        <div class="col-sm-10">{input prooptionskeywords} <span class="help-block">Zadávejte začátky názvu souvisejících položek oddělené čárkou</span></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label">{label prowarranty /}</div>

        <div class="col-sm-10">{input prowarranty} <span class="help-block">Textově délka záruky, např. "24 mesíců"</span></div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>

        <div class="col-sm-10">{input tabs_editrem}</div>
      </div>

    </div>

    {* parametry *}
    {if $id>0}
    <div id="tabs_param" class="tab-pane fade">
      <div class="panel panel-success panel-custom form-inline">

        <div class="panel-heading"><h3 class="panel-title">Nový parametr</h3></div>

        <div class="panel-body">

            <div class="input-group">
                <span class="input-group-addon">{label param_name_0}Název:{/label}</span>
                {input param_name_0}{input param_prdid_0}
            </div>

            <div class="input-group">
                <span class="input-group-addon">{label param_value_0}Hodnota{/label}</span>
                {input param_value_0}
            </div>

        </div>

    </div>

    <table  class="table table-condensed table-hover table-bordered">
       {if !empty($form["params_edit"])}
         <tr>
           <th>Parametr</th>
           <th>Hodnota</th>
           <th></th>
         </tr>
       {foreach $form["params_edit"]->controls as $key => $component}
         {if $key == 'param_prdid'}
         <tr>
          <td>
            Systémový parametr (v hodnotě neuvádějte jednotku)
            {input $component}
           <br>
         {/if}
         {if $key == 'param_name'}
            Název parametru textově
            {input $component}
           </td>
         {/if}
         {if $key == 'param_value'}
          <td>{input $component}</td>
          <td><a href="{plink 'deleteParam', $id, (int)$component->parent->name}"> vymazat </a></td>
          </tr>
         {/if}
       {/foreach}
       {/if}
     </table>
     <div class="form-group">
        <div class="col-sm-2 control-label"></div>

        <div class="col-sm-10">{input tabs_param}</div>
      </div>
    </div>
    {/if}

    {* cenové akce *}
    {if !empty($form["product_prices"])}
    <div id="product_prices" class="tab-pane fade">
      <div class="panel panel-success panel-custom form-inline">

        <div class="panel-heading"><h3 class="panel-title">Nová cenová akce</h3></div>

        <div class="panel-body">
        {formContainer product_prices}
          {formContainer 0}
          <div class="input-group">
              <span class="input-group-addon">{label pradatefrom /}</span>
              {input pradatefrom}
          </div>

          <div class="input-group">
              <span class="input-group-addon">{label pradateto /}</span>
              {input pradateto}
          </div>

          <div class="input-group">
              <span class="input-group-addon">{label praprccat /}</span>
              {input praprccat}
          </div>
          <div class="input-group">
              <span class="input-group-addon">{label praprice /}</span>
              {input praprice}
          </div>
          <div class="input-group">
            {label pratypid} {input pratypid}{/label}
            {label pratypid2} {input pratypid2}{/label}
            {label pratypid3} {input pratypid3}{/label}
            {label pratypid4} {input pratypid4}{/label}
            {label pratypid5} {input pratypid5}{/label}
          </div>
          {/formContainer}
        {/formContainer}
        </div>

      </div>

      <div class="panel panel-success panel-custom form-inline">

        <div class="panel-heading"><h3 class="panel-title">Platné cenová akce</h3></div>

        <div class="panel-body">
        {formContainer product_prices}
        {foreach $form["product_prices"]->getComponents(FALSE, '\Nette\Forms\Container') as $key => $component}
          {if $key != 0}
          {formContainer $key}
          <div class="input-group">
              <span class="input-group-addon">{label pradatefrom /}</span>
              {input pradatefrom}
          </div>

          <div class="input-group">
              <span class="input-group-addon">{label pradateto /}</span>
              {input pradateto}
          </div>

          <div class="input-group">
              <span class="input-group-addon">{label praprccat /}</span>
              {input praprccat}
          </div>

          <div class="input-group">
              <span class="input-group-addon">{label praprice /}</span>
              {input praprice}
          </div>

          <div class="input-group">
              {input prastatus}
          </div>
          <div class="input-group">
            {label pratypid} {input pratypid}{/label}
            {label pratypid2} {input pratypid2}{/label}
            {label pratypid3} {input pratypid3}{/label}
            {label pratypid4} {input pratypid4}{/label}
            {label pratypid5} {input pratypid5}{/label}
          </div>
          {/formContainer}
          <a href="{plink deleteProductPrice $id, $key}">{|glyph('delete')|noescape}</a><br>
          {/if}
        {/foreach}
        {/formContainer}
        </div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>

        <div class="col-sm-10">{input tabs_product_prices}</div>
      </div>

    {/if}
    </div>



    {* obrázky *}
    <div id="tabs_pic" class="tab-pane fade">

    {if isset($proSamePicNames)}
      {foreach $proSamePicNames as $row}
       {if $iterator->isFirst()}
       <p><strong>Nalezena duplicita názvů obrázků:</strong><br />
       {/if}
       <a href="{plink 'edit', $row->proid}">{$row->procode} {$row->proname}</a><br />
       {if $iterator->isLast()}
       </p>
       {/if}
      {/foreach}
   {/if}
   {* vypisu obrazky *}
   {if isset($images)}
   {foreach $images as $pos => $image}
     {if ($pos != 11)}
     <img title="{$image["title"]}" src="{$baseUri}/pic/product/list/{$image["filename"]}?{time()}" />
     {else}
     <img title="{$image["title"]}" src="{$baseUri}/pic/product/slider/{$image["filename"]}?{time()}" />
     {/if}
     {if ($pos != 0 && $pos != 11)} <a href="{plink 'deleteImage', $id, $image["filename"]}"><img src="{$baseUri}/admin/ico/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat {$image["title"]}" /></a>{/if}
   {/foreach}
   {/if}

  <div class="form-group">
    <div class="col-sm-2 control-label">{label propicname /}</div>

    <div class="col-sm-10">{input propicname} <span class="help-block">.jpg</span></div>
  </div>

  <div class="form-group">
    <div class="col-sm-2 control-label">{label pic0 /}</div>

    <div class="col-sm-10">{input pic0} <span class="help-block">Nahráním nového obrázku ten původní přepíšete. Je třeba dodržet poměr stran obrázku jinak bude obrázek oříznutý.</span></div>
  </div>

  <div class="form-group">
    <div class="col-sm-2 control-label">{label pic1 /}</div>

    <div class="col-sm-10">{input pic1}</div>
  </div>

  <div class="form-group">
    <div class="col-sm-2 control-label">{label pic1position /}</div>

    <div class="col-sm-10">{input pic1position} <span class="help-block">Nahráním nového obrázku na obsazenou pozici ten původní přepíšete. Je třeba dodržet poměr stran obrázku jinak bude obrázek oříznutý.</span></div>
  </div>

  <div class="form-group">
    <div class="col-sm-2 control-label">{label pic_usrsize /}</div>

    <div class="col-sm-10">{input pic_usrsize} <span class="help-block">Nahráním nového obrázku ten původní přepíšete. Je třeba dodržet poměr stran obrázku 40x40px jinak bude obrázek oříznutý.</span></div>
  </div>

  <div class="form-group">
    <div class="col-sm-2 control-label"></div>

    <div class="col-sm-10">{input tabs_pic}</div>
  </div>


    </div>
    {* přílohy  *}
    <div id="tabs_attachment" class="tab-pane fade">
    {if isset($attachments)}
     {* vypisu priloh *}
     {foreach $attachments as $row}
       <a href="{$baseUri}/files/{$row["atafilename"]}?{time()}">{$row["ataname"]}</a>
       <a href="{plink 'deleteFile', $row["ataid"], $id}"><img src="{$baseUri}/img/admin/delete.png" height="16" width="16" alt="Vymazat" title="Vymazat {$row["ataname"]}" /></a><br />
     {/foreach}
     {/if}
     <div class="form-group">
       <div class="col-sm-2 control-label">{label attAdd /}</div>
       <div class="col-sm-10">{input attAdd}</div>
     </div>
     <div class="form-group">
       <div class="col-sm-2 control-label">{label ataname /}</div>
       <div class="col-sm-10">{input ataname}</div>
     </div>
     <div class="form-group">
       <div class="col-sm-2 control-label"></div>
       <div class="col-sm-10">{input tabs_attachment}</div>
     </div>

    </div>

    <div id="tabs_accessories" class="tab-pane fade">
      <div class="form-group">
        <div class="col-sm-3 control-label">Aktuální příslušenství:</div>
        <div class="col-sm-9">
        {foreach $form["accessories"]->controls as $key => $item}
          {input $item}
        {/foreach}
        </div>
      </div>
      <div class="form-group">
        <div class="col-sm-3 control-label">Přidat příslušenství:</div>
        <div class="col-sm-9">
        {foreach $form["accessories_new"]->controls as $key => $item}
            {input $item}
        {/foreach}
        </div>
      </div>

      <div class="form-group">
        <div class="col-sm-2 control-label"></div>
        <div class="col-sm-10">{input tabs_accessories}</div>
      </div>
    </div>

  </div>

    {if $subItems}
     <div role="tabpanel" class="tab-pane" id="tabs_subitems">
     <h3>Podřízené položky</h3>
     <table class="table table-condensed table-hover table-bordered">
      <tr>
        <th>Katalogové č.</th>
        <th>Název</th>
        <th>Cena</th>
        <th>Dostupnost</th>
        <th>Status</th>
        <th colspan="2"></th>
      </tr>
      {foreach $subItems as $row}
        <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
          <td>{$row->procode}</td>
          <td>{$row->proname}</td>
          <td>{$row->proprice1a|formatPrice}</td>
          <td>{$enum_proaccess[$row->proaccess]} {if (int)$row->proqty > 0} ({$row->proqty}ks){/if}</td>
          <td>{$enum_prostatus[$row->prostatus]}</td>
          <td><a href="{plink Product:edit, $row->proid}">{!$template->glyph('edit')}</a></td>
          <td><a href="{plink :Front:Product:detail, $row->proid, $template->getProKey($row)}">{!$template->glyph('front')}</td>
        </tr>
      {/foreach}
      </table>
     </div>
   {/if}

     <div class="form-group">
       <div class="col-sm-6">{input save}</div>
       <div class="col-sm-6">{if $id > 0}{input saveAsNew}{/if}</div>
     </div>

   {/form}


   </div>
</div>
{/block}
