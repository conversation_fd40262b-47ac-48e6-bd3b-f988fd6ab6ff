{if $id > 0}
{if $dataRow->delmasid == 0}
{var title = 'Editace způsobu dopravy'}
{var serviceName = 'Doprava'}
{else}
{var title = 'Editace způsobu platby'}
{var serviceName = 'Platba'}
{/if}
{else}
  {ifset $delMas}
    {var title = 'Nový způsob platby pro '.$delMas->delname}
    {var serviceName = 'Platba'}
  {else}Nová doprava
    {var title = 'Nová doprava'}
    {var serviceName = 'Doprava'}
  {/ifset}
{/if}

{block #content}

  {control editForm}

  <h3 id="freedelivery">{$serviceName} ZDARMA</h3>
  {form editDelFreeForm}
    <table class="table table-condensed table-hover table-bordered">
      <tr>
        {if $secondCurrency}<th>Měna</th>{/if}
        <th>Cenová kategorie</th>
        <th>Cena objednávky od</th>
        <th>Stav</th>
        <th colspan="2"></th>
      </tr>
      {foreach  $form["items"]->getComponents() as $cont}
      {var $disid=$cont->name}
      {if $disid == 0}
      <tr>
        <th colspan="6">Nová položka</th>
      </tr>
      {/if}
      <tr {if !$iterator->isOdd()}bgcolor="#D0D0D0"{/if}>
        {if $secondCurrency}<td><?php echo $form["items"][$cont->name]['discurid']->control ?></td>{/if}
        <td><?php echo $form["items"][$cont->name]['disprccat']->control ?></td>
        <td><?php echo $form["items"][$cont->name]['disfrom']->control ?></td>
        <td><?php echo $form["items"][$cont->name]['disstatus']->control ?></td>
        <td>{if $disid > 0}<a href="{plink deleteDelFree, $disid, (int)$presenter->getParam('id')}" onclick="return DeleteConfirm('dopravu zdarma ID: {$disid}');"> {!$template->glyph('delete')}</a>{/if}</td>
      </tr>
      {/foreach}
      <tr>
        <td colspan=6>{input save class=>"btn btn-primary"}</td>
      </tr>
    </table>
  {/form}
{/block}
