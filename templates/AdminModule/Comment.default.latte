{var title = 'Koment<PERSON><PERSON><PERSON>'}

{block #content}

{form searchForm class=>'form-inline'}
<div class="panel panel-primary panel-custom">

    <div class="panel-heading"><strong>Filtrace</strong></div>

    <div class="panel-body">

      <div class="form-group">{label text /}: {input text class=>'form-control'}</div>
      <div class="form-group">{label usrmail /}: {input usrmail class=>'form-control'}</div>
      <div class="form-group">{label procode /}: {input procode class=>'form-control'}</div>
      <div class="form-group">{input search class=>"btn btn-primary"} {input clear class=>"btn"}</div>

    </div>

  </div>
{/form}

  <table class="table table-condensed table-hover table-bordered">
  <tr>
    <th>Id</th>
    <th>Kód produktu</th>
    <th>Datum/čas</th>
    <th>Nick</th>
    <th>Titulek</th>
    <th>Text</th>
    <th colspan="3"></th>
  </tr>
  {foreach $comments as $row}
    <tr>
      <td>{$row->cmtid}</td>
      <td>{$row->procode}</td>
      <td>{$row->cmtdatec|date:'d.m.Y H:i:s'}</td>
      <td>{$row->cmtnick}{if !empty($row->cmtmail)}<br>{$row->cmtmail}{/if}</td>
      <td>{$row->cmtsubj}</td>
      <td>
      <strong>{if $row->cmtcatid > 0}{$enum_cmtcatid[$row->cmtcatid]} |{/if} {if $row->cmtcatid2 > 0}{$enum_cmtcatid[$row->cmtcatid2]}{/if}</strong><br>
      {!$row->cmttext|nl2br}</td>
      <td><a href="{plink Comment:edit, $row->cmtid}" title="editovat komentář">{!$template->glyph('edit')}</a></td>
      <td>{if $row->cmtreid}<a href="{plink Comment:edit, $row->cmtid}" title="editovat zdrojový komentář">{!$template->glyph('export', 'editovat zdrojový komentář')}</a>{/if}</td>
         <td><a href="{plink Comment:delete, $row->cmtid}" onclick="return DeleteConfirm('komentář {!$row->cmtsubj}');">{!$template->glyph('delete')}</a></td>
    </tr>
  {/foreach}
  </table>
  {control paginator}
{/block}
