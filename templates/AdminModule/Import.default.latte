{var title = 'Import'}

{block #content}
  {ifset $log}
  {foreach $log as $row}
    {$row}<br>
  {/foreach}
  {/ifset}

  <h3>Export</h3>
  <p>Pravidla práce s vyexportovaným souborem:</p>
  <ul>
    <li>Ve výsledném exportu nesmíte vymazat první dva řádky a první sloupec (ID).</li>
    <li>Pokud doplníte nový řádek a ve sloupci ID vyplníte 0 bude se tento řádek při importu vkládat jako nová položka</li>
    <li>Před aktualizací dat si vždy vygenerujte aktuální export.</li>
    <li>Jako oddělovač desetin používejte čárku.</li>
  </ul>
  {control exportForm}

  <h3>Import</h3>
  <p>Pravidla pro import:</p>
  <ul>
    <li>Upravte v excelu čí jiném tabulkovém kalkulátoru a před importem uložte jako XLS.</li>
    <li>Pokud se bude program dotazovat na kódování češtiny, zvolte WINDOWS 1250</li>
    <li>Jako oddělovač desetin používejte čárku.</li>
  </ul>
  {control importForm}

{/block}
