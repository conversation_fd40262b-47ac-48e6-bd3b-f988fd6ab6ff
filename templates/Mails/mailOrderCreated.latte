<p><PERSON><PERSON><PERSON><PERSON><PERSON>,<br>
děkujeme Vám za Vaši objednávku na {$presenter->config["SERVER_NAMESHORT"]} s číslem {$orderRow->ordcode}. Objednávka byla právě přijata k vyřízení. O jejím průběhu a odeslání Vás budeme informovat.<br>
<br>
Cena objednávky s DPH: {$orderRow->ordpricevat|formatPrice}
</p>
<p>
<strong>{_'Zvolená doprava'}:</strong> {if $delMode->delid==18}<strong style="color: green;">{$delMode->delname}</strong>{else}{$delMode->delname}{/if} {if $delMode->delcode=='ULOZENKA'} Odběrné místo: {$enum_ulozenka[$orderRow->orddelspec]}{/if}<br />
<strong>{_'Platba'}:</strong> {$payMode->delname}<br />

{if $payMode->delcode == 'paybefore'}
{* udaje pro platbu predem *}
<br />
<strong>Údaje pro platbu předem:</strong><br />
Číslo účtu: {if $orderRow->ordcurid==1}{$presenter->config["SERVER_ACCNO"]}{/if}<br />
Variabilní symbol: {$orderRow->ordcode}<br />
Částka: {$orderRow->ordpricevat|formatPrice}
{/if}

<strong>{_'Poznámka'}:</strong><br />
<strong style="color: red;">{!$orderRow->ordnote|nl2br}</strong><br />
<br />
<strong>{if !empty($orderRow->ordstname)}Fakturační adresa:{else}Fakturační a současně doručovací adresa:{/if}</strong><br />
{_'Jméno'}: {$orderRow->ordiname}<br />
{_'Přijmení'}: {$orderRow->ordilname}<br />
{if $lang=='cs'}{_'Firma'}: {$orderRow->ordifirname}<br />{/if}
{_'Ulice'}: {$orderRow->ordistreet}<br />
{_'Číslo popisné'}: {$orderRow->ordistreetno}<br />
{_'Město, obec'}: {$orderRow->ordicity}<br />
{_'PSČ'}: {$orderRow->ordipostcode}<br />
{_'Telefon'}: {$orderRow->ordtel}<br />
{_'Email'}: <a href="mailto:{$orderRow->ordmail}">{$orderRow->ordmail}</a><br />
IČ: {$orderRow->ordic}, DIČ: {$orderRow->orddic}<br />
{if !empty($orderRow->ordstname)}
<br />
<strong>Dodací adresa:</strong><br />
Jméno, přijmení: {$orderRow->ordstname} {$orderRow->ordstlname}<br />
Firma: {$orderRow->ordstfirname}<br />
Ulice: {$orderRow->ordststreet} {$orderRow->ordststreetno}<br />
Město, obec: {$orderRow->ordstcity}<br />
PSČ: {$orderRow->ordstpostcode}<br />
{/if}

</p>
<strong>{_'Položky objednávky'}:</strong><br />
<table>
<tr>
  <td><strong>Katalogové číslo</strong></td>
  <td><strong>Název</strong></td>
  <td><strong>Kusy</strong></td>
  <td><strong>Sleva</strong></td>
  <td><strong>Cena s DPH</strong></td>
</tr>
<?php
$sum = 0;
?>
{foreach $ordItemRows as $row}
<?php
  $sum += ($row->oriqty*$row->oriprice);
?>

<tr>
  <td>{$row->oriprocode}</td>
  <td>{$row->oriname}</td>
  <td>{$row->oriqty} {_'ks'}</td>
  <td></td>
  <td>{if $row->oriprice != 0}{$row->oriprice|formatPrice}{else}ZDARMA{/if}</td>
</tr>

{/foreach}
</table>
<br />
<table>
<tr>
  <td><strong>{_'Celková cena s DPH'}: </strong></td>
  <td>{$orderRow->ordpricevat|formatPrice}</td>
</tr>
</table>
{include 'mailFooter.latte'}
