
<p>
  <PERSON><PERSON><PERSON><PERSON> den,<br>
  {_'stav va<PERSON><PERSON> o<PERSON> č.'} {$orderRow->ordcode} {_'na'} {$presenter->config["SERVER_NAME"]} {_'byl změněn na'} <strong>{$template->translate($enum_ordStatus[$orderRow->ordstatus])}</strong>.</p>

{if ($orderRow->ordstatus == 3 || $orderRow->ordstatus == 8)  && $delMode->delcode === 'OSOBNE'}
  <p>
  {_'Zboží je připraveno k odběru'}
  {if $delMode->delid ===4}
    na centrále v Praze.
  {elseif $delMode->delid === 7}
    na pobočce v Brně.
  {elseif $delMode->delid === 10}
    na pobočce v Ostravě.
  {else}
    {$delMode->delname}
  {/if}
  </p>
  <p>{$delMode->deltext1|nl2br|noescape}</p>
{/if}
{if $orderRow->ordstatus == 3 && $delMode->delcode !== 'OSOBNE'}
  <p>
    {_'Zboží bylo odesláno přepravní službou'} <strong>{$delMode->delname}</strong>.<br />
  {if !empty($orderRow->ordparcode)} {_'Vaše číslo balíku je'}: {$orderRow->ordparcode}.<br />{/if}
  {if isset($parcelURL)}{_'Balík můžete sledovat'} <a href="{!$parcelURL}">{_'na stránkách přepravce'}</a> .{/if}
  </p>
{/if}
{if $orderRow->ordstatus == 6 && !empty($eetRow->logfik)}
  <p>Na základě Vaší platby uvádíme informace k EET.</p>
  <p>
  <strong>Částka k EET:</strong> {$eetRow->logprice|formatPrice}<br>
  <strong>Evidováno:</strong> {$eetRow->logdatec|date:'d.m.Y H:m:i'}<br>
  <strong>FIK:</strong> {$eetRow->logfik}<br>
  <strong>BKP:</strong> {$eetRow->logbkp}
  </p>
  <p>Toto potvrzení neslouží jako daňový doklad, ten obdržíte po fakturaci.</p>
{/if}

{include 'mailFooter.latte'}
