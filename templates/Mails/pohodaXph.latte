{*
http://www.stormware.cz/eform/eform.xml
*}
<?xml version="1.0" encoding="utf-8"?>
<eform version="1.0">
  <order version="1.1">
    <document number="{$order->ordcode}" date="{$order->orddatec|date:'Y-m-d'}"></document>
    {foreach $ordItems as $row}
    <?php
      $rateVAT = 'high';
      if ((int)$row->orivatid === 1) $rateVAT = 'low';
      if ((int)$row->orivatid === 2) $rateVAT = 'third';
      if ((int)$row->orivatid === 3) $rateVAT = 'none';

      $proCode = (!empty((string)$row->procodep) ? (string)$row->procodep : (string)$row->procode);
      if ((int)$row->oritypid === 1) $proCode = 'DOPR_505';
      if ((int)$row->oritypid === 3) $proCode = 'SLEVA';
    ?>
    <orderItem code="{$proCode}" quantity="{$row->oriqty}" unit="ks" rateVAT="{$rateVAT}" price="{$row->oriprice}">{$row->oriname}</orderItem>
    {/foreach}
    <customer>
      <company>{$order->ordifirname}</company>
      <name>{$order->ordiname} {$order->ordilname}</name>
      <street>{$order->ordistreet} {$order->ordistreetno}</street>
      <city>{$order->ordicity}</city>
      <psc>{$order->ordipostcode}</psc>
      <ico>{$order->ordic}</ico>
      <dic>{$order->orddic}</dic>
      <tel>{$order->ordtel}</tel>
      <fax></fax>
      <email>{$order->ordmail}</email>
      <remark></remark>
      {if !empty($order->ordstlname) OR !empty($order->ordstfirname)}
      <consignee>
        <company>{$order->ordstfirname}</company>
        <name>{$order->ordstname} {$order->ordstlname}</name>
        <street>{$order->ordststreet} {$order->ordststreetno}</street>
        <city>{$order->ordstcity}</city>
        <psc>{$order->ordstpostcode}</psc>
      </consignee>
      {/if}
    </customer>
    {*
    payType enum:
    draft cash postal delivery creditcard advance encashment cheque compensation
    příkazem, hotově, složenkou, dobírka, platební kartou, zálohová faktura, inkasem, šekem, zápočtem
    *}
    {php
      $payType = "delivery";
      if ($payMode->delcode == 'paybefore') {
        $payType = "draft";
      } else if ($payMode->delcode == 'cetelem') {
        $payType = "draft";
      } else if ($payMode->delcode == 'dobirka') {
        $payType = "delivery";
      } else if ($payMode->delcode == 'cash') {
        $payType = "cash";
      } else if ($payMode->delcode == 'creditcard') {
        $payType = "creditcard";
      }
    }
    <payment payType="{$payType}" payVAT="yes"></payment>
  </order>
</eform>
