{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "9f9e562760007ab18687e9b4ba0191bc", "packages": [{"name": "bitbang/http", "version": "v0.2.0", "source": {"type": "git", "url": "https://github.com/bitbang/http.git", "reference": "32586b8d66e847b4b33c404d50771b15369d9828"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bitbang/http/zipball/32586b8d66e847b4b33c404d50771b15369d9828", "reference": "32586b8d66e847b4b33c404d50771b15369d9828", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"nette/tester": "~1.4.0"}, "suggest": {"ext-curl": "Allows you to use Bitbang\\Http\\CurlClient", "kdyby/curl-ca-bundle": "Auto-updated trusted CA Certs bundle. Handy on Windows."}, "type": "library", "autoload": {"classmap": ["src/Http/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Bitbang Contributors", "homepage": "https://github.com/bitbang/http/graphs/contributors"}], "description": "HTTP client library", "time": "2015-11-26T13:43:32+00:00"}, {"name": "dibi/dibi", "version": "v3.0.7", "source": {"type": "git", "url": "https://github.com/dg/dibi.git", "reference": "22e6ea4e404504bb23a7075ed54c64e5d9bfb0e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dg/dibi/zipball/22e6ea4e404504bb23a7075ed54c64e5d9bfb0e2", "reference": "22e6ea4e404504bb23a7075ed54c64e5d9bfb0e2", "shasum": ""}, "require": {"php": ">=5.4.4"}, "replace": {"dg/dibi": "*"}, "require-dev": {"nette/tester": "~1.7", "tracy/tracy": "~2.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"], "files": ["src/loader.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}], "description": "Dibi is Database Abstraction Library for PHP", "homepage": "https://dibiphp.com", "keywords": ["access", "database", "dbal", "mssql", "mysql", "odbc", "oracle", "pdo", "postgresql", "sqlite", "sqlsrv"], "time": "2017-01-04T14:18:17+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.2.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "8d6c6cc55186db87b7dc5009827429ba4e9dc006"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/8d6c6cc55186db87b7dc5009827429ba4e9dc006", "reference": "8d6c6cc55186db87b7dc5009827429ba4e9dc006", "shasum": ""}, "require": {"guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.4", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.0", "psr/log": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2017-02-28T22:50:30+00:00"}, {"name": "guzzlehttp/promises", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "60d379c243457e073cff02bc323a2a86cb355631"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/60d379c243457e073cff02bc323a2a86cb355631", "reference": "60d379c243457e073cff02bc323a2a86cb355631", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2020-09-30T07:37:28+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "53330f47520498c0ae1f61f7e2c90f55690c06a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/53330f47520498c0ae1f61f7e2c90f55690c06a3", "reference": "53330f47520498c0ae1f61f7e2c90f55690c06a3", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2020-09-30T07:37:11+00:00"}, {"name": "ipub/mobile-detect", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/iPublikuj/mobile-detect.git", "reference": "4f002ec4d0e2071685c4abdca07a66058e7b0026"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/iPublikuj/mobile-detect/zipball/4f002ec4d0e2071685c4abdca07a66058e7b0026", "reference": "4f002ec4d0e2071685c4abdca07a66058e7b0026", "shasum": ""}, "require": {"latte/latte": "~2.2", "mobiledetect/mobiledetectlib": "2.8.*", "nette/application": "~2.2", "nette/di": "~2.2", "nette/http": "~2.2", "nette/utils": "~2.2", "php": ">=5.3.0"}, "require-dev": {"janmarek/mockista": "@dev", "nette/bootstrap": "~2.2", "nette/mail": "~2.2", "nette/robot-loader": "~2.2", "nette/safe-stream": "~2.2", "nette/tester": "@dev", "tracy/tracy": "@dev"}, "type": "library", "autoload": {"psr-0": {"IPub\\MobileDetect\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "iPublikuj:cms", "email": "<EMAIL>", "homepage": "http://www.ipublikuj.eu/"}], "description": "Extension for detecting mobile devices, managing mobile view types, redirect to mobile version for Nette Framework", "homepage": "https://github.com/iPublikuj/mobile-detect", "keywords": ["framework", "ipub", "ipublikuj", "mobile", "mobile detect", "mobile redirect", "mobile view managing", "nette", "tools"], "abandoned": true, "time": "2015-06-29T13:13:10+00:00"}, {"name": "ipub/visual-paginator", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/ipublikuj-ui/visual-paginator.git", "reference": "26ba98199a7d6d6a61072fc8323176e6cec39dbe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ipublikuj-ui/visual-paginator/zipball/26ba98199a7d6d6a61072fc8323176e6cec39dbe", "reference": "26ba98199a7d6d6a61072fc8323176e6cec39dbe", "shasum": ""}, "require": {"latte/latte": "~2.2", "nette/application": "~2.2", "nette/di": "~2.2", "nette/utils": "~2.2", "php": ">=5.4.0"}, "require-dev": {"janmarek/mockista": "@dev", "nette/bootstrap": "~2.2", "nette/forms": "~2.2", "nette/mail": "~2.2", "nette/robot-loader": "~2.2", "nette/safe-stream": "~2.2", "nette/tester": "@dev", "tracy/tracy": "@dev"}, "type": "library", "autoload": {"psr-0": {"IPub\\VisualPaginator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "iPublikuj:cms", "email": "<EMAIL>", "homepage": "http://www.ipublikuj.eu/"}], "description": "Visual paginator for Nette Framework", "homepage": "https://github.com/iPublikuj/visual-paginator", "keywords": ["ipub", "ipublikuj", "nette", "paginator", "paging", "tools"], "abandoned": true, "time": "2018-07-02T18:04:23+00:00"}, {"name": "latte/latte", "version": "v2.4.8", "source": {"type": "git", "url": "https://github.com/nette/latte.git", "reference": "95ca6bab6caaa3efa3b5d7d4537f9a45cb89ed90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/latte/zipball/95ca6bab6caaa3efa3b5d7d4537f9a45cb89ed90", "reference": "95ca6bab6caaa3efa3b5d7d4537f9a45cb89ed90", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "php": ">=5.4.4"}, "conflict": {"nette/application": "<2.4.1"}, "require-dev": {"nette/tester": "~1.7", "tracy/tracy": "^2.3"}, "suggest": {"ext-fileinfo": "to use filter |datastream", "ext-mbstring": "to use filters like lower, upper, capitalize, ...", "nette/utils": "to use filter |webalize"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "☕ Latte: the intuitive and fast template engine for those who want the most secure PHP sites. Introduces context-sensitive escaping.", "homepage": "https://latte.nette.org", "keywords": ["context-sensitive", "engine", "escaping", "html", "nette", "security", "template", "twig"], "time": "2018-06-03T17:34:15+00:00"}, {"name": "minetro/recaptcha", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/minetro/reCAPTCHA.git", "reference": "b052b25a517ec65056d5a1ab3c21b0e53316fddc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/minetro/reCAPTCHA/zipball/b052b25a517ec65056d5a1ab3c21b0e53316fddc", "reference": "b052b25a517ec65056d5a1ab3c21b0e53316fddc", "shasum": ""}, "require": {"nette/di": "~2.4.0", "nette/forms": "~2.4.0", "nette/utils": "~2.4.0", "php": ">= 5.6"}, "require-dev": {"ninjify/nunjuck": "^0.1.4", "ninjify/qa": "^0.3.3"}, "suggest": {"ext-openssl": "To make requests via https"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}, "ninjify": {"qa": {"codesniffer": {"ruleset": "nette", "folders": ["src", "tests"]}, "codefixer": {"ruleset": "nette", "folders": ["src", "tests"]}, "linter": {"folders": ["src", "tests"]}}}}, "autoload": {"psr-4": {"Minetro\\ReCaptcha\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://jfx.cz"}], "description": "Google reCAPTCHA for Nette - Forms", "homepage": "https://github.com/minetro/reCAPTCHA", "keywords": ["Forms", "<PERSON><PERSON>a", "google", "nette", "recaptcha"], "abandoned": "contributte/reCAPTCHA", "time": "2017-04-05T09:25:01+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.34", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "6f8113f57a508494ca36acbcfa2dc2d923c7ed5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/6f8113f57a508494ca36acbcfa2dc2d923c7ed5b", "reference": "6f8113f57a508494ca36acbcfa2dc2d923c7ed5b", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8.35||~5.7"}, "type": "library", "autoload": {"classmap": ["Mobile_Detect.php"], "psr-0": {"Detection": "namespaced/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "time": "2019-09-18T18:44:20+00:00"}, {"name": "nette/application", "version": "v2.4.16", "source": {"type": "git", "url": "https://github.com/nette/application.git", "reference": "e32c04de211873c792d13d9a00c50083b8e05a23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/application/zipball/e32c04de211873c792d13d9a00c50083b8e05a23", "reference": "e32c04de211873c792d13d9a00c50083b8e05a23", "shasum": ""}, "require": {"nette/component-model": "^2.3", "nette/http": "^2.2", "nette/reflection": "^2.2", "nette/utils": "^2.4", "php": ">=5.6.0"}, "conflict": {"nette/di": "<2.4", "nette/forms": "<2.4", "nette/latte": "<2.4", "nette/nette": "<2.2"}, "require-dev": {"latte/latte": "^2.4.3", "mockery/mockery": "^1.0", "nette/di": "^2.4", "nette/forms": "^2.4", "nette/robot-loader": "^2.4.2 || ^3.0", "nette/security": "^2.4", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "suggest": {"latte/latte": "Allows using Latte in templates", "nette/forms": "Allows to use Nette\\Application\\UI\\Form"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🏆 Nette Application: a full-stack component-based MVC kernel for PHP that helps you write powerful and modern web applications. Write less, have cleaner code and your work will bring you joy.", "homepage": "https://nette.org", "keywords": ["Forms", "component-based", "control", "framework", "mvc", "mvp", "nette", "presenter", "routing", "seo"], "time": "2020-08-25T01:51:49+00:00"}, {"name": "nette/bootstrap", "version": "v2.4.6", "source": {"type": "git", "url": "https://github.com/nette/bootstrap.git", "reference": "268816e3f1bb7426c3a4ceec2bd38a036b532543"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/bootstrap/zipball/268816e3f1bb7426c3a4ceec2bd38a036b532543", "reference": "268816e3f1bb7426c3a4ceec2bd38a036b532543", "shasum": ""}, "require": {"nette/di": "~2.4.7", "nette/utils": "~2.4", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"latte/latte": "~2.2", "nette/application": "~2.3", "nette/caching": "~2.3", "nette/database": "~2.3", "nette/forms": "~2.3", "nette/http": "~2.4.0", "nette/mail": "~2.3", "nette/robot-loader": "^2.4.2 || ^3.0", "nette/safe-stream": "~2.2", "nette/security": "~2.3", "nette/tester": "~2.0", "tracy/tracy": "^2.4.1"}, "suggest": {"nette/robot-loader": "to use Configurator::createRobotLoader()", "tracy/tracy": "to use Configurator::enableTracy()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Bootstrap: the simple way to configure and bootstrap your Nette application.", "homepage": "https://nette.org", "keywords": ["bootstrapping", "configurator", "nette"], "time": "2018-05-17T12:52:20+00:00"}, {"name": "nette/caching", "version": "v2.5.9", "source": {"type": "git", "url": "https://github.com/nette/caching.git", "reference": "d93ef446836a5a0ff7ef78d5ffebb7fe043f9953"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/caching/zipball/d93ef446836a5a0ff7ef78d5ffebb7fe043f9953", "reference": "d93ef446836a5a0ff7ef78d5ffebb7fe043f9953", "shasum": ""}, "require": {"nette/finder": "^2.2 || ~3.0.0", "nette/utils": "^2.4 || ~3.0.0", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"latte/latte": "^2.4", "nette/di": "^2.4 || ~3.0.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "suggest": {"ext-pdo_sqlite": "to use SQLiteStorage or SQLiteJournal"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⏱ Nette Caching: library with easy-to-use API and many cache backends.", "homepage": "https://nette.org", "keywords": ["cache", "journal", "memcached", "nette", "sqlite"], "time": "2019-11-19T18:38:13+00:00"}, {"name": "nette/component-model", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/nette/component-model.git", "reference": "b6197fa6867d816b08457ac73b04ba70f78682e3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/component-model/zipball/b6197fa6867d816b08457ac73b04ba70f78682e3", "reference": "b6197fa6867d816b08457ac73b04ba70f78682e3", "shasum": ""}, "require": {"nette/utils": "^2.4 || ~3.0.0", "php": ">=5.6.0"}, "conflict": {"nette/application": "<2.4", "nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Component Model", "homepage": "https://nette.org", "time": "2017-07-11T08:59:35+00:00"}, {"name": "nette/database", "version": "v2.4.10", "source": {"type": "git", "url": "https://github.com/nette/database.git", "reference": "0ead09615f06d1ca77ae28d1eba0fe153d590ee0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/database/zipball/0ead09615f06d1ca77ae28d1eba0fe153d590ee0", "reference": "0ead09615f06d1ca77ae28d1eba0fe153d590ee0", "shasum": ""}, "require": {"ext-pdo": "*", "nette/caching": "^2.2", "nette/utils": "^2.4", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"mockery/mockery": "^1.0.0", "nette/di": "^2.4 || ~3.0.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "💾 Nette Database: layer with a familiar PDO-like API but much more powerful. Building queries, advanced joins, drivers for MySQL, PostgreSQL, SQLite, MS SQL Server and Oracle.", "homepage": "https://nette.org", "keywords": ["database", "mssql", "mysql", "nette", "notorm", "oracle", "pdo", "postgresql", "queries", "sqlite"], "time": "2020-03-19T14:46:46+00:00"}, {"name": "nette/deprecated", "version": "v2.4.2", "source": {"type": "git", "url": "https://github.com/nette/deprecated.git", "reference": "348e284192f6f6451692b1763b6ed235c28b25ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/deprecated/zipball/348e284192f6f6451692b1763b6ed235c28b25ae", "reference": "348e284192f6f6451692b1763b6ed235c28b25ae", "shasum": ""}, "require": {"php": "<7.2"}, "require-dev": {"latte/latte": "^2.2", "nette/application": "^2.2", "nette/bootstrap": "^2.2.1", "nette/caching": "^2.2", "nette/forms": "^2.2", "nette/mail": "^2.2", "nette/robot-loader": "^2.2", "nette/safe-stream": "^2.2", "nette/security": "^2.2", "nette/tester": "^1.1", "nette/utils": "^2.2", "tracy/tracy": "^2.2"}, "type": "library", "autoload": {"classmap": ["src/"], "files": ["src/loader.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Deprecated: APIs and features removed from Nette Framework", "homepage": "https://nette.org", "keywords": ["nette"], "time": "2018-02-06T16:29:40+00:00"}, {"name": "nette/di", "version": "v2.4.14", "source": {"type": "git", "url": "https://github.com/nette/di.git", "reference": "923da3e2c0aa53162ef455472c0ac7787b096c5a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/di/zipball/923da3e2c0aa53162ef455472c0ac7787b096c5a", "reference": "923da3e2c0aa53162ef455472c0ac7787b096c5a", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/neon": "^2.3.3 || ~3.0.0", "nette/php-generator": "^2.6.1 || ~3.0.0", "nette/utils": "^2.4.3 || ~3.0.0", "php": ">=5.6.0"}, "conflict": {"nette/bootstrap": "<2.4", "nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Dependency Injection Container: Flexible, compiled and full-featured DIC with perfectly usable autowiring and support for all new PHP 7.1 features.", "homepage": "https://nette.org", "keywords": ["compiled", "di", "dic", "factory", "ioc", "nette", "static"], "time": "2018-09-17T15:47:40+00:00"}, {"name": "nette/finder", "version": "v2.4.2", "source": {"type": "git", "url": "https://github.com/nette/finder.git", "reference": "ee951a656cb8ac622e5dd33474a01fd2470505a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/finder/zipball/ee951a656cb8ac622e5dd33474a01fd2470505a0", "reference": "ee951a656cb8ac622e5dd33474a01fd2470505a0", "shasum": ""}, "require": {"nette/utils": "~2.4", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~2.0", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Finder: find files and directories with an intuitive API.", "homepage": "https://nette.org", "keywords": ["filesystem", "glob", "iterator", "nette"], "time": "2018-06-28T11:49:23+00:00"}, {"name": "nette/forms", "version": "v2.4.10", "source": {"type": "git", "url": "https://github.com/nette/forms.git", "reference": "c38d0bc1d627c983075bf64560f8528c8e265d87"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/forms/zipball/c38d0bc1d627c983075bf64560f8528c8e265d87", "reference": "c38d0bc1d627c983075bf64560f8528c8e265d87", "shasum": ""}, "require": {"nette/component-model": "~2.3", "nette/http": "^2.3.8", "nette/utils": "^2.4.6", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"latte/latte": "~2.4", "nette/di": "~2.4", "nette/tester": "~2.0", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "?? Nette Forms: generating, validating and processing secure forms in PHP. Handy API, fully customizable, server & client side validation and mature design.", "homepage": "https://nette.org", "keywords": ["Forms", "bootstrap", "csrf", "javascript", "nette", "validation"], "time": "2019-11-19T15:39:27+00:00"}, {"name": "nette/http", "version": "v2.4.11", "source": {"type": "git", "url": "https://github.com/nette/http.git", "reference": "3d75d11a880fe223bfa6bc7ca9822bdfe789e5a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/http/zipball/3d75d11a880fe223bfa6bc7ca9822bdfe789e5a6", "reference": "3d75d11a880fe223bfa6bc7ca9822bdfe789e5a6", "shasum": ""}, "require": {"nette/utils": "^2.4 || ~3.0.0", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "^2.4.8 || ~3.0.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "suggest": {"ext-fileinfo": "to detect type of uploaded files", "nette/security": "allows use Nette\\Http\\UserStorage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Http: abstraction for HTTP request, response and session. Provides careful data sanitization and utility for URL and cookies manipulation.", "homepage": "https://nette.org", "keywords": ["cookies", "http", "nette", "proxy", "request", "response", "security", "session", "url"], "time": "2019-03-13T19:04:45+00:00"}, {"name": "nette/mail", "version": "v2.4.6", "source": {"type": "git", "url": "https://github.com/nette/mail.git", "reference": "431f1774034cc14ee6a795b6514fe6343f75a68e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/mail/zipball/431f1774034cc14ee6a795b6514fe6343f75a68e", "reference": "431f1774034cc14ee6a795b6514fe6343f75a68e", "shasum": ""}, "require": {"ext-iconv": "*", "nette/utils": "^2.4 || ~3.0.0", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "^2.4 || ~3.0.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "suggest": {"ext-fileinfo": "to detect type of attached files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Mail: handy email creation and transfer library for PHP with both text and MIME-compliant support.", "homepage": "https://nette.org", "keywords": ["mail", "mailer", "mime", "nette", "smtp"], "time": "2018-11-21T22:35:13+00:00"}, {"name": "nette/neon", "version": "v2.4.3", "source": {"type": "git", "url": "https://github.com/nette/neon.git", "reference": "5e72b1dd3e2d34f0863c5561139a19df6a1ef398"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/neon/zipball/5e72b1dd3e2d34f0863c5561139a19df6a1ef398", "reference": "5e72b1dd3e2d34f0863c5561139a19df6a1ef398", "shasum": ""}, "require": {"ext-iconv": "*", "ext-json": "*", "php": ">=5.6.0"}, "require-dev": {"nette/tester": "~2.0", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette NEON: encodes and decodes NEON file format.", "homepage": "http://ne-on.org", "keywords": ["export", "import", "neon", "nette", "yaml"], "time": "2018-03-21T12:12:21+00:00"}, {"name": "nette/nette", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/nette/nette.git", "reference": "25381e4ec7902734e49924c1ffd07017830c5f31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/nette/zipball/25381e4ec7902734e49924c1ffd07017830c5f31", "reference": "25381e4ec7902734e49924c1ffd07017830c5f31", "shasum": ""}, "require": {"latte/latte": "^2.4", "nette/application": "^2.4", "nette/bootstrap": "^2.4", "nette/caching": "^2.5", "nette/component-model": "^2.3", "nette/database": "^2.4", "nette/deprecated": "^2.3", "nette/di": "^2.4", "nette/finder": "^2.4", "nette/forms": "^2.4", "nette/http": "^2.4", "nette/mail": "^2.4", "nette/neon": "^2.4", "nette/php-generator": "^2.4", "nette/reflection": "^2.4", "nette/robot-loader": "^2.4", "nette/safe-stream": "^2.3", "nette/security": "^2.4", "nette/tokenizer": "^2.2", "nette/utils": "^2.4", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["Nette/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Framework - innovative framework for fast and easy development of secured web applications in PHP. Write less, have cleaner code and your work will bring you joy.", "homepage": "https://nette.org", "keywords": ["Forms", "database", "debugging", "framework", "mailing", "mvc", "templating"], "time": "2016-05-03T15:59:52+00:00"}, {"name": "nette/php-generator", "version": "v2.6.4", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "c1f6b2163c5471c4d94cd94b93362f12bceef183"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/c1f6b2163c5471c4d94cd94b93362f12bceef183", "reference": "c1f6b2163c5471c4d94cd94b93362f12bceef183", "shasum": ""}, "require": {"nette/utils": "^2.4.2 || ~3.0.0", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette PHP Generator: generates neat PHP code for you. Supports new PHP 7.1 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "time": "2018-04-26T16:47:39+00:00"}, {"name": "nette/reflection", "version": "v2.4.2", "source": {"type": "git", "url": "https://github.com/nette/reflection.git", "reference": "b12327e98ead74e87a1315e0d48182a702adf901"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/reflection/zipball/b12327e98ead74e87a1315e0d48182a702adf901", "reference": "b12327e98ead74e87a1315e0d48182a702adf901", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/caching": "^2.2 || ^3.0", "nette/utils": "^2.4 || ^3.0", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "^2.4 || ^3.0", "nette/tester": "^2.0", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Reflection: docblock annotations parser and common reflection classes", "homepage": "https://nette.org", "keywords": ["annotation", "nette", "reflection"], "abandoned": true, "time": "2017-07-11T19:28:57+00:00"}, {"name": "nette/robot-loader", "version": "v2.4.4", "source": {"type": "git", "url": "https://github.com/nette/robot-loader.git", "reference": "1f7f8792ce4d94162959e6b766822d6051623bca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/robot-loader/zipball/1f7f8792ce4d94162959e6b766822d6051623bca", "reference": "1f7f8792ce4d94162959e6b766822d6051623bca", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/caching": "~2.2", "nette/finder": "~2.3", "nette/utils": "~2.4", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~2.0", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette RobotLoader: high performance and comfortable autoloader that will search and autoload classes within your application.", "homepage": "https://nette.org", "keywords": ["autoload", "class", "interface", "nette", "trait"], "time": "2017-08-14T20:23:02+00:00"}, {"name": "nette/safe-stream", "version": "v2.3.3", "source": {"type": "git", "url": "https://github.com/nette/safe-stream.git", "reference": "0fcd45ae82be5817f4b3ad25bc8955968f355412"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/safe-stream/zipball/0fcd45ae82be5817f4b3ad25bc8955968f355412", "reference": "0fcd45ae82be5817f4b3ad25bc8955968f355412", "shasum": ""}, "require": {"php": ">=5.3.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~1.7", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"files": ["src/loader.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette SafeStream: atomic and safe manipulation with files via native PHP functions.", "homepage": "https://nette.org", "keywords": ["atomic", "filesystem", "nette", "safe"], "time": "2017-07-13T18:20:37+00:00"}, {"name": "nette/security", "version": "v2.4.4", "source": {"type": "git", "url": "https://github.com/nette/security.git", "reference": "7b8ac90c9ec405bb3b4dab9214bf122d3620fc65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/security/zipball/7b8ac90c9ec405bb3b4dab9214bf122d3620fc65", "reference": "7b8ac90c9ec405bb3b4dab9214bf122d3620fc65", "shasum": ""}, "require": {"nette/utils": "~2.4", "php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/di": "~2.4", "nette/http": "~2.4", "nette/tester": "~2.0", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Security: provides authentication, authorization and a role-based access control management via ACL (Access Control List)", "homepage": "https://nette.org", "keywords": ["Authentication", "acl", "authorization", "nette"], "time": "2018-10-17T15:50:54+00:00"}, {"name": "nette/tokenizer", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/nette/tokenizer.git", "reference": "88373e9f79007245af0ccd8132fde117421723b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tokenizer/zipball/88373e9f79007245af0ccd8132fde117421723b2", "reference": "88373e9f79007245af0ccd8132fde117421723b2", "shasum": ""}, "require": {"php": ">=5.4"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~1.7", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "<PERSON><PERSON>", "homepage": "https://nette.org", "time": "2017-09-08T14:16:06+00:00"}, {"name": "nette/utils", "version": "v2.4.10", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "89a88fc36f5bb76f9355ed8a09820cefcbb0af39"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/89a88fc36f5bb76f9355ed8a09820cefcbb0af39", "reference": "89a88fc36f5bb76f9355ed8a09820cefcbb0af39", "shasum": ""}, "require": {"php": ">=5.6.0"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "~2.0", "tracy/tracy": "^2.3"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize() and toAscii()", "ext-intl": "for script transliteration in Strings::webalize() and toAscii()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-xml": "to use Strings::length() etc. when mbstring is not available"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "? Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "time": "2018-09-18T10:09:08+00:00"}, {"name": "nextras/mail-panel", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/nextras/mail-panel.git", "reference": "790008bcbb9df1de8254cdda920ac023909b6b59"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/mail-panel/zipball/790008bcbb9df1de8254cdda920ac023909b6b59", "reference": "790008bcbb9df1de8254cdda920ac023909b6b59", "shasum": ""}, "require": {"latte/latte": "~2.4", "nette/http": "~2.4", "nette/mail": "~2.4", "nette/utils": "~2.4", "php": ">=5.6", "tracy/tracy": "~2.4"}, "require-dev": {"phpstan/phpstan-nette": "~0.9", "phpstan/phpstan-shim": "~0.9.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"psr-4": {"Nextras\\MailPanel\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://www.jandrabek.cz"}, {"name": "<PERSON>", "homepage": "http://www.janmarek.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "MailPanel is extension for Nette Framework which captures sent e-mails in development mode and shows them in debugger bar.", "keywords": ["debugging", "framework", "mail", "mail panel", "mailing", "mailpanel"], "time": "2017-12-12T09:07:26+00:00"}, {"name": "ondrakoupil/csob-eapi-paygate", "version": "v1.7.4", "source": {"type": "git", "url": "https://github.com/ondrakoupil/csob.git", "reference": "353751c1dbef85853ad50c06877224339a1a733a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ondrakoupil/csob/zipball/353751c1dbef85853ad50c06877224339a1a733a", "reference": "353751c1dbef85853ad50c06877224339a1a733a", "shasum": ""}, "require": {"ext-curl": "*", "ext-openssl": "*", "ondrakoupil/tools": "~0.0.5", "php": ">=5.3.1"}, "require-dev": {"nette/tester": "dev-master", "ondrakoupil/testing-utils": "~0.0.7"}, "type": "library", "autoload": {"psr-4": {"OndraKoupil\\Csob\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP Client library for easy integration of ČSOB payment gateway", "homepage": "https://github.com/ondrakoupil/csob", "keywords": ["Bank", "cards", "commerce", "csob", "merchant", "pay", "payment"], "time": "2017-06-27T06:23:05+00:00"}, {"name": "ondrakoupil/tools", "version": "v0.0.6", "source": {"type": "git", "url": "https://github.com/ondrakoupil/tools.git", "reference": "778c4931ba883d86a338ea9591d48eb9bb24eb13"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ondrakoupil/tools/zipball/778c4931ba883d86a338ea9591d48eb9bb24eb13", "reference": "778c4931ba883d86a338ea9591d48eb9bb24eb13", "shasum": ""}, "require-dev": {"nette/tester": "^1.4@dev", "ondrakoupil/testing-utils": "^0.0.7"}, "type": "library", "autoload": {"psr-4": {"OndraKoupil\\Tools\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Just a bunch of tools for myself", "time": "2015-07-27T20:41:18+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "tracy/tracy", "version": "v2.5.9", "source": {"type": "git", "url": "https://github.com/nette/tracy.git", "reference": "cd9b889371cfbffe17e5b1a19e0a11de1ad31c8f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tracy/zipball/cd9b889371cfbffe17e5b1a19e0a11de1ad31c8f", "reference": "cd9b889371cfbffe17e5b1a19e0a11de1ad31c8f", "shasum": ""}, "require": {"ext-json": "*", "ext-session": "*", "php": ">=5.4.4"}, "require-dev": {"nette/di": "~2.3 || ~3.0.0", "nette/tester": "~1.7 || ~2.0", "nette/utils": "~2.3"}, "suggest": {"https://nette.org/donate": "Please support <PERSON> via a donation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"classmap": ["src"], "files": ["src/shortcuts.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "😎 Tracy: the addictive tool to ease debugging PHP code for cool developers. Friendly design, logging, profiler, advanced features like debugging AJAX calls or CLI support. You will love it.", "homepage": "https://tracy.nette.org", "keywords": ["Xdebug", "debug", "debugger", "nette", "profiler"], "time": "2020-05-17T09:25:46+00:00"}, {"name": "ulozenka/api-v3", "version": "0.4.0", "source": {"type": "git", "url": "https://github.com/ulozenka/api-v3.git", "reference": "230b2e87b240bda95bad180bc92599e0bf72360a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ulozenka/api-v3/zipball/230b2e87b240bda95bad180bc92599e0bf72360a", "reference": "230b2e87b240bda95bad180bc92599e0bf72360a", "shasum": ""}, "require": {"bitbang/http": "0.2@dev", "php": ">= 5.4.0"}, "require-dev": {"nette/tester": "@dev"}, "type": "library", "autoload": {"psr-4": {"UlozenkaLib\\": "src/UlozenkaLib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.ulozenka.cz"}], "description": "Uloženka APIv3 library", "homepage": "https://www.ulozenka.cz/", "keywords": ["api", "restapi", "transport", "ulozenka"], "time": "2016-07-12T11:53:55+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "platform-overrides": {"php": "5.6"}, "plugin-api-version": "1.1.0"}