ALTER TABLE `admins` CHANGE `admpassw` `admpassw` VARCHAR(100) CHARSET utf8 COLLATE utf8_general_ci NOT NULL COMMENT 'heslo';
ALTER TABLE `users` CHANGE `usrpassw` `usrpassw` VARCHAR(100) CHARSET utf8 COLLATE utf8_general_ci DEFAULT '' NOT NULL COMMENT 'heslo';

DROP TABLE IF EXISTS `menus`;
CREATE TABLE `menus` (
  `menid` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
  `menmasid` mediumint(8) unsigned NOT NULL DEFAULT '0' COMMENT 'ID nadrizene urovne, 0 - korenova uroven',
  `mensrctype` enum('page','gallery','url') NOT NULL DEFAULT 'page' COMMENT 'typ polozky v menu',
  `mensrcid` mediumint(9) DEFAULT '0' COMMENT 'ID zaznamu',
  `menurlsys` varchar(100) DEFAULT NULL COMMENT 'Systemovy odkaz ',
  `menurl` varchar(255) DEFAULT NULL COMMENT 'URL cile',
  `menlevel` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT 'Level katalogu',
  `menname` varchar(250) NOT NULL COMMENT 'Nazev ',
  `menkeywords` varchar(255) DEFAULT NULL COMMENT 'klicove slova',
  `mendesc` text COMMENT 'popis',
  `menorder` int(11) NOT NULL DEFAULT '0' COMMENT 'poradi',
  `mencounter` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'pocitadlo navstev',
  `menpath` varchar(255) DEFAULT NULL COMMENT 'cesta do teto kategorie',
  `menpathids` varchar(255) DEFAULT NULL COMMENT 'cesta do teto kategorie, vycet ID',
  `menstatus` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT 'Status kategorie',
  `mendatec` datetime DEFAULT NULL COMMENT 'datum vytvoreni',
  `mendateu` datetime DEFAULT NULL COMMENT 'datum posledni zmeny',
  PRIMARY KEY (`menid`),
  KEY `i_catmasid` (`menmasid`),
  KEY `i_catorder` (`menorder`),
  KEY `i_catstatus` (`menstatus`),
  KEY `i_catpathids` (`menpathids`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

INSERT INTO `menus` (`menid`, `menmasid`, `mensrctype`, `mensrcid`, `menurlsys`, `menurl`, `menlevel`, `menname`, `menkeywords`, `mendesc`, `menorder`, `mencounter`, `menpath`, `menpathids`, `menstatus`, `mendatec`, `mendateu`) VALUES
(1,	0,	'page',	0,	'',	'',	1,	'Patička menu 1',	NULL,	NULL,	10,	0,	'|Patička menu 1|',	'|1|',	0,	'2017-04-13 17:11:50',	NULL),
(2,	1,	'page',	5,	'Page:detail',	'',	2,	'Kontakt',	NULL,	NULL,	40,	0,	'|Patička menu 1|Kontakt|',	'|1|2|',	0,	'2017-04-13 17:12:15',	NULL),
(3,	1,	'page',	4,	'Page:detail',	'',	2,	'Obchodní podmínky',	NULL,	NULL,	20,	0,	'|Patička menu 1|Obchodní podmínky|',	'|1|3|',	0,	'2017-04-13 17:13:02',	NULL),
(4,	1,	'page',	3,	'Page:detail',	'',	2,	'Doprava a platba',	NULL,	NULL,	10,	0,	'|Patička menu 1|Doprava a platba|',	'|1|4|',	0,	'2017-04-13 17:13:20',	NULL),
(5,	0,	'page',	0,	'',	'',	1,	'Patička menu 2',	NULL,	NULL,	20,	0,	'|Patička menu 2|',	'|5|',	0,	'2017-05-17 10:10:10',	NULL),
(6,	0,	'page',	0,	'',	'',	1,	'Top - horizontální menu',	NULL,	NULL,	1,	0,	'|Top - horizontální menu|',	'|6|',	0,	'2017-05-17 10:10:57',	NULL);

DROP TABLE IF EXISTS `pages`;
CREATE TABLE `pages` (
  `pagid` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
  `pagtypid` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT 'typ stranky',
  `pagurlkey` varchar(50) NOT NULL COMMENT 'URL klic',
  `pagname` varchar(255) NOT NULL COMMENT 'jmeno, title',
  `pagtitle` varchar(255) DEFAULT NULL COMMENT 'title stranky',
  `pagdescription` text COMMENT 'description stranky',
  `pagkeywords` varchar(255) DEFAULT NULL COMMENT 'klicove slova',
  `pagdesc` varchar(255) DEFAULT NULL COMMENT 'popis',
  `pagbody` longtext NOT NULL COMMENT 'telo stranky',
  `pagtext1` text COMMENT 'variabilni text 1',
  `pagtext2` text COMMENT 'variabilni text 2',
  `pagtext3` text COMMENT 'variabilni text 3',
  `pagtext4` text COMMENT 'variabilni text 4',
  `pagtext5` text COMMENT 'variabilni text 5',
  `pagtext6` text COMMENT 'variabilni text 6',
  `pagblock` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT 'zda je to blok',
  `pagstatus` tinyint(1) DEFAULT '0' COMMENT 'status zda zobrazovat',
  `pagdatec` datetime DEFAULT NULL COMMENT 'datum vytvoreni',
  `pagdateu` datetime DEFAULT NULL COMMENT 'datum zmeny',
  `Sloupec 14` int(11) DEFAULT NULL,
  PRIMARY KEY (`pagid`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

INSERT INTO `pages` (`pagid`, `pagtypid`, `pagurlkey`, `pagname`, `pagtitle`, `pagdescription`, `pagkeywords`, `pagdesc`, `pagbody`, `pagtext1`, `pagtext2`, `pagtext3`, `pagtext4`, `pagtext5`, `pagtext6`, `pagblock`, `pagstatus`, `pagdatec`, `pagdateu`, `Sloupec 14`) VALUES
(1,	0,	'footer_content',	'Patička stránky',	'',	'',	NULL,	NULL,	'<p>Patička content</p>',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	1,	0,	'2017-04-13 17:10:01',	'2017-05-17 10:17:35',	NULL),
(2,	0,	'footer_contact',	'Patička kontakt',	'',	'',	NULL,	NULL,	'<p>Patička kontakt</p>',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	1,	0,	'2017-04-13 17:10:26',	'2017-05-17 10:16:50',	NULL),
(3,	0,	'doprava-a-platba',	'Doprava a platba',	'',	'Doprava a platba',	'Doprava a platba',	NULL,	'<h1>Doprava a platba</h1>',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	0,	0,	'2017-05-17 10:14:28',	NULL,	NULL),
(4,	0,	'obchodni-podminky',	'Obchodní podmínky',	'Obchodní podmínky',	'Obchodní podmínky',	'Obchodní podmínky',	NULL,	'<h1>Obchodn&iacute; podm&iacute;nky</h1>',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	0,	0,	'2017-05-17 10:14:53',	NULL,	NULL),
(5,	0,	'kontakt',	'Kontakt',	'Kontakt',	'Kontakt',	'Kontakt',	NULL,	'<h1>Kontakt</h1>',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	0,	0,	'2017-05-17 10:15:15',	NULL,	NULL),
(6,	0,	'footer_copyright',	'Patička Copyright',	'Patička Copyright',	'Patička Copyright',	NULL,	NULL,	'<p>Patička Copyright</p>',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	1,	0,	'2017-05-17 10:18:38',	NULL,	NULL);


-- nová tabulka
CREATE TABLE `csob_log` (
  `payid` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `payordid` int(11) DEFAULT NULL,
  `payresponse` text COLLATE utf8_unicode_ci,
  `paydatec` datetime DEFAULT NULL,
  PRIMARY KEY (`payid`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

ALTER TABLE `orders` ADD COLUMN `orddatepayed` DATETIME NULL COMMENT 'datum úhrady' AFTER `ordpaystatus`;

ALTER TABLE `orders_log` ADD COLUMN `orladmid` INT NULL COMMENT 'id admina kdo provedl' AFTER `orlordid`, ADD COLUMN `orlnote` VARCHAR(255) NULL COMMENT 'poznámka' AFTER `orladmid`;

ALTER TABLE `orders` ADD COLUMN `orddisccoupon` DOUBLE NULL COMMENT 'sleva z kuponu' AFTER `orddiscpercent`, ADD COLUMN `ordcoucode` VARCHAR(15) NULL COMMENT 'kod slevoveho kuponu' AFTER `orddisccoupon`;

ALTER TABLE `orders` CHANGE `ordprice` `ordprice` DOUBLE(11,2) DEFAULT 0.00 NOT NULL COMMENT 'cena bez DPH', CHANGE `ordpricevat` `ordpricevat` DOUBLE(11,2) DEFAULT 0.00 NOT NULL COMMENT 'cena s DPH', CHANGE `orddisc` `orddisc` INT(10) UNSIGNED DEFAULT 0 NOT NULL COMMENT 'vyse slevy';

ALTER TABLE `orditems` ADD COLUMN `oridiscount` DOUBLE NULL COMMENT 'sleva' AFTER `oriprice`;
ALTER TABLE `orditems` ADD COLUMN `oripriceoriginal` DOUBLE NULL COMMENT 'puvodni cena pred slevou' AFTER `oridiscount`;

ALTER TABLE `deliverymodes` ADD COLUMN `delnodelfree` TINYINT(1) DEFAULT 0 NOT NULL COMMENT 'Nedovolit dopravu zdarma' AFTER `deltext2`;
ALTER TABLE `orditems` ADD COLUMN `orioriid` INT NULL COMMENT 'svázaná položka' AFTER `oriordid`;

ALTER TABLE `orders` ADD COLUMN `ordpaymentid` VARCHAR(100) NULL COMMENT 'ID platby platební brány' AFTER `orddatepayed`;

-- upravený obsah stránek a menu
DROP TABLE IF EXISTS `menus`;
CREATE TABLE `menus` (
  `menid` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
  `menmasid` mediumint(8) unsigned NOT NULL DEFAULT '0' COMMENT 'ID nadrizene urovne, 0 - korenova uroven',
  `mensrctype` enum('page','gallery','url') NOT NULL DEFAULT 'page' COMMENT 'typ polozky v menu',
  `mensrcid` mediumint(9) DEFAULT '0' COMMENT 'ID zaznamu',
  `menurlsys` varchar(100) DEFAULT NULL COMMENT 'Systemovy odkaz ',
  `menurl` varchar(255) DEFAULT NULL COMMENT 'URL cile',
  `menlevel` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT 'Level katalogu',
  `menname` varchar(250) NOT NULL COMMENT 'Nazev ',
  `menkeywords` varchar(255) DEFAULT NULL COMMENT 'klicove slova',
  `mendesc` text COMMENT 'popis',
  `menorder` int(11) NOT NULL DEFAULT '0' COMMENT 'poradi',
  `mencounter` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'pocitadlo navstev',
  `menpath` varchar(255) DEFAULT NULL COMMENT 'cesta do teto kategorie',
  `menpathids` varchar(255) DEFAULT NULL COMMENT 'cesta do teto kategorie, vycet ID',
  `menstatus` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT 'Status kategorie',
  `mendatec` datetime DEFAULT NULL COMMENT 'datum vytvoreni',
  `mendateu` datetime DEFAULT NULL COMMENT 'datum posledni zmeny',
  PRIMARY KEY (`menid`),
  KEY `i_catmasid` (`menmasid`),
  KEY `i_catorder` (`menorder`),
  KEY `i_catstatus` (`menstatus`),
  KEY `i_catpathids` (`menpathids`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

INSERT INTO `menus` (`menid`, `menmasid`, `mensrctype`, `mensrcid`, `menurlsys`, `menurl`, `menlevel`, `menname`, `menkeywords`, `mendesc`, `menorder`, `mencounter`, `menpath`, `menpathids`, `menstatus`, `mendatec`, `mendateu`) VALUES
  (1,	0,	'page',	0,	'',	'',	1,	'Patička menu 1',	NULL,	NULL,	10,	0,	'|Patička menu 1|',	'|1|',	0,	'2017-04-13 17:11:50',	NULL),
  (2,	1,	'page',	5,	'Page:detail',	'',	2,	'Kontakt',	NULL,	NULL,	40,	0,	'|Patička menu 1|Kontakt|',	'|1|2|',	0,	'2017-04-13 17:12:15',	NULL),
  (3,	1,	'page',	4,	'Page:detail',	'',	2,	'Obchodní podmínky',	NULL,	NULL,	20,	0,	'|Patička menu 1|Obchodní podmínky|',	'|1|3|',	0,	'2017-04-13 17:13:02',	NULL),
  (4,	1,	'page',	3,	'Page:detail',	'',	2,	'Doprava a platba',	NULL,	NULL,	10,	0,	'|Patička menu 1|Doprava a platba|',	'|1|4|',	0,	'2017-04-13 17:13:20',	NULL),
  (5,	0,	'page',	0,	'',	'',	1,	'Patička menu 2',	NULL,	NULL,	20,	0,	'|Patička menu 2|',	'|5|',	0,	'2017-05-17 10:10:10',	NULL),
  (6,	0,	'page',	0,	'',	'',	1,	'Top - horizontální menu',	NULL,	NULL,	1,	0,	'|Top - horizontální menu|',	'|6|',	0,	'2017-05-17 10:10:57',	NULL),
  (7,	6,	'page',	0,	'',	'/',	2,	'Úvod',	NULL,	NULL,	10,	0,	'|Top - horizontální menu|Úvod|',	'|6|7|',	0,	'2017-06-09 09:48:22',	NULL),
  (8,	6,	'page',	7,	'Page:detail',	'',	2,	'Vše o nákupu',	NULL,	NULL,	20,	0,	'|Top - horizontální menu|Vše o nákupu|',	'|6|8|',	0,	'2017-06-09 09:49:41',	NULL),
  (9,	6,	'page',	5,	'Page:detail',	'',	2,	'Kontakt',	NULL,	NULL,	30,	0,	'|Top - horizontální menu|Kontakt|',	'|6|9|',	0,	'2017-06-09 09:50:09',	NULL);

DROP TABLE IF EXISTS `pages`;
CREATE TABLE `pages` (
  `pagid` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
  `pagtypid` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT 'typ stranky',
  `pagurlkey` varchar(50) NOT NULL COMMENT 'URL klic',
  `pagname` varchar(255) NOT NULL COMMENT 'jmeno, title',
  `pagtitle` varchar(255) DEFAULT NULL COMMENT 'title stranky',
  `pagdescription` text COMMENT 'description stranky',
  `pagkeywords` varchar(255) DEFAULT NULL COMMENT 'klicove slova',
  `pagdesc` varchar(255) DEFAULT NULL COMMENT 'popis',
  `pagbody` longtext NOT NULL COMMENT 'telo stranky',
  `pagtext1` text COMMENT 'variabilni text 1',
  `pagtext2` text COMMENT 'variabilni text 2',
  `pagtext3` text COMMENT 'variabilni text 3',
  `pagtext4` text COMMENT 'variabilni text 4',
  `pagtext5` text COMMENT 'variabilni text 5',
  `pagtext6` text COMMENT 'variabilni text 6',
  `pagblock` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT 'zda je to blok',
  `pagstatus` tinyint(1) DEFAULT '0' COMMENT 'status zda zobrazovat',
  `pagdatec` datetime DEFAULT NULL COMMENT 'datum vytvoreni',
  `pagdateu` datetime DEFAULT NULL COMMENT 'datum zmeny',
  `Sloupec 14` int(11) DEFAULT NULL,
  PRIMARY KEY (`pagid`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

INSERT INTO `pages` (`pagid`, `pagtypid`, `pagurlkey`, `pagname`, `pagtitle`, `pagdescription`, `pagkeywords`, `pagdesc`, `pagbody`, `pagtext1`, `pagtext2`, `pagtext3`, `pagtext4`, `pagtext5`, `pagtext6`, `pagblock`, `pagstatus`, `pagdatec`, `pagdateu`, `Sloupec 14`) VALUES
  (1,	0,	'footer_content',	'Patička stránky',	'',	'',	NULL,	NULL,	'<p>Patička content</p>',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	1,	0,	'2017-04-13 17:10:01',	'2017-05-17 10:17:35',	NULL),
  (2,	0,	'footer_contact',	'Patička kontakt',	'',	'',	NULL,	NULL,	'<p>Patička kontakt</p>',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	1,	0,	'2017-04-13 17:10:26',	'2017-05-17 10:16:50',	NULL),
  (3,	0,	'doprava-a-platba',	'Doprava a platba',	'',	'Doprava a platba',	'Doprava a platba',	NULL,	'<h1>Doprava a platba</h1>',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	0,	0,	'2017-05-17 10:14:28',	NULL,	NULL),
  (4,	0,	'obchodni-podminky',	'Obchodní podmínky',	'Obchodní podmínky',	'Obchodní podmínky',	'Obchodní podmínky',	NULL,	'<h1>Obchodn&iacute; podm&iacute;nky</h1>',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	0,	0,	'2017-05-17 10:14:53',	NULL,	NULL),
  (5,	0,	'kontakt',	'Kontakt',	'Kontakt',	'Kontakt',	'Kontakt',	NULL,	'<h1>Kontakt</h1>',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	0,	0,	'2017-05-17 10:15:15',	NULL,	NULL),
  (6,	0,	'footer_copyright',	'Patička Copyright',	'Patička Copyright',	'Patička Copyright',	NULL,	NULL,	'<p>Patička Copyright</p>',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	1,	0,	'2017-05-17 10:18:38',	NULL,	NULL),
  (7,	0,	'vse-o-nakupu',	'Vše o nákupu',	'Vše o nákupu',	'Vše o nákupu',	'Vše o nákupu',	NULL,	'<h1>V&scaron;e o n&aacute;kupu</h1>',	NULL,	NULL,	NULL,	NULL,	NULL,	NULL,	0,	0,	'2017-06-09 09:49:13',	NULL,	NULL);

-- upravené nastavení eshopu
DROP TABLE IF EXISTS `config`;
CREATE TABLE `config` (
  `cfgid` mediumint(8) unsigned NOT NULL AUTO_INCREMENT COMMENT 'primarni klic',
  `cfgcode` varchar(20) NOT NULL DEFAULT '' COMMENT 'kod nastaveni',
  `cfgtypid` tinyint(1) unsigned DEFAULT NULL COMMENT 'typ nastaveni',
  `cfgcontroltype` enum('text','textarea','combo','checkbox') DEFAULT 'text' COMMENT 'typ editacniho prvku',
  `cfgvalues` varchar(255) DEFAULT NULL COMMENT 'vycet moznych hodnot',
  `cfgvalue` varchar(255) NOT NULL DEFAULT '' COMMENT 'hodnota nastaveni',
  `cfgorder` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT 'poradi',
  `cfgnote` varchar(255) DEFAULT NULL COMMENT 'poznamka',
  `cfgdatec` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'datum vytvoreni',
  `cfgdateu` datetime DEFAULT NULL COMMENT 'datum posledni zmeny',
  PRIMARY KEY (`cfgid`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

INSERT INTO `config` (`cfgid`, `cfgcode`, `cfgtypid`, `cfgcontroltype`, `cfgvalues`, `cfgvalue`, `cfgorder`, `cfgnote`, `cfgdatec`, `cfgdateu`) VALUES
  (1,	'CATALOG_ROWSCNT',	43,	'text',	NULL,	'24',	8,	'Počet položek na jednu stránku v kataogu',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (9,	'SERVER_NAME',	40,	'text',	NULL,	'Widex.cz',	1,	'název serveru uváděný v title stránky',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (20,	'SERVER_MAIL',	40,	'text',	NULL,	'<EMAIL>',	9,	'email serveru',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (91,	'VATTYPE_0',	40,	'text',	NULL,	'21',	255,	'Výše základní daňové sazby v %',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (52,	'PRICEVAT',	40,	'combo',	'inclvat,novat;s DPH,bez DPH',	'inclvat',	99,	'Ceny uvedeny:',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (64,	'INDEX_PRODUCTLIST',	46,	'text',	NULL,	'78,59,77,82,72,65',	15,	'Výčet ID zboží pro výpis na úvodní stránce (zadávejte ID zboží oddělené čárkou)',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (96,	'CATPICSIZE',	42,	'text',	NULL,	'200x200',	35,	'Velikost obrázku katalogu',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (67,	'PROPICSIZE_BIG',	42,	'text',	NULL,	'400x400',	30,	'Velikost obrázku v katalogu zboží - Největší (SIRKAxVYSKA)',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (68,	'PROPICSIZE_DETAIL',	42,	'text',	NULL,	'315x315',	30,	'Velikost obrázku v katalogu zboží - Detail (SIRKAxVYSKA)',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (69,	'PROPICSIZE_LIST',	42,	'text',	NULL,	'160x160',	30,	'Velikost obrázku v katalogu zboží - Seznam (SIRKAxVYSKA)',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (73,	'CATALOG_ROWSCNT_TAB',	43,	'text',	NULL,	'50',	8,	'Počet položek na jednu stránku v kataogu v tabulkovém výpisu',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (92,	'VATTYPE_1',	40,	'text',	NULL,	'15',	255,	'Výše snížené daňové sazby v %',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (93,	'SERVER_NAMESHORT',	40,	'text',	NULL,	'Widex.cz',	5,	'Krátký název serveru (uvádí se např. jménu odesilatele emailu)',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (85,	'ANTISPAM_NO',	40,	'text',	NULL,	'554',	0,	'Antispamová kontrolní číslo',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (94,	'CATALOG_SSTATROWSCNT',	43,	'text',	NULL,	'10',	0,	'Počet položek ve výpisu nejprodávanějšího zboží v katalogu',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (95,	'ADMIN_ROWSCNT',	43,	'text',	NULL,	'90',	0,	'Počet položek ve výpisu na jednu stránku v administraci',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (97,	'MENU_PRODUCTLIST',	46,	'text',	NULL,	'',	16,	'Výpis zboží v menu',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (101,	'INDEX_TITLE',	46,	'text',	NULL,	'Widex.cz',	5,	'Title úvodní stránky',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (102,	'INDEX_KEYWORDS',	46,	'text',	NULL,	'Widex.cz',	6,	'Keywords úvodní stránky',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (103,	'INDEX_DESC',	46,	'textarea',	NULL,	'Widex.cz',	7,	'Description úvodní stránky',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (104,	'SERVER_ACCNO',	40,	'text',	NULL,	'*********/0000',	50,	'Číslo bankovního účtu',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (105,	'SERVER_LANGUAGE',	40,	'combo',	'cs,en;česky,anglicky',	'cs',	1,	'Hlavní jazyk eshopu',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (106,	'SERVER_MAILFORPAY',	40,	'text',	NULL,	'<EMAIL>',	10,	'Email pro online platby',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (129,	'PRICE2RATE',	40,	'text',	NULL,	'27,50',	255,	'Měnový kurz pro výpočet ceny 2',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (108,	'SERVER_MAILORDERS',	40,	'text',	NULL,	'<EMAIL>',	0,	'Email na který se posílá informace o nové objednávce',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (109,	'HEADER_H1_1',	46,	'text',	NULL,	'Widex.cz',	20,	'Hlavička nadpis H1 první část',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (110,	'HEADER_H1_2',	46,	'text',	NULL,	'Widex.cz',	25,	'Hlavička nadpis H1 druhá část',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (111,	'HEADER_H2',	46,	'text',	NULL,	'Widex.cz',	30,	'Hlavička nadpis H2',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (112,	'CHECK_STOCK',	43,	'combo',	'0,1;Ne,Ano',	'0',	0,	'Hlídat stav skladu, nepovolit objednat zboží co není skladem',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (113,	'MEIPICSIZE',	46,	'text',	NULL,	'1150x450',	255,	'Velikost obrázku v menu na úvodní stránce',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (114,	'MEIPICSIZEBIG',	46,	'text',	NULL,	'1150x450',	100,	'Velikost velkého obrázku v menu na úvodní stránce',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (115,	'DEFAULT_DISCOUT',	40,	'text',	NULL,	'0',	255,	'Výchozí sleva pro registrované zákazníky',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (116,	'WEIGHT_LIMIT',	40,	'text',	NULL,	'10',	255,	'Váhový limit v Kg kdy se stanovuje poštovné individuelně',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (122,	'PRODUCT_NEWAGE',	40,	'text',	NULL,	'30',	255,	'Kolik dní od vytvoření držet u zboží příznak novinka',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (123,	'INVOICE_VENDOR_R1',	40,	'text',	NULL,	'Widex Line spol. s r.o.',	255,	'Objednávka adresa dodavatele 1. řádek',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (124,	'INVOICE_VENDOR_R2',	40,	'text',	NULL,	'Bohušovická 230/12',	255,	'Objednávka adresa dodavatele 2. řádek',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (125,	'INVOICE_VENDOR_R3',	40,	'text',	NULL,	'190 00 Praha 9',	255,	'Objednávka adresa dodavatele 3. řádek',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (126,	'INVOICE_VENDOR_R4',	40,	'text',	NULL,	'telefon: 283 882 217',	255,	'Objednávka adresa dodavatele 4. řádek',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (127,	'INVOICE_VENDOR_R5',	40,	'text',	NULL,	'IČ: 45786381, DIČ: CZ45786381',	255,	'Objednávka adresa dodavatele IČ a DIČ',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (128,	'INVOICE_VENDOR_R6',	40,	'text',	NULL,	'Registrace: KOS v Praze, oddíl C, vložka 17126',	255,	'Zápis v obchodním rejstříku',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (130,	'FOOTER_FAVPRODUCTIDS',	46,	'text',	NULL,	'',	16,	'TOP 10 - nejsledovanější v patičce (zadávejte ID zboží oddělené čárkou)',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (131,	'INDEX_PRODUCTLISTNEW',	46,	'text',	NULL,	'78,59,77,82,72,65',	16,	'Novinky na úvodní stránce (ID zboží oddělené čárkou)',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (135,	'GOLDDAY_DATETO',	46,	'text',	NULL,	'',	16,	'Datum platnosti akční slevy do',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (138,	'DISCOUNT_DISSOLVE',	46,	'combo',	'0,1;Ne,Ano',	'1',	255,	'Rozpouštět množstevní slevu do položek objednávky',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (139,	'SERVER_MAILING_MAIL',	40,	'text',	NULL,	'<EMAIL>',	0,	'Email na který se nemají posílat odpovědi',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (140,	'SERVER_MAILING_CNT',	40,	'text',	NULL,	'50',	0,	'Počet emailů v jedné dávce, který se odešle jednou za hodinu',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11'),
  (141,	'CATALOG_ROWSCNT_MOBI',	43,	'text',	NULL,	'12',	9,	'Počet položek v katalogu na jednu stránku pro mobily',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11');

ALTER TABLE `menuindexs` ADD COLUMN `meitarget` VARCHAR(10) CHARSET utf8 COLLATE utf8_general_ci DEFAULT 'index' NOT NULL COMMENT 'Cíl zobrazneí reklamy' AFTER `meicatid`;
ALTER TABLE `menuindexs` ADD INDEX `i_target` (`meitarget`);

INSERT INTO `config` (`cfgid`, `cfgcode`, `cfgtypid`, `cfgcontroltype`, `cfgvalues`, `cfgvalue`, `cfgorder`, `cfgnote`, `cfgdatec`, `cfgdateu`) VALUES
  (NULL,	'MEIPICSIZEFOOTER',	46,	'text',	NULL,	'100x100',	255,	'Velikost obrázku v reklamě v patičce',	'0000-00-00 00:00:00',	'2017-06-09 11:22:11');

UPDATE `config` SET `cfgnote` = 'Velikost obrázku pro reklamu na úvodní stránce' WHERE `cfgid` = '113';
UPDATE `config` SET `cfgnote` = 'Velikost velkého obrázku pro reklamu na úvodní stránce' WHERE `cfgid` = '114';

UPDATE `config` SET `cfgtypid` = '42' WHERE `cfgid` = '112';

ALTER TABLE `menus` CHANGE `mensrctype` `mensrctype` VARCHAR(10) CHARSET utf8 COLLATE utf8_general_ci DEFAULT 'page' NOT NULL COMMENT 'typ polozky v menu';
UPDATE `menus`set mensrctype='not' WHERE mensrcid=0 and (mensrctype ='page' OR mensrctype ='');

ALTER TABLE `products` ADD COLUMN `progifts` VARCHAR(255) NULL COMMENT 'dárky' AFTER `procodep`;

-- PROVEDENO - EKRAMEK

-- velikosti obrázků novinky a články do nastavení
INSERT INTO `config` (`cfgid`, `cfgcode`, `cfgtypid`, `cfgcontroltype`, `cfgvalues`, `cfgvalue`, `cfgorder`, `cfgnote`, `cfgdatec`, `cfgdateu`) VALUES
(NULL,	'ARTPICSIZE_BIG',	42,	'text',	NULL,	'400x400',	30,	'Velikost obrázku k článku - Největší (SIRKAxVYSKA)',	'0000-00-00 00:00:00',	'2017-06-29 08:48:46'),
(NULL,	'ARTPICSIZE_DETAIL',	42,	'text',	NULL,	'315x315',	30,	'Velikost obrázku k článku - Detail (SIRKAxVYSKA)',	'0000-00-00 00:00:00',	'2017-06-29 08:48:46'),
(NULL,	'ARTPICSIZE_LIST',	42,	'text',	NULL,	'160x160',	30,	'Velikost obrázku k článku - Seznam (SIRKAxVYSKA)',	'0000-00-00 00:00:00',	'2017-06-29 08:48:46');

INSERT INTO `config` (`cfgid`, `cfgcode`, `cfgtypid`, `cfgcontroltype`, `cfgvalues`, `cfgvalue`, `cfgorder`, `cfgnote`, `cfgdatec`, `cfgdateu`) VALUES
(NULL,	'NEWPICSIZE_BIG',	42,	'text',	NULL,	'400x400',	30,	'Velikost obrázku k novince - Největší (SIRKAxVYSKA)',	'0000-00-00 00:00:00',	'2017-06-29 08:48:46'),
(NULL,	'NEWPICSIZE_DETAIL',	42,	'text',	NULL,	'315x315',	30,	'Velikost obrázku k novince - Detail (SIRKAxVYSKA)',	'0000-00-00 00:00:00',	'2017-06-29 08:48:46'),
(NULL,	'NEWPICSIZE_LIST',	42,	'text',	NULL,	'160x160',	30,	'Velikost obrázku k novince - Seznam (SIRKAxVYSKA)',	'0000-00-00 00:00:00',	'2017-06-29 08:48:46');



CREATE TABLE `proparamdefs` (
  `prdid` INT(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'PK',
  `prdcatid` INT(11) DEFAULT NULL COMMENT 'Kategorie zboží',
  `prdname` VARCHAR(100) COLLATE utf8_unicode_ci NOT NULL COMMENT 'název parametru',
  `prdsearch` TINYINT(1) NOT NULL DEFAULT '0' COMMENT 'vyhledávat podle něj',
  `prdunit` VARCHAR(10) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'jednotka',
  `prddatec` DATETIME DEFAULT NULL,
  `prddateu` DATETIME DEFAULT NULL,
  PRIMARY KEY (`prdid`)
) ENGINE=MYISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

ALTER TABLE `proparams` ADD COLUMN `prpprdid` INT NULL COMMENT 'ID definice parametru' AFTER `prpid`;

CREATE TABLE `proaccess` (
  `acsid` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `acsproid` int(10) unsigned NOT NULL,
  `acsacsproid` int(10) unsigned NOT NULL,
  `acsisban` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `acsdatec` datetime DEFAULT NULL,
  `acsdateu` datetime DEFAULT NULL,
  PRIMARY KEY (`acsid`),
  KEY `i_proid` (`acsproid`,`acsacsproid`)
) ENGINE=MyISAM AUTO_INCREMENT=2824 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


ALTER TABLE `orditems` ADD COLUMN `orivat` DOUBLE NULL COMMENT 'sazba DPH v %' AFTER `oripricemaster`;

UPDATE `config` SET `cfgcode` = 'CATPICSIZE_BIG' , `cfgvalue` = '100x140' WHERE `cfgid` = '96';
UPDATE `config` SET `cfgnote` = 'Velikost obrázku katalogu - velká' WHERE `cfgid` = '96';
INSERT INTO `config` (`cfgid`, `cfgcode`, `cfgtypid`, `cfgcontroltype`, `cfgvalues`, `cfgvalue`, `cfgorder`, `cfgnote`, `cfgdatec`, `cfgdateu`) VALUES (NULL, 'CATPICSIZE_SMALL', '42', 'text', NULL, '75x75', '35', 'Velikost obrázku katalogu - malá', '0000-00-00 00:00:00', '2017-06-09 11:22:11');

ALTER TABLE `proparamdefs` ADD COLUMN `prdstatus` TINYINT(1) DEFAULT 0 NOT NULL COMMENT 'status' AFTER `prdunit`;
ALTER TABLE `products` ADD COLUMN `procatrootid` INT NULL COMMENT 'ID kořenové úrovně produktu' AFTER `promasid`;

CREATE TABLE `product_prices` (
  `praid` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
  `praproid` int(10) unsigned NOT NULL COMMENT 'ID produktu',
  `praprccat` char(1) COLLATE utf8_unicode_ci NOT NULL COMMENT 'cenová kategorie',
  `pracurid` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'ID měny',
  `pradatefrom` date NOT NULL COMMENT 'datum od',
  `pradateto` date NOT NULL COMMENT 'datum do',
  `prapricebefore` double DEFAULT NULL COMMENT 'cena před',
  `praprice` double NOT NULL COMMENT 'cena pro zadané období',
  `prastatus` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0-připravená,1-aktivní,2-ukončená',
  `pradatec` datetime NOT NULL,
  `pradateu` datetime DEFAULT NULL,
  PRIMARY KEY (`praid`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

ALTER TABLE `proparams` ADD INDEX `i_prpprdid` (`prpprdid`);
INSERT INTO `config` (`cfgcode`, `cfgtypid`, `cfgorder`, `cfgnote`) VALUES ('PROPICSIZE_SMALL', '42', '30', 'Velikost obrázku produktu malý (ŠÍŘKAxVÝŠKA)');

ALTER TABLE `product_prices` ADD COLUMN `pratypid` TINYINT(1) DEFAULT 0 NOT NULL AFTER `praprice`, ADD COLUMN `pratypid2` TINYINT(1) DEFAULT 0 NOT NULL AFTER `pratypid`, ADD COLUMN `pratypid3` TINYINT(1) DEFAULT 0 NOT NULL AFTER `pratypid2`, ADD COLUMN `pratypid4` TINYINT(1) DEFAULT 0 NOT NULL AFTER `pratypid3`, ADD COLUMN `pratypid5` TINYINT(1) DEFAULT 0 NOT NULL AFTER `pratypid4`;

-- nová tabulka eet_log

ALTER TABLE deliverymodes ADD delpricelimit TINYINT DEFAULT 0 NOT NULL;
ALTER TABLE deliverymodes
  MODIFY COLUMN delpricelimit TINYINT NOT NULL DEFAULT 0 AFTER delspecdel;

CREATE TABLE users_log
(
    uslid INT PRIMARY KEY AUTO_INCREMENT,
    uslusrid INT NOT NULL COMMENT 'ID uživatele',
    uslevtid TINYINT NOT NULL COMMENT 'ID události',
    usldatec DATETIME NOT NULL COMMENT 'datum a čas události',
    usldateu DATETIME
);
CREATE INDEX users_log_uslusrid_index ON users_log (uslusrid);

ALTER TABLE users ADD usrrepretel VARCHAR(255) NULL;
ALTER TABLE users ADD usrrepremail VARCHAR(255) NULL;
ALTER TABLE users ADD usrrepreaddress VARCHAR(255) NULL;
ALTER TABLE users ADD usrreprename VARCHAR(255) NULL;
ALTER TABLE users ADD usragelimit TINYINT DEFAULT 0 NOT NULL;
ALTER TABLE users
  MODIFY COLUMN usrdateu DATETIME COMMENT 'datum posledni zmeny' AFTER usrrepretel,
  MODIFY COLUMN usrdatec DATETIME NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT 'datum registrace' AFTER usrrepretel,
  MODIFY COLUMN usrstatus TINYINT(1) unsigned NOT NULL DEFAULT '0' COMMENT 'status' AFTER usrrepretel;

-- PROVEDENO - DEV EKRAMEKCZ

create table ulozenkapoints
);

(
	uloid int unsigned auto_increment
		primary key,
	uloid2 int(10) null,
	uloshortcut varchar(255) null,
	uloname varchar(255) null,
	ulostreet varchar(255) null,
	ulocity varchar(255) null,
	ulopostcode varchar(255) null,
	ulocountry varchar(255) null,
	uloemail varchar(255) null,
	ulophone varchar(255) null,
	uloopeninghours text null,
	ulostatus tinyint(1) default '0' not null,
	ulourl varchar(255) null,
	ulourlphoto varchar(255) null,
	ulourlself varchar(255) null,
	ulogpsn varchar(255) null,
	ulogpse varchar(255) null,
	ulonavigation text null,
	ulodatec datetime null,
	ulodateu datetime null
)
engine=MyISAM)
;

create index id2
	on ulozenkapoints (uloshortcut)
;





