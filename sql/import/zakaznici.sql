TRUNCATE users;
INSERT INTO users (
SELECT
usrid AS usrid,
usrpricecat AS usrprccat,
0 AS usrdiscount,
'' AS usrpassw,
usremail AS usrmail,
'' AS usrstname,
usrstname AS usrstlname,
'' AS usrstfirname,
usrststreet AS usrststreet,
NULL AS usrststreetno,
usrstcity AS usrstcity,
usrstpostcode AS usrstpostcode,
1 AS usrstcouid,
usrstgsm AS usrtel,
'' AS usriname,
usriname AS usrilname,
NULL AS usrifirname,
usristreet AS usristreet,
NULL AS usristreetno,
usricity AS usricity,
usripostcode AS usripostcode,
1 AS usricouid,
0 AS usrvat,
usrico AS usric,
usrdic AS usrdic,
usrip AS usrip,
usrnote AS usrnote,
NULL AS usrmailvcode,
0 AS usrmailverified,
usrmaillist AS usrmaillist,
null AS usrrepretel,
usrdisable AS usrstatus,
usrc AS usrdatec,
usru AS usrdateu,
null AS usrrepremail,
null AS usrrepreaddress,
null AS usrreprename,
0 AS usragelimit,
0 AS usrgdpr
FROM ekr_user
);
