# deploy master větve pomocí git-ftp https://github.com/git-ftp/git-ftp/blob/master/man/git-ftp.1.md
# vyžaduje nastavit v settings -> pipelines -> environment variables pro ftp
# pokud ftp server podporuje používat pro ftp_..._url ftpes:// protokol, tedy ftpes://ftp.server.name
# pro první spuštění a inicializaci na ftp serveru použít místo git ftp init ... nebo na server nahrát soubor .git-ftp.log ve kterém je id aktuálního commitu, který je na ftp

image: bitnami/git

pipelines:
  custom:
    1-deploy-production:
      - step:
          script:
            - apt-get update
            - apt-get -qq install git-ftp
            - git ftp push --insecure --user $FTP_PRODUCTION_USERNAME --passwd $FTP_PRODUCTION_PASSWORD ftpes://ftp.kosmetika-biolage.cz/public_html/
            - curl -O https://www.kosmetika-biolage.cz/cc.php?k=lZwJIL
