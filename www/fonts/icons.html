<!doctype html>
<html>
	<head>
		<meta charset="utf-8">
		<title>icons</title>
		<style>
		body {
			margin:0;
			padding:10px 20px;
			background:#fff;
			color:#222;
			}
		h1, div, footer {
			font-family:"Helvetica Neue", <PERSON><PERSON>, sans-serif;
			}
		h1 {
			margin:0 0 20px;
			font-size:32px;
			font-weight:normal;
			}
		h1 small {
			font-size: 0.8em;
			padding-left: 2em;
		}
		.icons {
			margin-bottom:40px;
			-webkit-column-count:5;
			   -moz-column-count:5;
			        column-count:5;
			-webkit-column-gap:20px;
			   -moz-column-gap:20px;
			        column-gap:20px;
			}
		.icons__item,
		.icons__item i {
			line-height:2em;
			cursor:pointer;
			overflow:hidden;
			}
		.icons__item:hover {
			color:#3c90be;
			}
		.icons__item i {
			display:inline-block;
			width:32px;
			text-align:center;
			}
		.icons__item:hover i {
			-webkit-transform:scale(1.5);
			        transform:scale(1.5);
			}
		footer {
			margin-top:40px;
			font-size:14px;
			color:#999;
			}

		/* Generated by grunt-webfont */


@font-face {
	font-family:"icons";
	src:url("icons.eot?895dc50df3c755967eacbc5ce7997e21");
	src:url("icons.eot?#iefix") format("embedded-opentype"),
		url("icons.woff?895dc50df3c755967eacbc5ce7997e21") format("woff"),
		url("icons.ttf?895dc50df3c755967eacbc5ce7997e21") format("truetype");
	font-weight:normal;
	font-style:normal;
}

.icon {
	
		font-family:"icons";
	
	display:inline-block;
	vertical-align:middle;
	line-height:1;
	font-weight:normal;
	font-style:normal;
	speak:none;
	text-decoration:inherit;
	text-transform:none;
	text-rendering:auto;
	-webkit-font-smoothing:antialiased;
	-moz-osx-font-smoothing:grayscale;
}


/* Icons */


.icon--arrow-down:before {
	content:"\f101";
}


.icon--arrow-left:before {
	content:"\f102";
}


.icon--arrow-right:before {
	content:"\f103";
}


.icon--arrow-up:before {
	content:"\f104";
}


.icon--basket:before {
	content:"\f105";
}


.icon--calc:before {
	content:"\f106";
}


.icon--chat:before {
	content:"\f107";
}


.icon--close:before {
	content:"\f108";
}


.icon--dog:before {
	content:"\f109";
}


.icon--download:before {
	content:"\f10a";
}


.icon--email:before {
	content:"\f10b";
}


.icon--facebook:before {
	content:"\f10c";
}


.icon--googleplus:before {
	content:"\f10d";
}


.icon--info:before {
	content:"\f10e";
}


.icon--ko:before {
	content:"\f10f";
}


.icon--login:before {
	content:"\f110";
}


.icon--man:before {
	content:"\f111";
}


.icon--minus:before {
	content:"\f112";
}


.icon--ok:before {
	content:"\f113";
}


.icon--phone:before {
	content:"\f114";
}


.icon--plus:before {
	content:"\f115";
}


.icon--print:before {
	content:"\f116";
}


.icon--search:before {
	content:"\f117";
}


.icon--star:before {
	content:"\f118";
}


.icon--twitter:before {
	content:"\f119";
}


.icon--warn:before {
	content:"\f11a";
}


.icon--wheel:before {
	content:"\f11b";
}

		</style>
	</head>
	<body>
		<h1>icons</h1>

		<div class="icons" id="icons">
			
				<div class="icons__item" data-name="arrow-down"><i class="icon icon--arrow-down"></i> icon--arrow-down</div>
			
				<div class="icons__item" data-name="arrow-left"><i class="icon icon--arrow-left"></i> icon--arrow-left</div>
			
				<div class="icons__item" data-name="arrow-right"><i class="icon icon--arrow-right"></i> icon--arrow-right</div>
			
				<div class="icons__item" data-name="arrow-up"><i class="icon icon--arrow-up"></i> icon--arrow-up</div>
			
				<div class="icons__item" data-name="basket"><i class="icon icon--basket"></i> icon--basket</div>
			
				<div class="icons__item" data-name="calc"><i class="icon icon--calc"></i> icon--calc</div>
			
				<div class="icons__item" data-name="chat"><i class="icon icon--chat"></i> icon--chat</div>
			
				<div class="icons__item" data-name="close"><i class="icon icon--close"></i> icon--close</div>
			
				<div class="icons__item" data-name="dog"><i class="icon icon--dog"></i> icon--dog</div>
			
				<div class="icons__item" data-name="download"><i class="icon icon--download"></i> icon--download</div>
			
				<div class="icons__item" data-name="email"><i class="icon icon--email"></i> icon--email</div>
			
				<div class="icons__item" data-name="facebook"><i class="icon icon--facebook"></i> icon--facebook</div>
			
				<div class="icons__item" data-name="googleplus"><i class="icon icon--googleplus"></i> icon--googleplus</div>
			
				<div class="icons__item" data-name="info"><i class="icon icon--info"></i> icon--info</div>
			
				<div class="icons__item" data-name="ko"><i class="icon icon--ko"></i> icon--ko</div>
			
				<div class="icons__item" data-name="login"><i class="icon icon--login"></i> icon--login</div>
			
				<div class="icons__item" data-name="man"><i class="icon icon--man"></i> icon--man</div>
			
				<div class="icons__item" data-name="minus"><i class="icon icon--minus"></i> icon--minus</div>
			
				<div class="icons__item" data-name="ok"><i class="icon icon--ok"></i> icon--ok</div>
			
				<div class="icons__item" data-name="phone"><i class="icon icon--phone"></i> icon--phone</div>
			
				<div class="icons__item" data-name="plus"><i class="icon icon--plus"></i> icon--plus</div>
			
				<div class="icons__item" data-name="print"><i class="icon icon--print"></i> icon--print</div>
			
				<div class="icons__item" data-name="search"><i class="icon icon--search"></i> icon--search</div>
			
				<div class="icons__item" data-name="star"><i class="icon icon--star"></i> icon--star</div>
			
				<div class="icons__item" data-name="twitter"><i class="icon icon--twitter"></i> icon--twitter</div>
			
				<div class="icons__item" data-name="warn"><i class="icon icon--warn"></i> icon--warn</div>
			
				<div class="icons__item" data-name="wheel"><i class="icon icon--wheel"></i> icon--wheel</div>
			
		</div>

		

		<h1>Usage</h1>
		<pre><code>&lt;i class=&quot;icon icon--<span id="name">name</span>&quot;&gt;&lt;/i&gt;</code></pre>
		

		<footer>Generated by <a href="https://github.com/sapegin/grunt-webfont">grunt-webfont</a>.</footer>

		<script>
		(function() {
			document.getElementById('icons').onclick = function(e) {
				e = e || window.event;
				var name = e.target.getAttribute('data-name') || e.target.parentNode.getAttribute('data-name');
				document.getElementById('name').innerHTML = name;
				
			}
		})();
		</script>
	</body>
</html>
