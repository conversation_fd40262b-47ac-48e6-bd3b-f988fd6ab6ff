/* Sticky footer styles
-------------------------------------------------- */
html {
  position: relative;
  min-height: 100%;
}
body {
  /* Margin bottom by footer height */
  margin-bottom: 60px;
}
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  /* Set the fixed height of the footer here */
  height: 45px;
  background-color: #eeeeee;
}


/* Custom page CSS
-------------------------------------------------- */
/* Not required for template or sticky footer method. */

/* maximální <PERSON> str<PERSON> */
.container-fluid {
  max-width: 1200px;
}

/* padding z důvodu menu */
body { padding-top: 25px; }

/* úprava formuláře ve filtrech */
.panel-custom label {
  margin-bottom: 0;
  font-weight: normal;
}
.panel-custom .input-group {
  margin-top: 5px;
  margin-bottom: 5px;
}
.panel-custom .panel-heading {
  padding: 5px 10px;
}
.panel-custom .panel-body {
  padding: 5px 10px;
}

/* styly menu */
.navbar-inverse {
  color: #fff;
  border: none;
}
.navbar-inverse .navbar-nav>li>a {
  color: #fff;
}
.navbar-inverse .navbar-nav>li>a:hover {
  color: #f0f0f0;
}

/* úprava pozice loga */
.navbar-brand {
  padding-top: 11px;
}

/* úprava patičky */
.footer {
  padding-top: 10px;
  text-align: center;
}
.footer a {
  text-decoration: underline;
}
.footer p {
  margin-bottom: 0;
}

/* fix záložek, aby mezi nimi mohl být tag form */
.tab-content form > .tab-pane {
  display: none;
}
.tab-content form > .active {
  display: block;
}

/* změna barev ikonek */
.glyphicon-remove {
  color: #d9534f !important;
}
.glyphicon-pencil {
  color: #5cb85c !important;
}
.glyphicon-ok {
  color: #337ab7 !important;
}

/* úprava tabů */
.nav-tabs {
  margin-bottom: 25px;
}

/* doladění */
.panel label {
  margin-right: 4px;
}
.panel label input[type="submit"], .panel label input[type="checkbox"] {
  margin-right: 4px;
}