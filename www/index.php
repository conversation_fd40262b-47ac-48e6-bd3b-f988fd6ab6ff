<?php
/*
if ($_SERVER['REMOTE_ADDR'] != '86.49.166.19') {
  // Uncomment this line if you must temporarily take down your site for maintenance.
  require '.maintenance.php';
}
*/

// absolute filesystem path to the web root
define('WWW_DIR', dirname(__FILE__));

// absolute filesystem path to the application root
define('APP_DIR', WWW_DIR . '/../app');

// absolute filesystem path to the libraries
define('LIBS_DIR', WWW_DIR . '/../libs');

// absolute filesystem path to the temporary files
define('TEMP_DIR', WWW_DIR . '/../temp');

$container = require __DIR__ . '/../app/bootstrap.php';

$container->getByType(Nette\Application\Application::class)
  ->run();