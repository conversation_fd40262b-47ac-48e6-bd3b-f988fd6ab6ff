$(document).ready(function(){

  // obecne
  var bodyWidth = window.innerWidth;

  // při resize okna
  $(window).resize(function () {

    // osetreni, zda se velikost zmenila
    if ( bodyWidth !== window.innerWidth ) {
      mobileMenu();
    }

  });

  // mobilní menu
  function mobileMenu()
  {

    // šířka okna
    var bodyWidth = window.innerWidth;
    // breakpoint mobilního menu
    var respMenuWidth = 768;

    if ( bodyWidth < respMenuWidth ) {
      $('.nav').addClass('nav--mobile');
      $('.nav-product').not('.nav-product--hidden').hide();
      // skrytí košíku a přihlášení
      $('.basket__content, .login__content').hide();
    }
    else {
      $('.nav').removeClass('nav--mobile');
      $('.nav-product').not('.nav-product--hidden').show();
      $('.basket__content, .login__content').show();
    }

    // zobrazení menu
    if ( bodyWidth < respMenuWidth ) {
      $('.nav--mobile').click(function() {
        $('.basket__content, .login__content').hide();
        $('.nav-product').toggle();
        return false;
      });
    }

    // zobrazení košíku
    if ( bodyWidth < respMenuWidth ) {
      $('.basket__header').click(function() {
        $('.nav-product, .login__content').hide();
        $('.basket__content').toggle();
        return false;
      });
    }

    // zobrazení loginu
    if ( bodyWidth < respMenuWidth ) {
      $('.login__header').click(function() {
        $('.basket__content, .nav-product').hide();
        $('.login__content').toggle();
        return false;
      });
    }

  }
  mobileMenu();

  // lazy loading obrázků
  if ( $('.product__image').length ) {

    $('.product__image img').unveil(200);

  }

  // přepínání výrobců
  if ( $('.nav-product--switch').length ) {

    $('.nav-product--switch .nav-product__header').on('click', function( event ) {
      $('.nav-product--switch').removeClass('is-active');
      $(this).parent().addClass('is-active');
    });

  }

  // taby
  if ( $('.tabs').length ) {

    $('.tabs__content > div').not('.is-active').hide();
    $('.tabs__name').on('click', function( event ) {
      event.preventDefault();

      var target = $(this).attr('href');

      $('.tabs__content > .is-active').removeClass('is-active').hide();
      $(target).addClass('is-active').show();

      $('.tabs__name').removeClass('is-active');
      $(this).addClass('is-active');
    });

  }

  // otevřít tab přímo
  if ( $('.open-tab').length ) {

    $('.open-tab').on('click', function( event ) {

      $('.tabs__name').removeClass('is-active');
      $('.tabs__content > .is-active').removeClass('is-active').hide();

      var target = $(this).attr('href');

      $(target).addClass('is-active').show();
      $('.tab[href="' + target + '"]').addClass('is-active').show();

    });

  }

  // hláška s možností zavření
  if ( $('.alert--close').length ) {

    // přidání ikony
    $('.alert--close').append( ' <i class="icon icon--close"></i>' );
    // zavření okna
    $('.alert--close .icon--close').on( 'click', function() {
      $(this).parent().hide();
    });

  }

  // přičítání a odčítání počtu kusů
  if ( $('.control--count').length ) {

    $('.control--count').append( ' <span class="control control--plus"><i class="icon icon--plus"></i></span> <span class="control control--minus"><i class="icon icon--minus"></i></span>' );

    // počítání počtu kusů
    $('.control--plus').on( 'click', function() {
      // zjištění, převod na číslo a ošetření
      var quantity = $(this).parent().find('input').val();
      var quantity = parseFloat(quantity);
      if ( quantity <= 0 || !$.isNumeric(quantity) ) { quantity = 0; }
      // přičtení
      var quantity = quantity + 1;
      // nastavení čísla
      $(this).parent().find('input').val( quantity );
    });

    // odečítání počtu kusů
    $('.control--minus').on( 'click', function() {
      // zjištění, převod na číslo a ošetření
      var quantity = $(this).parent().find('input').val();
      var quantity = parseFloat(quantity);
      if ( quantity <= 0 || !$.isNumeric(quantity) ) { quantity = 1; }
      // odečtení
      var quantity = quantity - 1;
      // nastavení čísla
      $(this).parent().find('input').val( quantity );
    });

  }

  // modal okna
  if ( $('.modal').length ) {

    // otevření okna
    $('.modal--show').on( 'click', function() {

      // zjištění ID okna z atributu rel
      var modalName = '#' + $(this).attr('rel');
      var modalBody = modalName + ' .modal__body';

      // otevření konkrétního okna
      $( modalName ).show();

      // zjištění výšky okna
      var modalHeight = $(modalBody).outerHeight();

      // pokud je výška obrazovky menší než okno, resetuje se pozice
      if ( modalHeight > bodyHeight ) {
        $( modalName ).addClass('modal--reset');
      }

    });

    // zavření okna
    $('.modal__close').on( 'click', function() {
      $(this).closest('.modal').hide();
      return false;
    });

  }

  // antispam
  if ( $('.antispam').length ) {

    // zjištění ID z atributu rel
    var antispamNumber = $('.antispam').attr('data-help');

    // vypsání do inputu
    $('.antispam').find('input').val( antispamNumber );

    // skrytí celého bloku
    $('.antispam').hide();

  }

  // otevírání skrytých částí formuláře
  if ( $('.reveal').length ) {

    // skrytí částí
    $('.reveal').hide();

    // standardní odkaz (A) nebo jakýkoliv blok
    $('.reveal--show').not('label.reveal--show, input.reveal--show').on( 'click', function() {

      // zjištění ID z atributu rel
      var revealName = '#' + $(this).attr('rel');

      // zobrazení skryté části
      $( revealName ).toggle();

      return false;

    });

    // formulářový prvek...
    $('label.reveal--show, input.reveal--show').on( 'change', function() {

      // zjištění ID z atributu rel
      var revealName = '#' + $(this).attr('rel');

      // zobrazení skryté části
      $( revealName ).toggle();

    });

  }

  // vytisknout
  if ( $('.control--print').length ) {

    $(".control--print").click(function(){
      window.print();
      return false;
    });

  }

  // dopravy a platby
  if ( $('.order-delivery').length ) {

    // skrytí částí
    $('.order-delivery__payment').hide();

    // zvolení plateb po kliknutí na dopravu
    $('.order-delivery__type').on( 'click', function() {

      // skrytí zobrazených částí
      $('.order-delivery__payment').hide();

      // zjištění ID z atributu rel
      var revealName = '#' + $(this).attr('rel');

      // zobrazení skryté části
      $( revealName ).toggle();

      // pokud obsahuje radio button
      var has_checkbox = $(this).find('input[type=radio]');
      if ( $(has_checkbox).length ) {
        has_checkbox.prop('checked', true);
      }

      // automatické vybrání první volby
      $( revealName ).find('input').filter(':visible:first').trigger('click');

    });

    // při kliknutí na samotný radio button
    $('.order-delivery__type input').on( 'click', function() {
      $(this).closest('.order-delivery__type').trigger('click');
    });

    // zakliknout typ dopravy
    if ( delIdSelected!=0 ) {
      var selectedDelivery = '#delid_' + delIdSelected;
      $( selectedDelivery ).find('input').prop('checked', true);
    }

    // zakliknout a zobrazit pokud je zvolena platba
    if ( payIdSelected!=0 ) {
      var selectedPayment = 'input[value="' + payIdSelected + '"]';
      $( selectedPayment ).closest('.order-delivery__payment').show();
      $( selectedPayment ).trigger('click');
    }
    else {
      // není zvolena platba, zaklikneme první možnost
      $('.order-delivery__type').first().trigger('click');
    }

  }

  // slider (Slick)
  if ( bodyWidth > 576 && $('.slider').length ) {

    $('.slider__body').slick({
      dots: true,
      speed: 500,
      slidesToShow: 1,
      autoplay: true,
      arrows: false
    });

  }

  // lightbox (Magnific Popup)
  if ( $('.gallery').length ) {

    // český překlad
    $.extend(true, $.magnificPopup.defaults, {
      tClose: 'Zavřít',
      tLoading: 'Nahrávám...',
      gallery: {
        tPrev: 'Předchozí',
        tNext: 'Následující',
        tCounter: '%curr% z %total%'
      },
      image: {
        tError: '<a href="%url%">Obrázek</a> nelze načíst.'
      },
      ajax: {
        tError: '<a href="%url%">Obsah</a> nelze načíst.'
      }
    });

    $('.gallery').magnificPopup({
      delegate: 'a',
      type: 'image',
      removalDelay: 300,
      mainClass: 'mfp-fade',
      gallery: {
        enabled: true,
        tCounter: ''
      }
    });

  }

});
