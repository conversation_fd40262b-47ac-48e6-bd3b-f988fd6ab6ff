!function(a){a.fn.unveil=function(b,c){function d(){var b=j.filter(function(){var b=a(this);if(!b.is(":hidden")){var c=f.scrollTop(),d=c+f.height(),e=b.offset().top;return e+b.height()>=c-g&&e<=d+g}});e=b.trigger("unveil"),j=j.not(e)}var e,f=a(window),g=b||0,h=window.devicePixelRatio>1,i=h?"data-src-retina":"data-src",j=this;return this.one("unveil",function(){var a=this.getAttribute(i);(a=a||this.getAttribute("data-src"))&&(this.setAttribute("src",a),"function"==typeof c&&c.call(this))}),f.on("scroll.unveil resize.unveil lookup.unveil",d),d(),this}}(window.jQuery||window.Zepto),function(a){"function"==typeof define&&define.amd?define(["jquery"],a):a("object"==typeof exports?require("jquery"):window.jQuery||window.Zepto)}(function(a){var b,c,d,e,f,g,h=function(){},i=!!window.jQuery,j=a(window),k=function(a,c){b.ev.on("mfp"+a+".mfp",c)},l=function(b,c,d,e){var f=document.createElement("div");return f.className="mfp-"+b,d&&(f.innerHTML=d),e?c&&c.appendChild(f):(f=a(f),c&&f.appendTo(c)),f},m=function(c,d){b.ev.triggerHandler("mfp"+c,d),b.st.callbacks&&(c=c.charAt(0).toLowerCase()+c.slice(1),b.st.callbacks[c]&&b.st.callbacks[c].apply(b,a.isArray(d)?d:[d]))},n=function(c){return c===g&&b.currTemplate.closeBtn||(b.currTemplate.closeBtn=a(b.st.closeMarkup.replace("%title%",b.st.tClose)),g=c),b.currTemplate.closeBtn},o=function(){a.magnificPopup.instance||(b=new h,b.init(),a.magnificPopup.instance=b)},p=function(){var a=document.createElement("p").style,b=["ms","O","Moz","Webkit"];if(void 0!==a.transition)return!0;for(;b.length;)if(b.pop()+"Transition"in a)return!0;return!1};h.prototype={constructor:h,init:function(){var c=navigator.appVersion;b.isLowIE=b.isIE8=document.all&&!document.addEventListener,b.isAndroid=/android/gi.test(c),b.isIOS=/iphone|ipad|ipod/gi.test(c),b.supportsTransition=p(),b.probablyMobile=b.isAndroid||b.isIOS||/(Opera Mini)|Kindle|webOS|BlackBerry|(Opera Mobi)|(Windows Phone)|IEMobile/i.test(navigator.userAgent),d=a(document),b.popupsCache={}},open:function(c){var e;if(!1===c.isObj){b.items=c.items.toArray(),b.index=0;var g,h=c.items;for(e=0;e<h.length;e++)if(g=h[e],g.parsed&&(g=g.el[0]),g===c.el[0]){b.index=e;break}}else b.items=a.isArray(c.items)?c.items:[c.items],b.index=c.index||0;if(b.isOpen)return void b.updateItemHTML();b.types=[],f="",c.mainEl&&c.mainEl.length?b.ev=c.mainEl.eq(0):b.ev=d,c.key?(b.popupsCache[c.key]||(b.popupsCache[c.key]={}),b.currTemplate=b.popupsCache[c.key]):b.currTemplate={},b.st=a.extend(!0,{},a.magnificPopup.defaults,c),b.fixedContentPos="auto"===b.st.fixedContentPos?!b.probablyMobile:b.st.fixedContentPos,b.st.modal&&(b.st.closeOnContentClick=!1,b.st.closeOnBgClick=!1,b.st.showCloseBtn=!1,b.st.enableEscapeKey=!1),b.bgOverlay||(b.bgOverlay=l("bg").on("click.mfp",function(){b.close()}),b.wrap=l("wrap").attr("tabindex",-1).on("click.mfp",function(a){b._checkIfClose(a.target)&&b.close()}),b.container=l("container",b.wrap)),b.contentContainer=l("content"),b.st.preloader&&(b.preloader=l("preloader",b.container,b.st.tLoading));var i=a.magnificPopup.modules;for(e=0;e<i.length;e++){var o=i[e];o=o.charAt(0).toUpperCase()+o.slice(1),b["init"+o].call(b)}m("BeforeOpen"),b.st.showCloseBtn&&(b.st.closeBtnInside?(k("MarkupParse",function(a,b,c,d){c.close_replaceWith=n(d.type)}),f+=" mfp-close-btn-in"):b.wrap.append(n())),b.st.alignTop&&(f+=" mfp-align-top"),b.fixedContentPos?b.wrap.css({overflow:b.st.overflowY,overflowX:"hidden",overflowY:b.st.overflowY}):b.wrap.css({top:j.scrollTop(),position:"absolute"}),(!1===b.st.fixedBgPos||"auto"===b.st.fixedBgPos&&!b.fixedContentPos)&&b.bgOverlay.css({height:d.height(),position:"absolute"}),b.st.enableEscapeKey&&d.on("keyup.mfp",function(a){27===a.keyCode&&b.close()}),j.on("resize.mfp",function(){b.updateSize()}),b.st.closeOnContentClick||(f+=" mfp-auto-cursor"),f&&b.wrap.addClass(f);var p=b.wH=j.height(),q={};if(b.fixedContentPos&&b._hasScrollBar(p)){var r=b._getScrollbarSize();r&&(q.marginRight=r)}b.fixedContentPos&&(b.isIE7?a("body, html").css("overflow","hidden"):q.overflow="hidden");var s=b.st.mainClass;return b.isIE7&&(s+=" mfp-ie7"),s&&b._addClassToMFP(s),b.updateItemHTML(),m("BuildControls"),a("html").css(q),b.bgOverlay.add(b.wrap).prependTo(b.st.prependTo||a(document.body)),b._lastFocusedEl=document.activeElement,setTimeout(function(){b.content?(b._addClassToMFP("mfp-ready"),b._setFocus()):b.bgOverlay.addClass("mfp-ready"),d.on("focusin.mfp",b._onFocusIn)},16),b.isOpen=!0,b.updateSize(p),m("Open"),c},close:function(){b.isOpen&&(m("BeforeClose"),b.isOpen=!1,b.st.removalDelay&&!b.isLowIE&&b.supportsTransition?(b._addClassToMFP("mfp-removing"),setTimeout(function(){b._close()},b.st.removalDelay)):b._close())},_close:function(){m("Close");var c="mfp-removing mfp-ready ";if(b.bgOverlay.detach(),b.wrap.detach(),b.container.empty(),b.st.mainClass&&(c+=b.st.mainClass+" "),b._removeClassFromMFP(c),b.fixedContentPos){var e={marginRight:""};b.isIE7?a("body, html").css("overflow",""):e.overflow="",a("html").css(e)}d.off("keyup.mfp focusin.mfp"),b.ev.off(".mfp"),b.wrap.attr("class","mfp-wrap").removeAttr("style"),b.bgOverlay.attr("class","mfp-bg"),b.container.attr("class","mfp-container"),!b.st.showCloseBtn||b.st.closeBtnInside&&!0!==b.currTemplate[b.currItem.type]||b.currTemplate.closeBtn&&b.currTemplate.closeBtn.detach(),b.st.autoFocusLast&&b._lastFocusedEl&&a(b._lastFocusedEl).focus(),b.currItem=null,b.content=null,b.currTemplate=null,b.prevHeight=0,m("AfterClose")},updateSize:function(a){if(b.isIOS){var c=document.documentElement.clientWidth/window.innerWidth,d=window.innerHeight*c;b.wrap.css("height",d),b.wH=d}else b.wH=a||j.height();b.fixedContentPos||b.wrap.css("height",b.wH),m("Resize")},updateItemHTML:function(){var c=b.items[b.index];b.contentContainer.detach(),b.content&&b.content.detach(),c.parsed||(c=b.parseEl(b.index));var d=c.type;if(m("BeforeChange",[b.currItem?b.currItem.type:"",d]),b.currItem=c,!b.currTemplate[d]){var f=!!b.st[d]&&b.st[d].markup;m("FirstMarkupParse",f),b.currTemplate[d]=!f||a(f)}e&&e!==c.type&&b.container.removeClass("mfp-"+e+"-holder");var g=b["get"+d.charAt(0).toUpperCase()+d.slice(1)](c,b.currTemplate[d]);b.appendContent(g,d),c.preloaded=!0,m("Change",c),e=c.type,b.container.prepend(b.contentContainer),m("AfterChange")},appendContent:function(a,c){b.content=a,a?b.st.showCloseBtn&&b.st.closeBtnInside&&!0===b.currTemplate[c]?b.content.find(".mfp-close").length||b.content.append(n()):b.content=a:b.content="",m("BeforeAppend"),b.container.addClass("mfp-"+c+"-holder"),b.contentContainer.append(b.content)},parseEl:function(c){var d,e=b.items[c];if(e.tagName?e={el:a(e)}:(d=e.type,e={data:e,src:e.src}),e.el){for(var f=b.types,g=0;g<f.length;g++)if(e.el.hasClass("mfp-"+f[g])){d=f[g];break}e.src=e.el.attr("data-mfp-src"),e.src||(e.src=e.el.attr("href"))}return e.type=d||b.st.type||"inline",e.index=c,e.parsed=!0,b.items[c]=e,m("ElementParse",e),b.items[c]},addGroup:function(a,c){var d=function(d){d.mfpEl=this,b._openClick(d,a,c)};c||(c={});var e="click.magnificPopup";c.mainEl=a,c.items?(c.isObj=!0,a.off(e).on(e,d)):(c.isObj=!1,c.delegate?a.off(e).on(e,c.delegate,d):(c.items=a,a.off(e).on(e,d)))},_openClick:function(c,d,e){if((void 0!==e.midClick?e.midClick:a.magnificPopup.defaults.midClick)||!(2===c.which||c.ctrlKey||c.metaKey||c.altKey||c.shiftKey)){var f=void 0!==e.disableOn?e.disableOn:a.magnificPopup.defaults.disableOn;if(f)if(a.isFunction(f)){if(!f.call(b))return!0}else if(j.width()<f)return!0;c.type&&(c.preventDefault(),b.isOpen&&c.stopPropagation()),e.el=a(c.mfpEl),e.delegate&&(e.items=d.find(e.delegate)),b.open(e)}},updateStatus:function(a,d){if(b.preloader){c!==a&&b.container.removeClass("mfp-s-"+c),d||"loading"!==a||(d=b.st.tLoading);var e={status:a,text:d};m("UpdateStatus",e),a=e.status,d=e.text,b.preloader.html(d),b.preloader.find("a").on("click",function(a){a.stopImmediatePropagation()}),b.container.addClass("mfp-s-"+a),c=a}},_checkIfClose:function(c){if(!a(c).hasClass("mfp-prevent-close")){var d=b.st.closeOnContentClick,e=b.st.closeOnBgClick;if(d&&e)return!0;if(!b.content||a(c).hasClass("mfp-close")||b.preloader&&c===b.preloader[0])return!0;if(c===b.content[0]||a.contains(b.content[0],c)){if(d)return!0}else if(e&&a.contains(document,c))return!0;return!1}},_addClassToMFP:function(a){b.bgOverlay.addClass(a),b.wrap.addClass(a)},_removeClassFromMFP:function(a){this.bgOverlay.removeClass(a),b.wrap.removeClass(a)},_hasScrollBar:function(a){return(b.isIE7?d.height():document.body.scrollHeight)>(a||j.height())},_setFocus:function(){(b.st.focus?b.content.find(b.st.focus).eq(0):b.wrap).focus()},_onFocusIn:function(c){if(c.target!==b.wrap[0]&&!a.contains(b.wrap[0],c.target))return b._setFocus(),!1},_parseMarkup:function(b,c,d){var e;d.data&&(c=a.extend(d.data,c)),m("MarkupParse",[b,c,d]),a.each(c,function(c,d){if(void 0===d||!1===d)return!0;if(e=c.split("_"),e.length>1){var f=b.find(".mfp-"+e[0]);if(f.length>0){var g=e[1];"replaceWith"===g?f[0]!==d[0]&&f.replaceWith(d):"img"===g?f.is("img")?f.attr("src",d):f.replaceWith(a("<img>").attr("src",d).attr("class",f.attr("class"))):f.attr(e[1],d)}}else b.find(".mfp-"+c).html(d)})},_getScrollbarSize:function(){if(void 0===b.scrollbarSize){var a=document.createElement("div");a.style.cssText="width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;",document.body.appendChild(a),b.scrollbarSize=a.offsetWidth-a.clientWidth,document.body.removeChild(a)}return b.scrollbarSize}},a.magnificPopup={instance:null,proto:h.prototype,modules:[],open:function(b,c){return o(),b=b?a.extend(!0,{},b):{},b.isObj=!0,b.index=c||0,this.instance.open(b)},close:function(){return a.magnificPopup.instance&&a.magnificPopup.instance.close()},registerModule:function(b,c){c.options&&(a.magnificPopup.defaults[b]=c.options),a.extend(this.proto,c.proto),this.modules.push(b)},defaults:{disableOn:0,key:null,midClick:!1,mainClass:"",preloader:!0,focus:"",closeOnContentClick:!1,closeOnBgClick:!0,closeBtnInside:!0,showCloseBtn:!0,enableEscapeKey:!0,modal:!1,alignTop:!1,removalDelay:0,prependTo:null,fixedContentPos:"auto",fixedBgPos:"auto",overflowY:"auto",closeMarkup:'<button title="%title%" type="button" class="mfp-close">&#215;</button>',tClose:"Close (Esc)",tLoading:"Loading...",autoFocusLast:!0}},a.fn.magnificPopup=function(c){o();var d=a(this);if("string"==typeof c)if("open"===c){var e,f=i?d.data("magnificPopup"):d[0].magnificPopup,g=parseInt(arguments[1],10)||0;f.items?e=f.items[g]:(e=d,f.delegate&&(e=e.find(f.delegate)),e=e.eq(g)),b._openClick({mfpEl:e},d,f)}else b.isOpen&&b[c].apply(b,Array.prototype.slice.call(arguments,1));else c=a.extend(!0,{},c),i?d.data("magnificPopup",c):d[0].magnificPopup=c,b.addGroup(d,c);return d};var q,r,s,t=function(){s&&(r.after(s.addClass(q)).detach(),s=null)};a.magnificPopup.registerModule("inline",{options:{hiddenClass:"hide",markup:"",tNotFound:"Content not found"},proto:{initInline:function(){b.types.push("inline"),k("Close.inline",function(){t()})},getInline:function(c,d){if(t(),c.src){var e=b.st.inline,f=a(c.src);if(f.length){var g=f[0].parentNode;g&&g.tagName&&(r||(q=e.hiddenClass,r=l(q),q="mfp-"+q),s=f.after(r).detach().removeClass(q)),b.updateStatus("ready")}else b.updateStatus("error",e.tNotFound),f=a("<div>");return c.inlineElement=f,f}return b.updateStatus("ready"),b._parseMarkup(d,{},c),d}}});var u,v=function(){u&&a(document.body).removeClass(u)},w=function(){v(),b.req&&b.req.abort()};a.magnificPopup.registerModule("ajax",{options:{settings:null,cursor:"mfp-ajax-cur",tError:'<a href="%url%">The content</a> could not be loaded.'},proto:{initAjax:function(){b.types.push("ajax"),u=b.st.ajax.cursor,k("Close.ajax",w),k("BeforeChange.ajax",w)},getAjax:function(c){u&&a(document.body).addClass(u),b.updateStatus("loading");var d=a.extend({url:c.src,success:function(d,e,f){var g={data:d,xhr:f};m("ParseAjax",g),b.appendContent(a(g.data),"ajax"),c.finished=!0,v(),b._setFocus(),setTimeout(function(){b.wrap.addClass("mfp-ready")},16),b.updateStatus("ready"),m("AjaxContentAdded")},error:function(){v(),c.finished=c.loadError=!0,b.updateStatus("error",b.st.ajax.tError.replace("%url%",c.src))}},b.st.ajax.settings);return b.req=a.ajax(d),""}}});var x,y=function(c){if(c.data&&void 0!==c.data.title)return c.data.title;var d=b.st.image.titleSrc;if(d){if(a.isFunction(d))return d.call(b,c);if(c.el)return c.el.attr(d)||""}return""};a.magnificPopup.registerModule("image",{options:{markup:'<div class="mfp-figure"><div class="mfp-close"></div><figure><div class="mfp-img"></div><figcaption><div class="mfp-bottom-bar"><div class="mfp-title"></div><div class="mfp-counter"></div></div></figcaption></figure></div>',cursor:"mfp-zoom-out-cur",titleSrc:"title",verticalFit:!0,tError:'<a href="%url%">The image</a> could not be loaded.'},proto:{initImage:function(){var c=b.st.image,d=".image";b.types.push("image"),k("Open"+d,function(){"image"===b.currItem.type&&c.cursor&&a(document.body).addClass(c.cursor)}),k("Close"+d,function(){c.cursor&&a(document.body).removeClass(c.cursor),j.off("resize.mfp")}),k("Resize"+d,b.resizeImage),b.isLowIE&&k("AfterChange",b.resizeImage)},resizeImage:function(){var a=b.currItem;if(a&&a.img&&b.st.image.verticalFit){var c=0;b.isLowIE&&(c=parseInt(a.img.css("padding-top"),10)+parseInt(a.img.css("padding-bottom"),10)),a.img.css("max-height",b.wH-c)}},_onImageHasSize:function(a){a.img&&(a.hasSize=!0,x&&clearInterval(x),a.isCheckingImgSize=!1,m("ImageHasSize",a),a.imgHidden&&(b.content&&b.content.removeClass("mfp-loading"),a.imgHidden=!1))},findImageSize:function(a){var c=0,d=a.img[0],e=function(f){x&&clearInterval(x),x=setInterval(function(){if(d.naturalWidth>0)return void b._onImageHasSize(a);c>200&&clearInterval(x),c++,3===c?e(10):40===c?e(50):100===c&&e(500)},f)};e(1)},getImage:function(c,d){var e=0,f=function(){c&&(c.img[0].complete?(c.img.off(".mfploader"),c===b.currItem&&(b._onImageHasSize(c),b.updateStatus("ready")),c.hasSize=!0,c.loaded=!0,m("ImageLoadComplete")):(e++,e<200?setTimeout(f,100):g()))},g=function(){c&&(c.img.off(".mfploader"),c===b.currItem&&(b._onImageHasSize(c),b.updateStatus("error",h.tError.replace("%url%",c.src))),c.hasSize=!0,c.loaded=!0,c.loadError=!0)},h=b.st.image,i=d.find(".mfp-img");if(i.length){var j=document.createElement("img");j.className="mfp-img",c.el&&c.el.find("img").length&&(j.alt=c.el.find("img").attr("alt")),c.img=a(j).on("load.mfploader",f).on("error.mfploader",g),j.src=c.src,i.is("img")&&(c.img=c.img.clone()),j=c.img[0],j.naturalWidth>0?c.hasSize=!0:j.width||(c.hasSize=!1)}return b._parseMarkup(d,{title:y(c),img_replaceWith:c.img},c),b.resizeImage(),c.hasSize?(x&&clearInterval(x),c.loadError?(d.addClass("mfp-loading"),b.updateStatus("error",h.tError.replace("%url%",c.src))):(d.removeClass("mfp-loading"),b.updateStatus("ready")),d):(b.updateStatus("loading"),c.loading=!0,c.hasSize||(c.imgHidden=!0,d.addClass("mfp-loading"),b.findImageSize(c)),d)}}});var z,A=function(){return void 0===z&&(z=void 0!==document.createElement("p").style.MozTransform),z};a.magnificPopup.registerModule("zoom",{options:{enabled:!1,easing:"ease-in-out",duration:300,opener:function(a){return a.is("img")?a:a.find("img")}},proto:{initZoom:function(){var a,c=b.st.zoom,d=".zoom";if(c.enabled&&b.supportsTransition){var e,f,g=c.duration,h=function(a){var b=a.clone().removeAttr("style").removeAttr("class").addClass("mfp-animated-image"),d="all "+c.duration/1e3+"s "+c.easing,e={position:"fixed",zIndex:9999,left:0,top:0,"-webkit-backface-visibility":"hidden"},f="transition";return e["-webkit-"+f]=e["-moz-"+f]=e["-o-"+f]=e[f]=d,b.css(e),b},i=function(){b.content.css("visibility","visible")};k("BuildControls"+d,function(){if(b._allowZoom()){if(clearTimeout(e),b.content.css("visibility","hidden"),!(a=b._getItemToZoom()))return void i();f=h(a),f.css(b._getOffset()),b.wrap.append(f),e=setTimeout(function(){f.css(b._getOffset(!0)),e=setTimeout(function(){i(),setTimeout(function(){f.remove(),a=f=null,m("ZoomAnimationEnded")},16)},g)},16)}}),k("BeforeClose"+d,function(){if(b._allowZoom()){if(clearTimeout(e),b.st.removalDelay=g,!a){if(!(a=b._getItemToZoom()))return;f=h(a)}f.css(b._getOffset(!0)),b.wrap.append(f),b.content.css("visibility","hidden"),setTimeout(function(){f.css(b._getOffset())},16)}}),k("Close"+d,function(){b._allowZoom()&&(i(),f&&f.remove(),a=null)})}},_allowZoom:function(){return"image"===b.currItem.type},_getItemToZoom:function(){return!!b.currItem.hasSize&&b.currItem.img},_getOffset:function(c){var d;d=c?b.currItem.img:b.st.zoom.opener(b.currItem.el||b.currItem);var e=d.offset(),f=parseInt(d.css("padding-top"),10),g=parseInt(d.css("padding-bottom"),10);e.top-=a(window).scrollTop()-f;var h={width:d.width(),height:(i?d.innerHeight():d[0].offsetHeight)-g-f};return A()?h["-moz-transform"]=h.transform="translate("+e.left+"px,"+e.top+"px)":(h.left=e.left,h.top=e.top),h}}});var B=function(a){if(b.currTemplate.iframe){var c=b.currTemplate.iframe.find("iframe");c.length&&(a||(c[0].src="//about:blank"),b.isIE8&&c.css("display",a?"block":"none"))}};a.magnificPopup.registerModule("iframe",{options:{markup:'<div class="mfp-iframe-scaler"><div class="mfp-close"></div><iframe class="mfp-iframe" src="//about:blank" frameborder="0" allowfullscreen></iframe></div>',srcAction:"iframe_src",patterns:{youtube:{index:"youtube.com",id:"v=",src:"//www.youtube.com/embed/%id%?autoplay=1"},vimeo:{index:"vimeo.com/",id:"/",src:"//player.vimeo.com/video/%id%?autoplay=1"},gmaps:{index:"//maps.google.",src:"%id%&output=embed"}}},proto:{initIframe:function(){b.types.push("iframe"),k("BeforeChange",function(a,b,c){b!==c&&("iframe"===b?B():"iframe"===c&&B(!0))}),k("Close.iframe",function(){B()})},getIframe:function(c,d){var e=c.src,f=b.st.iframe;a.each(f.patterns,function(){if(e.indexOf(this.index)>-1)return this.id&&(e="string"==typeof this.id?e.substr(e.lastIndexOf(this.id)+this.id.length,e.length):this.id.call(this,e)),e=this.src.replace("%id%",e),!1});var g={};return f.srcAction&&(g[f.srcAction]=e),b._parseMarkup(d,g,c),b.updateStatus("ready"),d}}});var C=function(a){var c=b.items.length;return a>c-1?a-c:a<0?c+a:a},D=function(a,b,c){return a.replace(/%curr%/gi,b+1).replace(/%total%/gi,c)};a.magnificPopup.registerModule("gallery",{options:{enabled:!1,arrowMarkup:'<button title="%title%" type="button" class="mfp-arrow mfp-arrow-%dir%"></button>',preload:[0,2],navigateByImgClick:!0,arrows:!0,tPrev:"Previous (Left arrow key)",tNext:"Next (Right arrow key)",tCounter:"%curr% of %total%"},proto:{initGallery:function(){var c=b.st.gallery,e=".mfp-gallery";if(b.direction=!0,!c||!c.enabled)return!1;f+=" mfp-gallery",k("Open"+e,function(){c.navigateByImgClick&&b.wrap.on("click"+e,".mfp-img",function(){if(b.items.length>1)return b.next(),!1}),d.on("keydown"+e,function(a){37===a.keyCode?b.prev():39===a.keyCode&&b.next()})}),k("UpdateStatus"+e,function(a,c){c.text&&(c.text=D(c.text,b.currItem.index,b.items.length))}),k("MarkupParse"+e,function(a,d,e,f){var g=b.items.length;e.counter=g>1?D(c.tCounter,f.index,g):""}),k("BuildControls"+e,function(){if(b.items.length>1&&c.arrows&&!b.arrowLeft){var d=c.arrowMarkup,e=b.arrowLeft=a(d.replace(/%title%/gi,c.tPrev).replace(/%dir%/gi,"left")).addClass("mfp-prevent-close"),f=b.arrowRight=a(d.replace(/%title%/gi,c.tNext).replace(/%dir%/gi,"right")).addClass("mfp-prevent-close");e.click(function(){b.prev()}),f.click(function(){b.next()}),b.container.append(e.add(f))}}),k("Change"+e,function(){b._preloadTimeout&&clearTimeout(b._preloadTimeout),b._preloadTimeout=setTimeout(function(){b.preloadNearbyImages(),b._preloadTimeout=null},16)}),k("Close"+e,function(){d.off(e),b.wrap.off("click"+e),b.arrowRight=b.arrowLeft=null})},next:function(){b.direction=!0,b.index=C(b.index+1),b.updateItemHTML()},prev:function(){b.direction=!1,b.index=C(b.index-1),b.updateItemHTML()},goTo:function(a){b.direction=a>=b.index,b.index=a,b.updateItemHTML()},preloadNearbyImages:function(){var a,c=b.st.gallery.preload,d=Math.min(c[0],b.items.length),e=Math.min(c[1],b.items.length);for(a=1;a<=(b.direction?e:d);a++)b._preloadItem(b.index+a);for(a=1;a<=(b.direction?d:e);a++)b._preloadItem(b.index-a)},_preloadItem:function(c){if(c=C(c),!b.items[c].preloaded){var d=b.items[c];d.parsed||(d=b.parseEl(c)),m("LazyLoad",d),"image"===d.type&&(d.img=a('<img class="mfp-img" />').on("load.mfploader",function(){d.hasSize=!0}).on("error.mfploader",function(){d.hasSize=!0,d.loadError=!0,m("LazyLoadError",d)}).attr("src",d.src)),d.preloaded=!0}}}});a.magnificPopup.registerModule("retina",{options:{replaceSrc:function(a){return a.src.replace(/\.\w+$/,function(a){return"@2x"+a})},ratio:1},proto:{initRetina:function(){if(window.devicePixelRatio>1){var a=b.st.retina,c=a.ratio;c=isNaN(c)?c():c,c>1&&(k("ImageHasSize.retina",function(a,b){b.img.css({"max-width":b.img[0].naturalWidth/c,width:"100%"})}),k("ElementParse.retina",function(b,d){d.src=a.replaceSrc(d,c)}))}}}}),o()}),function(a,b){if(a.JSON)if("function"==typeof define&&define.amd)define(function(){return b(a)});else if("object"==typeof module&&"object"==typeof module.exports)module.exports=b(a);else{var c=!a.Nette||!a.Nette.noInit;a.Nette=b(a),c&&a.Nette.initOnLoad()}}("undefined"!=typeof window?window:this,function(window){"use strict";function getHandler(a){return function(b){return a.call(this,b)}}var Nette={};Nette.formErrors=[],Nette.version="2.4",Nette.addEvent=function(a,b,c){a.addEventListener?a.addEventListener(b,c):"DOMContentLoaded"===b?a.attachEvent("onreadystatechange",function(){"complete"===a.readyState&&c.call(this)}):a.attachEvent("on"+b,getHandler(c))},Nette.getValue=function(a){var b;if(a){if(a.tagName){if("radio"===a.type){var c=a.form.elements;for(b=0;b<c.length;b++)if(c[b].name===a.name&&c[b].checked)return c[b].value;return null}if("file"===a.type)return a.files||a.value;if("select"===a.tagName.toLowerCase()){var d=a.selectedIndex,e=a.options,f=[];if("select-one"===a.type)return d<0?null:e[d].value;for(b=0;b<e.length;b++)e[b].selected&&f.push(e[b].value);return f}if(a.name&&a.name.match(/\[\]$/)){var c=a.form.elements[a.name].tagName?[a]:a.form.elements[a.name],f=[];for(b=0;b<c.length;b++)("checkbox"!==c[b].type||c[b].checked)&&f.push(c[b].value);return f}return"checkbox"===a.type?a.checked:"textarea"===a.tagName.toLowerCase()?a.value.replace("\r",""):a.value.replace("\r","").replace(/^\s+|\s+$/g,"")}return a[0]?Nette.getValue(a[0]):null}return null},Nette.getEffectiveValue=function(a){var b=Nette.getValue(a);return a.getAttribute&&b===a.getAttribute("data-nette-empty-value")&&(b=""),b},Nette.validateControl=function(a,b,c,d,e){a=a.tagName?a:a[0],b=b||Nette.parseJSON(a.getAttribute("data-nette-rules")),d=void 0===d?{value:Nette.getEffectiveValue(a)}:d;for(var f=0,g=b.length;f<g;f++){var h=b[f],i=h.op.match(/(~)?([^?]+)/),j=h.control?a.form.elements.namedItem(h.control):a;if(h.neg=i[1],h.op=i[2],h.condition=!!h.rules,j)if("optional"!==h.op){if(!e||h.condition||":filled"===h.op){j=j.tagName?j:j[0];var k=a===j?d:{value:Nette.getEffectiveValue(j)},l=Nette.validateRule(j,h.op,h.arg,k);if(null!==l)if(h.neg&&(l=!l),h.condition&&l){if(!Nette.validateControl(a,h.rules,c,d,":blank"!==h.op&&e))return!1}else if(!h.condition&&!l){if(Nette.isDisabled(j))continue;if(!c){var m=Nette.isArray(h.arg)?h.arg:[h.arg],n=h.msg.replace(/%(value|\d+)/g,function(b,c){return Nette.getValue("value"===c?j:a.form.elements.namedItem(m[c].control))});Nette.addError(j,n)}return!1}}}else e=!Nette.validateRule(a,":filled",null,d)}return!("number"===a.type&&!a.validity.valid)||(c||Nette.addError(a,"Please enter a valid value."),!1)},Nette.validateForm=function(a,b){var c=a.form||a,d=!1;if(Nette.formErrors=[],c["nette-submittedBy"]&&null!==c["nette-submittedBy"].getAttribute("formnovalidate")){var e=Nette.parseJSON(c["nette-submittedBy"].getAttribute("data-nette-validation-scope"));if(!e.length)return Nette.showFormErrors(c,[]),!0;d=new RegExp("^("+e.join("-|")+"-)")}var f,g,h={};for(f=0;f<c.elements.length;f++)if(g=c.elements[f],!g.tagName||g.tagName.toLowerCase()in{input:1,select:1,textarea:1,button:1}){if("radio"===g.type){if(h[g.name])continue;h[g.name]=!0}if(!(d&&!g.name.replace(/]\[|\[|]|$/g,"-").match(d)||Nette.isDisabled(g)||Nette.validateControl(g,null,b)||Nette.formErrors.length))return!1}var i=!Nette.formErrors.length;return Nette.showFormErrors(c,Nette.formErrors),i},Nette.isDisabled=function(a){if("radio"===a.type){for(var b=0,c=a.form.elements;b<c.length;b++)if(c[b].name===a.name&&!c[b].disabled)return!1;return!0}return a.disabled},Nette.addError=function(a,b){Nette.formErrors.push({element:a,message:b})},Nette.showFormErrors=function(a,b){for(var c,d=[],e=0;e<b.length;e++){var f=b[e].element,g=b[e].message;Nette.inArray(d,g)||(d.push(g),!c&&f.focus&&(c=f))}d.length&&(alert(d.join("\n")),c&&c.focus())},Nette.expandRuleArgument=function(a,b){if(b&&b.control){var c=a.elements.namedItem(b.control),d={value:Nette.getEffectiveValue(c)};Nette.validateControl(c,null,!0,d),b=d.value}return b};var preventFiltering=!1;return Nette.validateRule=function(a,b,c,d){d=void 0===d?{value:Nette.getEffectiveValue(a)}:d,":"===b.charAt(0)&&(b=b.substr(1)),b=b.replace("::","_"),b=b.replace(/\\/g,"");var e=Nette.isArray(c)?c.slice(0):[c];if(!preventFiltering){preventFiltering=!0;for(var f=0,g=e.length;f<g;f++)e[f]=Nette.expandRuleArgument(a.form,e[f]);preventFiltering=!1}return Nette.validators[b]?Nette.validators[b](a,Nette.isArray(c)?e:e[0],d.value,d):null},Nette.validators={filled:function(a,b,c){return!("number"!==a.type||!a.validity.badInput)||""!==c&&!1!==c&&null!==c&&(!Nette.isArray(c)||!!c.length)&&(!window.FileList||!(c instanceof window.FileList)||c.length)},blank:function(a,b,c){return!Nette.validators.filled(a,b,c)},valid:function(a){return Nette.validateControl(a,null,!0)},equal:function(a,b,c){function d(a){return"number"==typeof a||"string"==typeof a?""+a:!0===a?"1":""}if(void 0===b)return null;c=Nette.isArray(c)?c:[c],b=Nette.isArray(b)?b:[b];a:for(var e=0,f=c.length;e<f;e++){for(var g=0,h=b.length;g<h;g++)if(d(c[e])===d(b[g]))continue a;return!1}return!0},notEqual:function(a,b,c){return void 0===b?null:!Nette.validators.equal(a,b,c)},minLength:function(a,b,c){if("number"===a.type){if(a.validity.tooShort)return!1;if(a.validity.badInput)return null}return c.length>=b},maxLength:function(a,b,c){if("number"===a.type){if(a.validity.tooLong)return!1;if(a.validity.badInput)return null}return c.length<=b},length:function(a,b,c){if("number"===a.type){if(a.validity.tooShort||a.validity.tooLong)return!1;if(a.validity.badInput)return null}return b=Nette.isArray(b)?b:[b,b],(null===b[0]||c.length>=b[0])&&(null===b[1]||c.length<=b[1])},email:function(a,b,c){return/^("([ !#-[\]-~]|\\[ -~])+"|[-a-z0-9!#$%&'*+\/=?^_`{|}~]+(\.[-a-z0-9!#$%&'*+\/=?^_`{|}~]+)*)@([0-9a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,61}[0-9a-z\u00C0-\u02FF\u0370-\u1EFF])?\.)+[a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,17}[a-z\u00C0-\u02FF\u0370-\u1EFF])?$/i.test(c)},url:function(a,b,c,d){return/^[a-z\d+.-]+:/.test(c)||(c="http://"+c),!!/^https?:\/\/((([-_0-9a-z\u00C0-\u02FF\u0370-\u1EFF]+\.)*[0-9a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,61}[0-9a-z\u00C0-\u02FF\u0370-\u1EFF])?\.)?[a-z\u00C0-\u02FF\u0370-\u1EFF]([-0-9a-z\u00C0-\u02FF\u0370-\u1EFF]{0,17}[a-z\u00C0-\u02FF\u0370-\u1EFF])?|\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}|\[[0-9a-f:]{3,39}\])(:\d{1,5})?(\/\S*)?$/i.test(c)&&(d.value=c,!0)},regexp:function(a,b,c){var d="string"==typeof b&&b.match(/^\/(.*)\/([imu]*)$/);try{return d&&new RegExp(d[1],d[2].replace("u","")).test(c)}catch(a){}},pattern:function(a,b,c){try{return"string"==typeof b?new RegExp("^(?:"+b+")$").test(c):null}catch(a){}},integer:function(a,b,c){return("number"!==a.type||!a.validity.badInput)&&/^-?[0-9]+$/.test(c)},float:function(a,b,c,d){return("number"!==a.type||!a.validity.badInput)&&(c=c.replace(" ","").replace(",","."),!!/^-?[0-9]*[.,]?[0-9]+$/.test(c)&&(d.value=c,!0))},min:function(a,b,c){if("number"===a.type){if(a.validity.rangeUnderflow)return!1;if(a.validity.badInput)return null}return null===b||parseFloat(c)>=b},max:function(a,b,c){if("number"===a.type){if(a.validity.rangeOverflow)return!1;if(a.validity.badInput)return null}return null===b||parseFloat(c)<=b},range:function(a,b,c){if("number"===a.type){if(a.validity.rangeUnderflow||a.validity.rangeOverflow)return!1;if(a.validity.badInput)return null}return Nette.isArray(b)?(null===b[0]||parseFloat(c)>=b[0])&&(null===b[1]||parseFloat(c)<=b[1]):null},submitted:function(a){return a.form["nette-submittedBy"]===a},fileSize:function(a,b,c){if(window.FileList)for(var d=0;d<c.length;d++)if(c[d].size>b)return!1;return!0},image:function(a,b,c){if(window.FileList&&c instanceof window.FileList)for(var d=0;d<c.length;d++){var e=c[d].type;if(e&&"image/gif"!==e&&"image/png"!==e&&"image/jpeg"!==e)return!1}return!0},static:function(a,b,c){return b}},Nette.toggleForm=function(a,b){var c;for(Nette.toggles={},c=0;c<a.elements.length;c++)a.elements[c].tagName.toLowerCase()in{input:1,select:1,textarea:1,button:1}&&Nette.toggleControl(a.elements[c],null,null,!b);for(c in Nette.toggles)Nette.toggle(c,Nette.toggles[c],b)},Nette.toggleControl=function(a,b,c,d,e){b=b||Nette.parseJSON(a.getAttribute("data-nette-rules")),e=void 0===e?{value:Nette.getEffectiveValue(a)}:e;for(var f,g=!1,h=[],i=function(){Nette.toggleForm(a.form,a)},j=0,k=b.length;j<k;j++){var l=b[j],m=l.op.match(/(~)?([^?]+)/),n=l.control?a.form.elements.namedItem(l.control):a;if(n){if(f=c,!1!==c){l.neg=m[1],l.op=m[2];var o=a===n?e:{value:Nette.getEffectiveValue(n)};if(null===(f=Nette.validateRule(n,l.op,l.arg,o)))continue;l.neg&&(f=!f),l.rules||(c=f)}if(l.rules&&Nette.toggleControl(a,l.rules,f,d,e)||l.toggle){if(g=!0,d)for(var p=!document.addEventListener,q=n.tagName?n.name:n[0].name,r=n.tagName?n.form.elements:n,s=0;s<r.length;s++)r[s].name!==q||Nette.inArray(h,r[s])||(Nette.addEvent(r[s],p&&r[s].type in{checkbox:1,radio:1}?"click":"change",i),h.push(r[s]));for(var t in l.toggle||[])Object.prototype.hasOwnProperty.call(l.toggle,t)&&(Nette.toggles[t]=Nette.toggles[t]||(l.toggle[t]?f:!f))}}}return g},Nette.parseJSON=function(s){return"{op"===(s||"").substr(0,3)?eval("["+s+"]"):JSON.parse(s||"[]")},Nette.toggle=function(a,b,c){var d=document.getElementById(a);d&&(d.style.display=b?"":"none")},Nette.initForm=function(a){Nette.toggleForm(a),a.noValidate||(a.noValidate=!0,Nette.addEvent(a,"submit",function(b){Nette.validateForm(a)||(b&&b.stopPropagation?(b.stopPropagation(),b.preventDefault()):window.event&&(event.cancelBubble=!0,event.returnValue=!1))}))},Nette.initOnLoad=function(){Nette.addEvent(document,"DOMContentLoaded",function(){for(var a=0;a<document.forms.length;a++)for(var b=document.forms[a],c=0;c<b.elements.length;c++)if(b.elements[c].getAttribute("data-nette-rules")){Nette.initForm(b);break}Nette.addEvent(document.body,"click",function(a){for(var b=a.target||a.srcElement;b;){if(b.form&&b.type in{submit:1,image:1}){b.form["nette-submittedBy"]=b;break}b=b.parentNode}})})},Nette.isArray=function(a){return"[object Array]"===Object.prototype.toString.call(a)},Nette.inArray=function(a,b){if([].indexOf)return a.indexOf(b)>-1;for(var c=0;c<a.length;c++)if(a[c]===b)return!0;return!1},Nette.webalize=function(a){a=a.toLowerCase();var b,c,d="";for(b=0;b<a.length;b++)c=Nette.webalizeTable[a.charAt(b)],d+=c||a.charAt(b);return d.replace(/[^a-z0-9]+/g,"-").replace(/^-|-$/g,"")},Nette.webalizeTable={"á":"a","ä":"a","č":"c","ď":"d","é":"e","ě":"e","í":"i","ľ":"l","ň":"n","ó":"o","ô":"o","ř":"r","š":"s","ť":"t","ú":"u","ů":"u","ý":"y","ž":"z"},Nette}),$(document).ready(function(){function a(){var a=window.innerWidth;a<768?($(".nav").addClass("nav--mobile"),$(".nav-product").not(".nav-product--hidden").hide(),$(".basket__content, .login__content").hide()):($(".nav").removeClass("nav--mobile"),$(".nav-product").not(".nav-product--hidden").show(),$(".basket__content, .login__content").show()),a<768&&$(".nav--mobile").click(function(){return $(".basket__content, .login__content").hide(),$(".nav-product").toggle(),!1}),a<768&&$(".basket__header").click(function(){
return $(".nav-product, .login__content").hide(),$(".basket__content").toggle(),!1}),a<768&&$(".login__header").click(function(){return $(".basket__content, .nav-product").hide(),$(".login__content").toggle(),!1})}var b=window.innerWidth;if($(window).resize(function(){b!==window.innerWidth&&a()}),a(),$(".product__image").length&&$(".product__image img").unveil(200),$(".nav-product--switch").length&&$(".nav-product--switch .nav-product__header").on("click",function(a){$(".nav-product--switch").removeClass("is-active"),$(this).parent().addClass("is-active")}),$(".tabs").length&&($(".tabs__content > div").not(".is-active").hide(),$(".tabs__name").on("click",function(a){a.preventDefault();var b=$(this).attr("href");$(".tabs__content > .is-active").removeClass("is-active").hide(),$(b).addClass("is-active").show(),$(".tabs__name").removeClass("is-active"),$(this).addClass("is-active")})),$(".open-tab").length&&$(".open-tab").on("click",function(a){$(".tabs__name").removeClass("is-active"),$(".tabs__content > .is-active").removeClass("is-active").hide();var b=$(this).attr("href");$(b).addClass("is-active").show(),$('.tab[href="'+b+'"]').addClass("is-active").show()}),$(".alert--close").length&&($(".alert--close").append(' <i class="icon icon--close"></i>'),$(".alert--close .icon--close").on("click",function(){$(this).parent().hide()})),$(".control--count").length&&($(".control--count").append(' <span class="control control--plus"><i class="icon icon--plus"></i></span> <span class="control control--minus"><i class="icon icon--minus"></i></span>'),$(".control--plus").on("click",function(){var a=$(this).parent().find("input").val(),a=parseFloat(a);(a<=0||!$.isNumeric(a))&&(a=0);var a=a+1;$(this).parent().find("input").val(a)}),$(".control--minus").on("click",function(){var a=$(this).parent().find("input").val(),a=parseFloat(a);(a<=0||!$.isNumeric(a))&&(a=1);var a=a-1;$(this).parent().find("input").val(a)})),$(".modal").length&&($(".modal--show").on("click",function(){var a="#"+$(this).attr("rel"),b=a+" .modal__body";$(a).show(),$(b).outerHeight()>bodyHeight&&$(a).addClass("modal--reset")}),$(".modal__close").on("click",function(){return $(this).closest(".modal").hide(),!1})),$(".antispam").length){var c=$(".antispam").attr("data-help");$(".antispam").find("input").val(c),$(".antispam").hide()}if($(".reveal").length&&($(".reveal").hide(),$(".reveal--show").not("label.reveal--show, input.reveal--show").on("click",function(){var a="#"+$(this).attr("rel");return $(a).toggle(),!1}),$("label.reveal--show, input.reveal--show").on("change",function(){var a="#"+$(this).attr("rel");$(a).toggle()})),$(".control--print").length&&$(".control--print").click(function(){return window.print(),!1}),$(".order-delivery").length){if($(".order-delivery__payment").hide(),$(".order-delivery__type").on("click",function(){$(".order-delivery__payment").hide();var a="#"+$(this).attr("rel");$(a).toggle();var b=$(this).find("input[type=radio]");$(b).length&&b.prop("checked",!0),$(a).find("input").filter(":visible:first").trigger("click")}),$(".order-delivery__type input").on("click",function(){$(this).closest(".order-delivery__type").trigger("click")}),0!=delIdSelected){var d="#delid_"+delIdSelected;$(d).find("input").prop("checked",!0)}if(0!=payIdSelected){var e='input[value="'+payIdSelected+'"]';$(e).closest(".order-delivery__payment").show(),$(e).trigger("click")}else $(".order-delivery__type").first().trigger("click")}b>576&&$(".slider").length&&$(".slider__body").slick({dots:!0,speed:500,slidesToShow:1,autoplay:!0,arrows:!1}),$(".gallery").length&&($.extend(!0,$.magnificPopup.defaults,{tClose:"Zavřít",tLoading:"Nahrávám...",gallery:{tPrev:"Předchozí",tNext:"Následující",tCounter:"%curr% z %total%"},image:{tError:'<a href="%url%">Obrázek</a> nelze načíst.'},ajax:{tError:'<a href="%url%">Obsah</a> nelze načíst.'}}),$(".gallery").magnificPopup({delegate:"a",type:"image",removalDelay:300,mainClass:"mfp-fade",gallery:{enabled:!0,tCounter:""}}))});