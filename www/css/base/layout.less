// bootstrap - pouze grid (http://getbootstrap.com/)
@import "../bootstrap/variables.less";
@import "../bootstrap/mixins.less";
@import "../bootstrap/grid.less";

// zmenš<PERSON><PERSON> mezer mezi bloky
@grid-gutter-width: 20px;

// cleafix na řádek
.row { .clear; }

// maximální šířka stránky
.container-fluid { max-width: @mqlg; }

// zarovnání v boxu
.col-center { text-align: center; }
.col-left { text-align: left; }
.col-right { text-align: right; }

// XS sloupec bude na nižších rozlišeních vždy 100%
.col-xs-1,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9,
.col-xs-10,
.col-xs-11,
.col-xs-12 {
  @media (max-width: @mqxxs) {
    width: 100%;
  }
}

// stejně vysoké boxy
.row--flex {

  display: flex;
  flex-wrap: wrap;

  // fix pro safari
  &:before, &:after {
    content: normal;
  }

}
.row--flex > [class*='col-'] {
  display: flex;
  flex-direction: column;
}

// vnořený div, nechceme mínusové marginy
.row--inner {
  margin: 0;
}

// okraj okolo celých boxů, pouze od vyššího rozlišení
.row--border {

  @media (min-width: @mqxxs) {

    border-top: 1px dotted #e5e5e5;
    border-left: 1px dotted #e5e5e5;

    & > div {

      border-bottom: 1px dotted #e5e5e5;
      border-right: 1px dotted #e5e5e5;

      // hover efekt vypočítáme z hlavní barvy
      &:hover, &:active, &:focus {
        box-shadow: 0 0 10px fade(@color_main, 50%);
        z-index: 100;
        border-radius: @radius;
      }

    }

  }

}

// autozarovnávač boxů
.row--autoclear {

  @media (min-width: 1200px) {
    .col-lg-1:nth-child(12n+1) { clear: left; }
    .col-lg-2:nth-child(6n+1) { clear: left; }
    .col-lg-3:nth-child(4n+1) { clear: left; }
    .col-lg-4:nth-child(3n+1) { clear: left; }
    .col-lg-6:nth-child(odd) { clear: left; }
  }
  @media (min-width: 992px) and (max-width: 1199px) {
    .col-md-1:nth-child(12n+1) { clear: left; }
    .col-md-2:nth-child(6n+1) { clear: left; }
    .col-md-3:nth-child(4n+1) { clear: left; }
    .col-md-4:nth-child(3n+1) { clear: left; }
    .col-md-6:nth-child(odd) { clear: left; }
  }
  @media (min-width: 768px) and (max-width: 991px) {
    .col-sm-1:nth-child(12n+1) { clear: left; }
    .col-sm-2:nth-child(6n+1) { clear: left; }
    .col-sm-3:nth-child(4n+1) { clear: left; }
    .col-sm-4:nth-child(3n+1) { clear: left; }
    .col-sm-6:nth-child(odd) { clear: left; }
  }
  @media (max-width: 767px) {
    .col-xs-1:nth-child(12n+1) { clear: left; }
    .col-xs-2:nth-child(6n+1) { clear: left; }
    .col-xs-3:nth-child(4n+1) { clear: left; }
    .col-xs-4:nth-child(3n+1) { clear: left; }
    .col-xs-6:nth-child(odd) { clear: left; }
  }

}

// fix gridu pro IE8
.lt-ie9 {

  .container,
  .container-fluid
  {
    display: table;
    width: 100%;
  }
  .row
  {
    height: 100%;
    display: table-row;
  }
  [class^=col-]
  {
    display: table-cell;
  }

}
