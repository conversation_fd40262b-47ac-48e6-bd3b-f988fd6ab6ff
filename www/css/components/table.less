// tabulka

.table {

  width: 100%;

  // rámování tabulky
  border-top: 1px solid @color_main;
  border-bottom: 0;
  border-left: 1px solid @color_main;
  border-right: 0;
  th, td {
    border-top: 0;
    border-bottom: 1px solid @color_main;
    border-left: 0;
    border-right: 1px solid @color_main;
  }

  // rozlámání tabulky na malých rozlišeních
  @media only screen and (max-width: @mqxs) {

    display: block;

    th, td, tr, thead, tbody {
      display: block;
    }

    th + th {
      border-top: 1px solid @color_main_light;
    }

    tr td:last-child {
      border-bottom: 2px solid @color_main;
    }

  }

  // více prostoru v tabulce
  th, td {

    padding: 5px;

    @media (min-width: @mqsm) {
      padding: 10px 20px;
    }

  }

  // hlavička tabulky
  th {

    color: @color_white;
    background-color: @color_main;
    border-right: 1px solid lighten(@color_main, 5%);

    &:last-child {
      border-right: 1px solid @color_main;
    }

  }

  // zvýraznění řádku po najetí myší
  tr:hover td {
    background-color: lighten(@color_main, 60%);
  }

  // zebra stripping
  tr:nth-child(odd) td {
    background-color: lighten(@color_main, 60%);
  }

}

  // vertikální tabulka
  .table--vertical {

    th {
      text-align: left;
      border-bottom: 1px dotted @color_main_light;
    }

  }

  // tabulka provonání
  .table--compare {

    td {
      vertical-align: top;
      text-align: center;
    }

    img {
      display: inline-block;
    }

    // nebudeme zdvojovat poslední linku
    tr td:last-child {
      border-bottom: 1px solid @color_main;
    }

    // zrušení rozlámání na malých rozlišeních
    @media only screen and (max-width: @mqxs) {

      display: table;

      th, td {
        display: table-cell;
      }

      tr, thead, tbody {
        display: table-row;
      }

    }

    // zvětšení boxů od většího rozlišení
    @media (min-width: @mqsm) {

      th { width: 5%; }
      td { width: 20%; }

    }

  }

