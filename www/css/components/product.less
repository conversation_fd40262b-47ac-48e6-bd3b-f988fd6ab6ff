// výpis produktů

.product {

  padding: 15px 10px 10px 10px; // kompenzujeme padding na tlačítcích

  text-align: center;

  background-color: #fff;

  // na nižších rozlišeních oddělíme produkty
  @media (max-width: @mqxxs) {
    padding-bottom: 20px;
    border-bottom: 1px dotted @color_main_light;
  }

  // štítky u produktu
  // přetěžujeme pozici - chceme formátovat pouze na výpisu
  .labels {

    // vystrčení z obrázku
    position: absolute;
    top: 5px;
    left: -5px;
    width: 1px; // pro zarovnání štítků pod sebe a zachování inline bloku štítků
    text-align: left;

    // menší mezery mezi štítky
    .label {
      margin-bottom: 2px;
    }

  }

}

  // hlavička produktu
  .product__header {

    margin: 0 0 10px 0;
    color: @color_main;
    font-size: 18px;

    @media (min-width: @mqxxs) {
      overflow: hidden; // zabráněn<PERSON> přetečení nadpisu
      height: 43px; // rezerva pro dva řádky, pouze na vyšších rozlišeních
      font-size: 20px;
    }

    a {
      color: @color_main;
      text-decoration: none;
    }

  }

  // obrázek produktu
  .product__image {

    position: relative; // pro absolutní pozicování štítků
    margin: 15px 0;

    img {
      width: 100%;
    }

    a:hover, a:active, a:focus {
      opacity: 0.8;
    }

  }

  // zkrácený text produktu
  .product__info {
    margin: 0.25em 0 0.75em 0;
    font-size: 14px;
  }

  // cena
  .product__price {

    margin: 10px 0;

    font-size: 13px;
    color: @color_price;

    // částka ceny
    strong {
      font-size: 20px;
    }

  }

  // ovládací prvky (tlačítka, inputy)
  .product__controls {

    // mezera mezi tlačítky na nižších rozlišeních
    a {
      margin-bottom: 5px;
    }

  }
