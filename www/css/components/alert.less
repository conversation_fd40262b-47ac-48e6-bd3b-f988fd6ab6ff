// chybo<PERSON><PERSON> hl<PERSON>

.alert {

  margin: 1em 0;
  padding: 0.1em 1em;

  color: @color_white;

  background-color: @color_main;
  border-radius: @radius;

  @media (min-width: @mqsm) {
    padding: 0.25em 1.5em;
  }

}

  // hláška s možností zavření
  .alert--close {

    position: relative; // pro křížek na zavření

    padding: 0.1em 2em 0.1em 1em; // místo na křížek, bude-li třeba

    @media (min-width: @mqsm) {
      padding: 0.25em 2em 0.25em 1.5em; // místo na křížek, bude-li třeba
    }

    .icon--close {

      position: absolute;
      top: 1em;
      right: 1em;
      font-size: 18px;
      cursor: pointer;

      &:hover, &:active, &:focus {
        opacity: 0.8;
      }

    }

  }

  // potvrzující hláška
  .alert--success {
    background-color: @color_success;
  }

  // informační hláška
  .alert--info {
    background-color: @color_info;
  }

  // výstražná hláška
  .alert--danger {
    background-color: @color_danger;
  }
