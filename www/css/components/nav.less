// navigace

.nav {

  color: @color_white;

  background-color: @color_main_light;

  // na mobilní verzi bude místo loga pouze ikona
  @media (max-width: @mq_menu) {
    position: absolute;
    top: 9px;
    right: 5px;
    width: 40px;
    height: 40px;
    overflow: hidden;
    background: @color_main_light url('../img/menu-icon.svg') center center no-repeat;
    text-indent: -9999px;
    border-top-left-radius: @radius;
    border-top-right-radius: @radius;
  }

  ul {

    list-style-type: none;
    margin: 0;
    padding: 0;
    font-size: 0; // inline-block fix

    @media (min-width: @mqmd) {
      text-align: right;
    }

  }

  li {

    display: inline-block;
    border-right: 1px solid @color_main;

    &:first-child {

      @media (min-width: @mq_menu) {
        border-left: 1px solid @color_main;
      }

    }

  }

  a {

    display: block;
    padding: 15px 10px;
    color: @color_white;
    font-size: 16px;
    font-weight: 700;
    text-decoration: none;
    text-transform: uppercase;

    @media (min-width: @mqmd) {
      padding: 15px 20px;
    }

    &:hover, &:active, &:focus, &.is-active {
      background-color: @color_main_dark;
    }

  }

}
