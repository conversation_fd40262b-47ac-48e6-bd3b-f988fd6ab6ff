// zálozky

.tabs {

  margin-top: 20px;

  @media (min-width: @mqsm) {
    border-bottom: 1px solid @color_gray_light;
  }

}

  // názvy <PERSON>
  .tabs__name {

    display: block;
    margin-bottom: -1px; // záporný margin pro aktuální záložku
    padding: 15px 20px;

    color: @color_white;
    text-decoration: none;
    text-align: center;

    background-color: @color_gray_light;
    border: 1px solid @color_gray_light;

    @media (min-width: @mqsm) {
      border-top-right-radius: 4px;
      border-top-left-radius: 4px;
    }

    &:hover, &:active, &:focus {
      background-color: darken(@color_gray_light, 10%);
    }

    // aktivní z<PERSON>
    &.is-active {

      font-weight: bold;
      color: @color_main;
      background-color: @color_white;

      @media (min-width: @mqsm) {
        border-bottom: 1px solid @color_white;
      }

    }

  }

  // obsah <PERSON>
  .tabs__into {

    padding: 0.75em;

    @media (min-width: @mqsm) {
      padding: 1.5em;
    }

    // orámování
    border-bottom: 1px solid @color_gray_light;
    border-left: 1px solid @color_gray_light;
    border-right: 1px solid @color_gray_light;

  }

  // nadpis záložky
  .tabs__header {
    margin-top: 0;
    font-size: 1.5em;
  }
