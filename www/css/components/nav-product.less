// navigace

.nav-product {

  position: relative; // pro umístěn<PERSON> t<PERSON>
  margin-top: 15px;

  @media (min-width: @mq_menu) {
    padding-bottom: 130px; // místo pro obrázek
    background: url(../img/nav-back.jpg) center bottom no-repeat;
  }

  ul {
    list-style-type: none;
    margin: 0;
    padding: 0;
    color: @color_white;
    background-color: @color_main_light;
  }

  // podmenu
  li ul a {

    padding: 7px 10px 7px 35px;
    font-size: 90%;

    &:before {
      position: absolute;
      display: block;
      content: ">";
      margin: 2px 0 0 -12px;
      font-size: 10px;
    }

  }

  // aktivní část
  li.is-active {

    padding: 10px 0;
    background-color: @color_main;

    a {

      background-color: @color_main;

      &:hover, &:active, &:focus {
        background-color: @color_main_dark;
      }

    }

  }

  a {

    display: block;
    padding: 8px 10px 8px 20px;
    color: @color_white;
    text-decoration: none;
    border-bottom: 1px dotted @color_main;

    &:hover, &:active, &:focus {
      background-color: @color_main_dark;
    }

  }

}

  // mobilní verze menu
  .nav-product__wrapper {

    @media (max-width: @mq_menu) {
      position: absolute; // pozicujeme nahoru k ikoně
      top: 32px;
      z-index: 1;
      width: 100%;
      padding: 0;
    }

  }

  // skryté menu pro full-page stránky
  .nav-product--hidden {
    display: none;
  }

  // přepínání produktů/výrobců
  .nav-product--switch {

    .nav-product__menu {

      @media (min-width: @mqsm) {
        padding-top: 35px;
        display: none;  // neaktivní menu skryté
      }

    }

    // aktivní nabídka
    &.is-active {

      // nadpis
      .nav-product__header {
        opacity: 1;
      }

      // menu
      .nav-product__menu {
        display: block;
      }

    }

    // zanoříme, protože chceme stylovat pouze pod switcherem
    .nav-product__header {

      display: block;

      @media (min-width: @mqsm) {

        position: absolute;
        top: 0;
        width: 49%;
        opacity: 0.8;
        cursor: pointer;
        border-top-left-radius: @radius;
        border-top-right-radius: @radius;

        &:hover, &:active, &:focus {
          opacity: 1;
        }

      }

    }

  }

  // nadpis menu
  .nav-product__header {
    margin: 0;
    padding: 10px 15px 7px 15px;
    color: @color_white;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    background-color: @color_main_light;

    @media (min-width: @mqsm) {
      border-top-left-radius: @radius;
      border-top-right-radius: @radius;
    }

  }

  // výrobce bude napravo
  .nav-product__header--right {
    right: 0;
  }

  // spodní menu
  .nav-product__other {

    @media (min-width: @mqsm) {
      padding-top: 10px;
    }

    .nav-product__header {
      background-color: @color_gray;
    }

    a {

      background-color: @color_gray_light;
      border-bottom: 1px dotted @color_gray_dark;

      &:hover, &:focus {
        background-color: @color_gray_dark;
      }

    }

  }

  // textový blok
  .nav-product__textblock {

  }
