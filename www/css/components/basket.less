// odkaz na košík

.basket {

  text-align: center;
  background-color: @color_main_light;
  transition: background 0.2s;
  border-top-left-radius: @radius;
  border-top-right-radius: @radius;

  // klikací celý blok
  a {
    display: block;
    text-decoration: none;
  }

  &:hover, &:active, &:focus {
    background-color: @color_main;
  }

}

  // mobilní verze boxu (reset m<PERSON><PERSON>)
  .basket__wrapper {

    @media (max-width: @mq_menu) {

      position: absolute; // pozicujeme nahoru k ikoně
      top: 48px;
      z-index: 1;
      width: 100%;
      padding: 0;

    }

  }

  // nadpis boxu
  .basket__header {

    margin: 0;
    padding: 15px 5px 10px 5px;
    font-size: 18px;
    font-weight: bold;
    text-transform: uppercase;
    background-color: @color_main;
    border-top-left-radius: @radius;
    border-top-right-radius: @radius;

    // klikací ikona na mobilu
    @media (max-width: @mq_menu) {
      position: absolute;
      top: -39px;
      right: 50px;
      width: 40px;
      height: 40px;
      padding: 12px 0 0 6px;
      overflow: hidden;
      background: @color_main_light url(../img/basket.svg) center center no-repeat;
      background-size: 20px 20px;
      text-indent: -9999px;
    }

  }

  // obsah boxu
  .basket__content {
    margin: 0;
    padding: 5px 10px 8px 10px;
    line-height: 1.4;
  }

  // počet kusů v košíku
  .basket__count {

    display: block;
    position: absolute;
    bottom: 0;
    right: 0;
    padding: 2px 3px;
    text-indent: 0;
    font-size: 10px;
    background-color: #ff2020;
    border-radius: 3px 0 0 0;

    @media (min-width: @mqsm) {
      display: none;
    }

  }
