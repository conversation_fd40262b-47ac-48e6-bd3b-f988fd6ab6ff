// hlavička

.header {

  position: relative;

  min-width: 168px;

  padding: 5px 0;

  color: @color_white;

  background: #fee8d3 url(../img/back-header.jpg) center top repeat-x;

  @media (min-width: @mqsm) {
    padding: 15px 0;
  }

  a {
    color: @color_white;
  }

  .search {

    text-align: center;

    @media (min-width: @mqsm) {
      margin-top: 16px;
    }

  }

}

  .header__logo {

    img {

      height: 45px;

      @media (min-width: @mq_menu) {
        margin-top: -2px;
        margin-left: 10px;
        height: auto;
      }

    }

  }

  // rozšířená verze loga
  .header__deco {

    display: none;
    z-index: 1;

    @media (min-width: @mqmd) {
      display: block;
      position: absolute;
      top: 0;
      margin-left: 10px;
      width: 190px;
      height: 109px;
      background: url(../img/logo-big.png) center center no-repeat;
      background-size: 190px 109px;
    }

    @media (min-width: 1070px) {
      width: 246px;
      height: 141px;
      background: url(../img/logo-big.png) center center no-repeat;
      background-size: 246px 141px;
    }

    a {
      display: block;
      width: 100%;
      height: 100%;
      text-decoration: none;
    }

  }
