// tlačítko

.btn, button {

  margin-bottom: 4px;
  padding: 10px 12px;

  color: @color_white;
  font: inherit; // pro sladění vzhledu buttonu a odkazu
  font-size: 16px;
  text-decoration: none;
  text-transform: uppercase;

  background-color: @color_main;
  border: 1px solid transparent;
  border-radius: @radius;

  cursor: pointer;

  &:hover, &:active, &:focus {
    background-color: darken(@color_main, 20%);
  }

  // ikona v tlačítku
  .icon {
    vertical-align: bottom;
    margin: 0 3px;
    font-size: 16px;
  }

}

  // úprava odkazu
  a.btn {
    display: inline-block;
  }

  // kompaktní tlačítko
  .btn--small {

    padding: 5px 6px;
    font-size: 14px;

    // úprava velikosti ikony
    .icon { font-size: 15px; }

  }

  // výrazné tlačítko
  .btn--big {

    padding: 15px 17px;
    font-size: 20px;

    // úprava velikosti ikony
    .icon { font-size: 20px; }

    // na malém rozlišení bude v<PERSON>dy malý
    @media (max-width: @mqxxs) {
      padding: 8px 10px;
      font-size: 14px;
      .icon { font-size: 14px; }
    }

  }

  // potvrzující tlačítko výrazné
  .btn--success {

    background-color: @color_success;

    &:hover, &:active, &:focus {
      background-color: darken(@color_success, 10%);
    }

  }

  // informativní tlačítko
  .btn--info {

    background-color: @color_info;

      &:hover, &:active, &:focus {
        background-color: darken(@color_info, 10%);
      }

  }

  // výstražné tlačítko
  .btn--danger {

    background-color: @color_danger;

    &:hover, &:active, &:focus {
      background-color: darken(@color_danger, 10%);
    }

  }

  // tlačítko koupit
  .btn--buy {

    background-color: @color_buy;

    &:hover, &:active, &:focus {
      background-color: darken(@color_buy, 10%);
    }

  }
