// obs<PERSON><PERSON>, z<PERSON><PERSON><PERSON><PERSON> vzhled

.article {

  line-height: 1.2; // pro v<PERSON><PERSON><PERSON><PERSON> čitelnost

  // základní podoba nadpisů
  h1,
  h2,
  h3,
  h4 {
    color: @color_main;
  }

  // základní odkaz
  // zaměříme všechny odkazy krom tlačítka (btn)
  a:not(.btn) {

    color: @color_main;

    &:hover, &:active, &:focus {
      color: @color_main_dark;
    }

  }

  // základní seznam
  ul {
    padding-left: 1.5em;
  }

  // základní číslovaný seznam
  ol {
    padding-left: 2em;
  }

  // základní citace
  blockquote {
    margin: 1em 0;
    padding: 0.1em 1.5em;
    background-color: lighten(@color_main, 60%);
    border-left: 2px solid @color_main;
  }

}

  // vložené obrázky
  .article__images {

    img {
      max-height: 200px;
      display: inline-block;
    }

  }

  // vložené př<PERSON>
  .article__attachements {

    ul {
      list-style-type: none;
      padding-left: 2px;
    }

    li {
      margin-top: 5px;
      margin-bottom: 5px;
    }

    .icon {
      margin-top: -3px;
      margin-bottom: 3px;
      margin-right: 5px;
      font-size: 22px;
      text-decoration: none;
    }

  }

  // související zboží a příslušenství
  .article__related {

    h2 {
      font-size: 25px;
      color: @color_main_light;
      font-weight: 400;
    }

  }
