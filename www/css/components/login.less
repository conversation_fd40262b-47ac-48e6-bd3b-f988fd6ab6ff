// p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON>

.login {

  text-align: center;
  background-color: @color_main_light;
  border-top-left-radius: @radius;
  border-top-right-radius: @radius;

  @media (max-width: @mqxs) {
    margin-bottom: 15px;
  }

  a {

    padding: 0 1px;

    &:hover, &:active, &:focus {
      color: @color_main_dark;
    }

  }

}

  // mobilní verze boxu (reset <PERSON><PERSON><PERSON><PERSON><PERSON>)
  .login__wrapper {

    @media (max-width: @mqsm) {

      position: absolute; // pozicujeme nahoru k ikoně
      top: 48px;
      z-index: 1;
      width: 100%;
      padding: 0;

    }

    @media (max-width: @mqmd) {
      clear: both;
    }

  }

  // nadpis boxu
  .login__header {

    margin: 0;
    padding: 15px 5px 10px 5px;
    font-size: 18px;
    font-weight: bold;
    text-transform: uppercase;
    background-color: @color_main;
    border-top-left-radius: @radius;
    border-top-right-radius: @radius;

    // klikací ikona na mobilu
    @media (max-width: @mq_menu) {
      position: absolute;
      top: -39px;
      right: 95px;
      width: 40px;
      height: 40px;
      padding: 12px 0 0 6px;
      overflow: hidden;
      background: @color_main_light url(../img/man.svg) center center no-repeat;
      background-size: 20px 20px;
      text-indent: -9999px;
    }

  }

  // obsah boxu
  .login__content {
    margin: 0;
    padding: 5px 10px 8px 10px;
    line-height: 1.4;
  }
