@echo off
:: cesta do MySQL bin adresáře
SET MySQLDir=C:\xampp\mysql\bin\

:: VZDALENY SERVER
SET host=db.koblihcz.savana-hosting.cz
SET port=10002
SET database=widexcz
SET user=widexcz

:: LOKALNI SERVER
SET host_local=127.0.0.1
SET database_local=widexcz
SET user_local=root

cls
set /p pw= Zadej heslo pro uzivatele %user% k VZDALNENE databazi %database%:
cls
set /p pw_local= Zadej heslo pro uzivatele %user% k LOKALNI databazi %database%:
cls
Echo Stahuji databazi ...
%MySQLDir%mysqldump --user=%user% --password=%pw% --host=%host% --port=%port% --add-drop-table --single-transaction --routines --triggers --events --default-character-set=utf8 %database% > %0\..\dump.sql
Echo Databaze stazena

IF EXIST %__CD__%dump.sql (
  Echo Importuji do lokalni databaze ...
  %MySQLDir%mysql --host=%host_local% --user=%user_local% --password=%pw_local% %database_local% < %__CD__%dump.sql
  Echo Hotovo

  del %__CD__%dump.sql
  Echo Dump byl vymazan

  Echo Mazu cache ...
  php %WwwDir%www\cc.php -f k=lZwJIL

) ELSE (
  Echo Dump %__CD__%dump.sql nenalezen.
)
