{"name": "kosmetika-biolage", "version": "0.1.0", "description": "kosmetika-biolage devstack", "author": "<PERSON>", "devDependencies": {"grunt": "^1.0.1", "grunt-autoprefixer": "^3.0.4", "grunt-browser-sync": "^2.2.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-cssmin": "^2.0.0", "grunt-contrib-imagemin": "^1.0.1", "grunt-contrib-less": "^1.4.1", "grunt-contrib-uglify": "^2.3.0", "grunt-contrib-watch": "^1.0.0", "grunt-legacssy": "^0.4.0", "grunt-newer": "^1.3.0", "grunt-webfont": "^1.6.0", "jit-grunt": "^0.10.0", "time-grunt": "^1.4.0"}}