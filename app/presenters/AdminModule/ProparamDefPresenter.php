<?php
namespace AdminModule;

use <PERSON><PERSON>,
  di<PERSON>,
  <PERSON>;

final class ProparamDefPresenter extends BasePresenter {
  /** @persistent */
  public $page = 1;

  /** @persistent */
  public $sName = '';

  /** @persistent */
  public $sCatId = 0;

  /** @persistent */
  public $sStatus = '';


  /********************* view default *********************/

  public function renderDefault() {
    $prds = new \Model\ProparamDefsModel();

    $where = "";
    if (!empty($this->sName)) $where .= " prdname = '$this->sName' AND ";
    if (!empty($this->sCatId)) $where .= " prdcatid = $this->sCatId AND ";
    if ((string)$this->sStatus!='') $where .= " prdstatus=$this->sStatus AND ";

    if (!empty($where)) {
      $where = " WHERE ".substr($where, 0, -5);
    }

    if (!empty($this->sOrderBy)) {
      $orderBy = " ORDER BY ".$this->sOrderBy." ".$this->sOrderByType;
    } else {
      $orderBy = " ORDER BY prdname DESC";
    }

    $dataSource = $prds->getDataSource("
      SELECT * FROM proparamdefs 
      LEFT JOIN catalogs ON (catid=prdcatid)       
      $where $orderBy
      ");

    $paginator = $this['paginator']->paginator;
    $paginator->page = $this->page;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->dataRows = $dataRows;
    //ciselnik statusu
    $this->template->enum_prdstatus = $prds->getEnumPrdStatus();
  }

  public function renderEdit($id) {
    $form = $this['editForm'];

    if (!$form->isSubmitted()) {
      $prds = new \Model\ProparamDefsModel();
      $dataRow = array();
      if ($id > 0) {
        $dataRow = $prds->load($id);

        if (!$dataRow) {
          throw new NBadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
      }
      $this->template->dataRow = $dataRow;
      $this->template->id = $id;
    }
  }


  public function actionDelete($id) {
    $prds = new \Model\ProparamDefsModel();
    $cnt = (int)dibi::fetchSingle("SELECT COUNT(*) FROM proparams WHERE prpprdid=%i", $id);
    if ($cnt > 0) {
      $this->flashMessage("Parametr nelze vymazat, je používán.", "err");
    } else {
      $prds->delete($id);
      $this->flashMessage("Parametr byl vymazán.");
    }
    $this->redirect('default');
  }
  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $prds = new \Model\ProparamDefsModel();
    $form = $this->createAppForm();

    $arr = dibi::fetchPairs("SELECT catid, catname FROM catalogs WHERE catmasid=0 AND catstatus=0 ORDER BY catorder");

    $form->addSelect("prdcatid", "Katalog", $arr)
      ->setPrompt('Bez omezení');

    $form->addText("prdname", "Název")
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno");

    $form->addText("prdunit", "Jednotka");

    $form->addCheckbox("prdsearch", "je to vyhledávací parametr");

    $form->addSelect("prdstatus", "Status", $prds->getEnumPrdStatus());

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');
    return $form;
  }

  public function editFormSubmitted (Nette\Application\UI\Form $form) {
    $prds = new \Model\ProparamDefsModel();
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $id = (int)$this->getParam('id');
      try {
        if ($id > 0) {
          $prds->update($id, $vals);
        } else {
          $id = $prds->insert($vals);
        }

        $this->flashMessage('Uloženo v pořádku');
        $this->redirect('edit',$id);

      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }
    }
  }

  protected function createComponentSearchForm() {
    $prds = new \Model\ProparamDefsModel();

    $form = $this->createAppForm();
    $form->addGroup("Vyhledávání");
    $form->addText("name", "Název ", 10)
      ->setDefaultValue($this->sName);

    $arr = dibi::fetchPairs("SELECT catid, catname FROM catalogs WHERE catmasid=0 AND catstatus=0 ORDER BY catorder");
    $form->addSelect("catid", "Katalog ", $arr)
      ->setPrompt('');
    if (!empty($this->sCatId)) $form["catid"]->setDefaultValue($this->sCatId);

    $form->addSelect("status", "Stav", $prds->getEnumPrdStatus())
      ->setPrompt('');
    if (!empty($this->sStatus)) $form["status"]->setDefaultValue($this->sStatus);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sName = Null;
        $this->sCatId = Null;
        $this->sStatus = Null;
      } else {
        $vals = $form->getValues();
        $this->sName = $vals["name"];
        $this->sCatId = $vals["catid"];
        $this->sStatus = $vals["status"];
      }
    }
    $this->redirect("default");
  }
}
?>
