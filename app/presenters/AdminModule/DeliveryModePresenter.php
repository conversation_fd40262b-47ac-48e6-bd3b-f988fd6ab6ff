<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  Model;

final class DeliveryModePresenter extends BasePresenter {

  public function editFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $deliveryMode = new \Model\DeliveryModesModel();
      $id = (int)$this->getParameter('id');
      $vals = $form->getValues();
      if (empty($vals["delprice1a"])) $vals["delprice1a"] = 0;
      if (empty($vals["delprice1b"])) $vals["delprice1b"] = 0;
      if (empty($vals["delprice1c"])) $vals["delprice1c"] = 0;
      if (empty($vals["delprice1d"])) $vals["delprice1d"] = 0;
      if (empty($vals["delprice2a"])) $vals["delprice2a"] = 0;
      if (empty($vals["delprice2b"])) $vals["delprice2b"] = 0;
      if (empty($vals["delprice2c"])) $vals["delprice2c"] = 0;
      if (empty($vals["delprice2d"])) $vals["delprice2d"] = 0;

      try {
        if ($deliveryMode->save($id, $vals)) {
          $this->flashMessage('Uloženo v pořádku');
          $this->redirect('DeliveryMode:default');
        }

      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }
    }
  }

  /********************* view default *********************/

  public function renderDefault($status=0) {
    $deliveryMode = new \Model\DeliveryModesModel();
    $statusSql = ($status == 1 ? ''  : ' AND delstatus=0');
    $dataRows = dibi::query($deliveryMode->getSql()." WHERE delmasid=0 $statusSql ORDER BY delorder")
      ->fetchAssoc('delid');

    foreach ($dataRows as $key => $value) {
      $dataRows[$key]["subItems"] = dibi::fetchAll($deliveryMode->getSql()." WHERE delmasid=$key $statusSql ORDER BY delorder");
    }
    $this->template->dataRows = $dataRows;
    //ciselnik statusu
    $this->template->enum_delstatus = $deliveryMode->getEnumDelStatus();
  }

  public function renderEdit($id, $masid=0) {
    $form = $this['editForm'];

    if (!$form->isSubmitted()) {
      $deliveryMode = new \Model\DeliveryModesModel();
      $dataRow = array();
      if ($id > 0) {
        $dataRow = $deliveryMode->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
      }
      if ($masid > 0) $this->template->delMas = $deliveryMode->load($masid);
      $this->template->dataRow = $dataRow;
      $this->template->id = $id;

      //nactu stavajici slevy
      $this->template->discounts = dibi::fetchAll("SELECT * FROM discounts WHERE disdelid=%i", $id);
    }
  }

  public function actionDeleteDelFree($disid, $delid) {
    $diss = new \Model\DiscountsModel();
    $diss->delete($disid);
    $this->redirect('edit#freedelivery', $delid);
  }

  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParameter('id');
    $masid = (int)$this->getParameter('masid');

    $deliveryMode = new \Model\DeliveryModesModel();
    if ($id > 0) {
      $dataRow = $deliveryMode->load($id);
      $masid = $dataRow->delmasid;
    }

    $form = $this->createAppForm();

    $form->addText('delname', 'Název:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Název: je nutné vyplnit');

    if ($masid > 0) {
      //jedna se o platbu

      $form->addSelect('delmasid', 'Způsob dopravy:', $deliveryMode->getEnumDelModes())
        ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna");
      $form["delmasid"]->setDefaultValue($masid);

      $form->addSelect('delcode', 'Typ platby:', $deliveryMode->getEnumPayTypes())
        ->setPrompt("");

      $form->addCheckbox("delnodelfree", "Neplatí platba zdarma");
    } else {
      //jedna se odopravu
      $form->addSelect('delcode', 'Typ dopravy:', $deliveryMode->getEnumDelTypes())
        ->setPrompt("");

      $form->addCheckbox("delnodelfree", "Neplatí doprava zdarma");
    }

    /*
    $arr = array(
      '0' => 'Bez omezení',
      '1' => 'Jen '.$this->curCodes[1],
      '2' => 'Jen '.$this->curCodes[2],
    );
    $form->addSelect('delcurid', 'Omezení:', $arr);
    */

    $arr = array(
      '1' => 'Česká republika',
      '2' => 'Slovensko',
    );
    $form->addSelect('delcouid', 'Cílová země:', $arr)
      ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
      ->setPrompt("");


    $labelA = 'Cena A';
    if (isset($this->neonParameters["labels"]["a"])) $labelA = $this->neonParameters["labels"]["a"];
    $labelB = 'Cena B';
    if (isset($this->neonParameters["labels"]["b"])) $labelB = $this->neonParameters["labels"]["b"];
    $labelC = 'Cena C';
    if (isset($this->neonParameters["labels"]["c"])) $labelC = $this->neonParameters["labels"]["c"];
    $labelD = 'Cena D';
    if (isset($this->neonParameters["labels"]["d"])) $labelD = $this->neonParameters["labels"]["d"];


    $text = ($masid > 0 ? 'platbu' : 'dopravu') ;

    $form->addText('delprice1a', $labelA.' za '.$text.' v '.$this->curCodes[1].':', 30, 5)
      ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addText('delprice1b', $labelB.' za '.$text.' v '.$this->curCodes[1].':', 30, 5)
      ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addText('delprice1c', $labelC.' za '.$text.' v '.$this->curCodes[1].':', 30, 5)
      ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    $form->addText('delprice1d', $labelD.' za '.$text.' v '.$this->curCodes[1].':', 30, 5)
      ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    if ($this->secondCurrency) {
      $form->addText('delprice2a', $labelA.' za '.$text.'  v '.$this->curCodes[2].':', 30, 5)
        ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

      $form->addText('delprice2b', $labelB.' za '.$text.'  v '.$this->curCodes[2].':', 30, 5)
        ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

      $form->addText('delprice2c', $labelC.' za '.$text.'  v '.$this->curCodes[2].':', 30, 5)
        ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

      $form->addText('delprice2d', $labelD.' za '.$text.'  v '.$this->curCodes[2].':', 30, 5)
        ->addRule(Nette\Forms\Form::FILLED, "%label: hodnota musí být vyplněna")
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FLOAT, "%label: hodnota musí být číslo");

    }

    if ($masid > 0) {
      //$form->addTextArea('deldesc', 'Popis:', 100, 5);
    } else {
      $form->addText('delweightlimitfrom', 'Hmotnost od:', 30, 10)
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FLOAT, "Hmotnost od: hodnota musí být číslo");
      $form->addText('delweightlimitto', 'Hmotnost do:', 30, 10)
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FLOAT, "Hmotnost do: hodnota musí být číslo");

      //$form->addCheckbox('delspecdel', 'individuální doprava');

      $form->addCheckbox('delpricelimit', 'drobná doprava do 500Kč');

      $form->addText('delurlparcel', 'URL sledování zásilky:', 130);
    }

    $form->addTextArea('deltext1', "Popis CZ", 100, 2);
    if ($this->secondCurrency) {
      $form->addTextArea('deltext2', "Popis SK", 100, 2);
    }

    $form->addText('delorder', 'Pořadí:', 30, 10)
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::INTEGER, "Pořadí: hodnota musí být celé číslo");

    $form->addSelect('delstatus', 'Status:', $deliveryMode->getEnumDelStatus());

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');

    return $form;
  }


  protected function createComponentEditDelFreeForm() {
    $delid = (int)$this->getParameter('id');
    $masid = (int)$this->getParameter('masid');

    $discount = new \Model\DiscountsModel();

    //nactu stavajici slevy
    $rows = dibi::fetchAll("SELECT * FROM discounts WHERE distypid='delfree' AND disdelid=%i", $delid);

    $form = $this->createAppForm();
    $conItems = $form->addContainer('items');
    foreach ($rows as $key => $row) {
      $con = $conItems->addContainer($row->disid);
      $con->addHidden("disdelid", $delid);
      $con->addHidden("distypid", 'delfree');

      if ($this->secondCurrency) {
        $con->addSelect('discurid', 'Měna:', $this->getEnumCurr())
          ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit')
          ->setDefaultValue($row->discurid);
      } else {
        $con->addHidden("discurid", 1);
      }

      $con->addSelect('disprccat', 'Cenová hladina:', $this->getEnumPrcCat())
        ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit')
        ->setDefaultValue($row->disprccat);

      $con->addText('disfrom', 'Cena od:', 30)
        ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit')
        ->addRule(Nette\Forms\Form::INTEGER, "%label musí být celé číslo")
        ->setDefaultValue($row->disfrom);

      $con->addSelect('disstatus', 'Status:', $discount->getEnumDisStatus())
        ->setDefaultValue($row->disstatus);
    }

    $con = $conItems->addContainer(0);
    $con->addHidden("disdelid", $delid);
    $con->addHidden("distypid", 'delfree');

    if ($this->secondCurrency) {
        $con->addSelect('discurid', 'Měna:', $this->getEnumCurr())
          ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit')
          ->setDefaultValue($row->discurid);
      } else {
        $con->addHidden("discurid", 1);
      }

    $con->addSelect('disprccat', 'Cenová hladina:', $this->getEnumPrcCat())
      ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit');

    $con->addText('disfrom', 'Cena od:', 30)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::INTEGER, "%label musí být celé číslo");

    $con->addSelect('disstatus', 'Status:', $discount->getEnumDisStatus());

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editDelFreeFormSubmitted');

    return $form;
  }

  public function editDelFreeFormSubmitted (Nette\Application\UI\Form $form) {

    if ($form->isSubmitted()) {
      $diss = new \Model\DiscountsModel();
      $id = (int)$this->getParameter('id');
      $vals = $form->getValues();
      foreach ($vals["items"] as $key => $row) {
        try {
          if ($key == 0 && (double)$row->disfrom > 0) {
            $diss->insert($row);
          } else if ($key > 0) {
            $diss->update($key, $row);
          }
        } catch (ModelException $e) {
          $form->addError($e->getMessage());
          return false;
        }
      }
      $this->flashMessage('Uloženo v pořádku');
    }
    $this->redirect('this#freedelivery');
  }

}
