<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  Model;

final class CommentPresenter extends BasePresenter {
  /** @persistent */
  public $sText = '';

  /** @persistent */
  public $sProCode = '';

  /** @persistent */
  public $sUsrMail = '';

  public function renderDefault() {
    //seznam aktualnich upozorneni
    $coms = new \Model\CommentsModel();
    $where = "";

    if (!empty($this->sText)) $where .= " (cmtsubj like '%$this->sText%' OR cmttext like '%$this->sText%') AND ";
    if (!empty($this->sUsrMail)) $where .= " cmtmail LIKE '%$this->sUsrMail%' AND ";
    if (!empty($this->sProCode)) $where .= " procode LIKE '%$this->sProCode%' AND ";
    if (!empty($where)) {
      $where = substr($where, 0, -5);
      $where = " WHERE $where";
    }

    $dataSource = $coms->getDataSource("
      SELECT * FROM comments
      LEFT JOIN products ON (cmtproid=proid)
      $where 
      ORDER BY cmtdatec DESC
     ");
    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->page = $paginator->page;
    $this->template->comments = $dataRows;
    $this->template->enum_cmtcatid = $coms->getEnumCmtCatId();
  }

  public function renderEdit($id) {
    $form = $this['editForm'];
    $imageRows = array();
    if (!$form->isSubmitted()) {
      $id = $this->getParameter('id');
      $this->template->id = $id;
      $coms = new \Model\CommentsModel();
      if ($id > 0) {
        $row = $coms->load($id);
        if (!$row) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($row);
      }
    }
  }

  public function renderDelete($id) {
    if ($id > 0) {
      $coms = new \Model\CommentsModel();
      if ($coms->delete($id)) {
        $this->flashMessage('Záznam byl vymazán');
      } else {
        $this->flashMessage('Záznam nebyl vymazán', 'danger');
      }
    }
    $this->redirect('default');
  }

  protected function createComponentEditForm() {

    $coms = new \Model\CommentsModel();
    $form = $this->createAppForm();
    $id = $this->getParameter('id');

    $arr1 = array(
      '0' => 'Nezařazeno',
    );
    $arr = $coms->getEnumCmtCatId();

    $form->addSelect("cmtcatid", "Téma 1", $arr)
      ->setPrompt('');

    $form->addSelect("cmtcatid2", "Téma 2", $arr)
      ->setPrompt('');

    $form->addtext('cmtnick', 'Přezdívka:', 100)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addtext('cmtmail', 'Přezdívka:', 100);

    $form->addCheckbox('cmtsendreply', "Zaslat odpověď na email");

    $form->addtext('cmtsubj', 'Předmět:', 100)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addTextArea('cmttext', 'Text', 100, 4)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');

    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = callback($this, 'editFormSubmitted');

    return $form;
  }

  public function editFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $id = $this->getParameter('id');
      $formVals = $form->getValues();

      $coms = new \Model\CommentsModel();
      if ($id > 0) {
        $coms->update($id, $formVals);
        $this->flashMessage('Změny uloženy v pořádku');
      } else {
        $id = $coms->insert($formVals);
        $this->flashMessage('Nový záznam uložen v pořádku');
      }

      $this->redirect('edit', $id);
    }
  }

  protected function createComponentSearchForm() {
    $catalogs = new \Model\CatalogsModel();

    $form = $this->createAppForm();
    $form->addGroup("Vyhledávání");
    $form->addText("text", "Text", 30)
      ->setDefaultValue($this->sText);
    $form->addText("usrmail", "Email", 20)
      ->setDefaultValue($this->sUsrMail);
    $form->addText("procode", "Kód zboží", 10)
      ->setDefaultValue($this->sProCode);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sText= Null;
        $this->sProCode = Null;
        $this->sUsrMail = Null;
      } else {
        $vals = $form->getValues();
        $this->sText= $vals["text"];
        $this->sProCode = $vals["procode"];
        $this->sUsrMail = $vals["usrmail"];
      }
    }
    $this->redirect("default");
  }
}
