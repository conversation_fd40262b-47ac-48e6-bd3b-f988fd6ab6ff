<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  Model;

final class MailingPresenter extends BasePresenter {
  /** @persistent */
  public $page = 1;

  /** @persistent */
  public $sDateFrom = NULL;

  /** @persistent */
  public $sDateTo = NULL;

  /** @persistent */
  public $sOrderBy = 'mamdatec';

  /** @persistent */
  public $sOrderByType = 'DESC';

  public function renderDefault() {
    $mams = new \Model\MailingsModel();
    $where = "";

    if (!empty($this->sDateFrom)) $where .= " coalesce(mamdatec, mamdateu) >= '".$this->formatDateMySQL($this->sDateFrom)."' AND ";
    if (!empty($this->sDateTo)) $where .= " coalesce(mamdatec, mamdateu) <= '".$this->formatDateMySQL($this->sDateTo)."' AND ";

    if (!empty($where)) {
      $where = " WHERE ".substr($where, 0, -5);
    }

    if (!empty($this->sOrderBy)) {
      $orderBy = " ORDER BY ".$this->sOrderBy." ".$this->sOrderByType;
    } else {
      $orderBy = " ORDER BY mamdatec DESC";
    }
    $dataSource = dibi::dataSource("
    SELECT *
    FROM mailings
    $where
    $orderBy
    ");
    $paginator = $this['paginator']->paginator;
    $paginator->page = $this->page;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $this->template->dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->page = $paginator->page;

    //ciselnik statusu
    $this->template->enum_mamstatus = $mams->getEnumMamStatus();
  }

  public function renderStats($id) {
    $mams =new \Model\MailingsModel();
    $mam = $mams->load($id);
    if (!$mam) {
      throw new NBadRequestException('Záznam nenalezen');
    }
    $this->template->mailing = $mam;
    $this->template->enum_mamstatus = $mams->getEnumMamStatus();
    //statistika produkty
    $this->template->statsProducts = dibi::fetchAll("
      SELECT proid, procode, proname, COUNT(masid) AS clicks 
      FROM mailingstats 
      INNER JOIN products ON (proid=masproid)
      WHERE masmamid=%i", $id, " AND mastypid=1
      GROUP BY masproid 
      ORDER BY clicks DESC
    ");

    //statistika nakupu podle produkt - Klikl na produt a nakonec ho taky kopil (do 30-ti dnu od kliku)
    $rows = dibi::fetchAll("
      SELECT masproid, oriid, oriprice*oriqty AS oriprice, oriqty AS oriqty, ordcode, ordid 
      FROM mailingstats 
      INNER JOIN products ON (proid=masproid)
      INNER JOIN users ON (usrid=masusrid)
      INNER JOIN orditems ON (oriproid=proid AND oridatec>=masdatec)
      INNER JOIN orders ON (oriordid=ordid AND ordstatus NOT IN (5,7))
      WHERE masmamid=%i", $id, " AND mastypid=1 AND masdatec + INTERVAL 30 DAY > oridatec
      GROUP BY oriid");
    $statsProductsSale = array();
    $statsOriId = array();
    $orders = array();
    $orderIds = array();
    foreach ($rows as $row) {
      $statsOriId[$row->oriid] = $row;
      //nactu cenu objednavky bez dopravy a bez akcniho produktu
      $ordPrice = (double)dibi::fetchSingle("SELECT  SUM(oriprice*oriqty) FROM orditems WHERE oriordid=%i", $row->ordid, " AND oritypid=0 AND oriproid!=%i", $row->masproid);
      $orders[$row->masproid][$row->ordid] = array(
        'ordid' => $row->ordid,
        'ordcode' => $row->ordcode,
        'ordpricevat_other' => $ordPrice,
      );
      $orderIds[$row->ordid] = $row->ordid;
    }
    foreach ($statsOriId as $key => $row) {
      if (!isset($statsProductsSale[$row->masproid])) $statsProductsSale[$row->masproid] = array('oriprice'=> 0, 'oriqty' => 0);
      $statsProductsSale[$row->masproid]['oriprice'] += $row->oriprice;
      $statsProductsSale[$row->masproid]['oriqty'] += $row->oriqty;
      $statsProductsSale[$row->masproid]["orders"] = $orders[$row->masproid];
    }
    $this->template->statsProductsSale = $statsProductsSale;

    //statistika nakupu kdekoliv klikl a objednal - bez objednavek vyse
    $rows = dibi::fetchAll("
      SELECT ordid FROM mailingstats 
      INNER JOIN users ON (masusrid=usrid)
      INNER JOIN orders ON (masusrid=ordid || usrmail=ordmail)
      WHERE 
        masmamid=%i", $id, " AND
        ".(count($orderIds) > 0 ? " ordid NOT IN (".implode(',', $orderIds).") AND " : "")."
        orddatec > masdatec AND
        masdatec + INTERVAL 30 DAY > orddatec
      GROUP BY ordid");

    //projdu vsechny obj a zjistim co objednal
    $ordersOtherSale = array();
    foreach ($rows as $row) {
      $ordersOtherSale[] = dibi::fetch("SELECT ordid, ordcode, ordpricevat FROM orders WHERE ordid=%i", $row->ordid);
    }
    $this->template->ordersOtherSale = $ordersOtherSale;

    //statistika podle typu kliku
    $this->template->statsClickTypes = dibi::fetchAll("
      SELECT COUNT(masid) AS clicks, mastypid 
      FROM mailingstats 
      WHERE masmamid=%i", $id, "
      GROUP BY mastypid 
      ORDER BY mastypid ASC
    ");

    $mass = new \Model\MailingStatsModel();
    $this->template->enum_MasTypId = $mass->getEnumMasTypId();
  }

  public function renderDetail($id) {
    $mams =new \Model\MailingsModel();
    $this->template->mailing = $mams->load($id);
  }
  public function renderEdit($id) {
    $mams =new \Model\MailingsModel();
    $dataRow = array();
    $formData = array();
    $pagBlock = Null;
    $form = $this['editForm'];
    if ($id > 0) {
      $dataRow = $mams->load($id);
      if (!$dataRow) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }

      if (!empty($dataRow->mamdate)) $dataRow->mamdate = $this->formatDate($dataRow->mamdate);
      $formData = (array)$dataRow;
      //nactu produkty
      if (!empty($dataRow->mamproducts)) {
        $arr = explode("\n", trim($dataRow->mamproducts));
        $arr1 = explode("\n", trim($dataRow->mamdesc1));
        $arr2 = explode("\n", trim($dataRow->mamdesc2));
        $arr3 = explode("\n", trim($dataRow->mamdesc3));
        $cnt = 0;
        foreach ($arr as $proid) {
          $pro = dibi::fetch("SELECT proid, proname FROM products WHERE proid=%i", $proid);
          if ($pro) {
            $formData["products"][$cnt] = array(
              'proid' => $pro->proid,
              'proname' => $pro->proname,
              'prodesc1' => (isset($arr1[$cnt]) ? $arr1[$cnt] : ""),
              'prodesc2' => (isset($arr2[$cnt]) ? $arr2[$cnt] : ""),
              'prodesc3' => (isset($arr3[$cnt]) ? $arr3[$cnt] : ""),
            );
          }
          $cnt++;
        }
      }
    }

    $form->setDefaults($formData);
    $this->template->dataRow = $dataRow;
    $this->template->id = $id;
  }

  public function renderAutocompleteProducts() {
    $term = (string)$this->getParameter('term');
    $where = "prostatus=0 AND promasid = 0";
    if (!empty($term)) {
      $arr = explode(' ', trim($term));
      if (count($arr) > 1) {
        foreach ($arr as $key) {
          $where .= " AND proname LIKE '%$key%'";
        }
      } else {
        $where .= " AND (proname LIKE '%$term%' OR  procode LIKE '$term%')";
      }

      $this->template->rows = dibi::fetchAll("SELECT proid, procode, proname, proprice".$this->curId."a AS propricea FROM products WHERE ".$where." ORDER BY proprice".$this->curId."a DESC");
    }
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sDateFrom = Null;
        $this->sDateTo = Null;
        $this->sOrderBy = Null;
        $this->sOrderByType = Null;
      } else {
        $vals = $form->getValues();
        $this->sDateFrom = $vals["datefrom"];
        $this->sDateTo = $vals["dateto"];
        $this->sOrderBy = $vals["orderby"];
        $this->sOrderByType = $vals["orderbytype"];
      }
    }
    $this->redirect("Order:default");
  }

  protected function createComponentSearchForm() {
    $orders = new OrdersModel();
    $catalogs = new CatalogsModel();
    $admins = new AdminsModel();

    $form = $this->createAppForm();

    $form->addGroup("Vyhledávání");

    $form->addText("datefrom", "Datum od", 10)
      ->setDefaultValue($this->sDateFrom);
    $form->addText("dateto", "Datum do", 10)
      ->setDefaultValue($this->sDateTo);

    $arr = array(
      'sopdatec'=>'Data vytvoření',
    );
    $form->addSelect("orderby", "Řadit podle", $arr)
      ->setDefaultValue($this->sOrderBy);

    $arr = array(
      'ASC'=>'Vzestupně',
      'DESC'=>'Sestupně',
    );
    $form->addSelect("orderbytype", "Typ řazení", $arr)
      ->setDefaultValue($this->sOrderByType);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  protected function createComponentEditForm() {
    $id = (int)$this->getParameter('id');

    $mams =new \Model\MailingsModel();

    $form = $this->createAppForm();

    $form->addText('mamdate', 'Datum odeslání:', 10)
      ->setOption('description', 'Vyplňte ve tvaru dd.mm.rrrr');

    $labelA = 'Cena A';
    if (isset($this->neonParameters["labels"]["a"])) $labelA = $this->neonParameters["labels"]["a"];
    $labelB = 'Cena B';
    if (isset($this->neonParameters["labels"]["b"])) $labelB = $this->neonParameters["labels"]["b"];
    $labelC = 'Cena C';
    if (isset($this->neonParameters["labels"]["c"])) $labelC = $this->neonParameters["labels"]["c"];
    $labelD = 'Cena D';
    if (isset($this->neonParameters["labels"]["d"])) $labelD = $this->neonParameters["labels"]["d"];

    $form->addCheckbox('mampricea', $labelA)
      ->setDefaultValue(TRUE);
    $form->addCheckbox('mampriceb', $labelB)
      ->setDefaultValue(TRUE);
    $form->addCheckbox('mampricec', $labelC)
      ->setDefaultValue(TRUE);
    $form->addCheckbox('mampriced', $labelD)
      ->setDefaultValue(TRUE);

    $form->addCheckbox('mammaillist', "Jen přihlášení k maillistu")
      ->setDefaultValue(TRUE);

    $form->addText('mamsubject', 'Predmět emailu:', 100)
      ->addRule(Nette\Forms\Form::FILLED, '%label je nutné vyplnit');

    $form->addTextArea('mambody', 'Text emailu:', 60, 20);
    $form['mambody']->getControlPrototype()->class('mceEditor');

    $form->addContainer('products');
    for($i=0;$i<=11;$i++){
      $product = $form["products"]->addContainer($i);
      $product->addText('proid', '', 5);
      $product->addText('proname', '', 30)
        ->setAttribute('class', 'autocomplete');
      $product->addText('prodesc1', '', 100);
      $product->addText('prodesc2', '', 100);
      $product->addText('prodesc3', '', 100);
    }

    $form->addTextArea('mamfooter', 'Patička emailu:', 60, 20);
    $form['mamfooter']->getControlPrototype()->class('mceEditor');


    /*
    $form->addText('mamcouponvalue', 'Hodnota slevového kupónu:', 10)
      ->setOption('description', 'Nechte prázdné pokud nechcete generovat slevové kupóny.');
    */
    $form->addSelect("mamstatus", "Status", $mams->getEnumMamStatus());

    $form->addSubmit('save', 'Uložit');
    $form->addSubmit('test', 'Odmailovat test');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    return $form;
  }

  public function editFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $mams =new \Model\MailingsModel();
      $id = (int)$this->getParameter('id');
      $vals = $form->getValues();

      //pokud vyplni datum naformatuju jinak NULL
      $vals["mamdate"] = (empty($vals["mamdate"]) ? NULL : $this->formatDateMySQL($vals["mamdate"]));

      //nactu ID produktu
      $arrProId = array();
      $arrDesc1 = array();
      $arrDesc2 = array();
      $arrDesc3 = array();
      foreach ($vals["products"] as $product) {
        if (!empty($product->proid)) {
          $arrProId[] = $product->proid;
          $arrDesc1[] = $product->prodesc1;
          $arrDesc2[] = $product->prodesc2;
          $arrDesc3[] = $product->prodesc3;
        }
        $pro = dibi::fetch("SELECT * FROM products WHERE proid=%i", $product->proid);
        if ($pro) {
          //pripravim obrazky pokud neexistuji
          /*
          $picName = ($pro->propicname != "" ? trim($pro->propicname).'.jpg' : $pro->procode.'.jpg');
          $fileNameSrc = WWW_DIR."/pic/product/big/".$picName;
          $fileName = WWW_DIR."/pic/product/ml-small/".$picName;
          if (!file_exists($fileName) && file_exists($fileNameSrc)) {
            //udelam orez na maly rozmer
            $image = Nette\utils\Image::fromFile($fileNameSrc);
            $this->saveImage($image, WWW_DIR."/pic/product", $picName, array('200x284xslider', '200x284xml-big', '100x142xml-small'));
          }
          */
        }
      }
      $vals["mamproducts"] = implode("\n", $arrProId);
      $vals["mamdesc1"] = implode("\n", $arrDesc1);
      $vals["mamdesc2"] = implode("\n", $arrDesc2);
      $vals["mamdesc3"] = implode("\n", $arrDesc3);

      unset($vals["products"]);

      try {
        if ($mams->save($id, $vals)) {
          $this->flashMessage('Uloženo v pořádku');
        }
      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }
    }
    $this->redirect('edit', $id);
  }

  protected function createComponentMailTestForm() {
    $id = (int)$this->getParameter('id');

    $form = $this->createAppForm();

    $form->addHidden('mamid', $id);

    $form->addText('testmails', 'Testovací emaily:', 50)
      ->setOption('description', 'Vyplňte emaily oddělené čárkou');

    $form->addSubmit('test', 'Odmailovat test');
    $form->onSuccess[] = array($this, 'mailTestFormSubmitted');

    return $form;
  }

  public function mailTestFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $mams = new \Model\MailingsModel();
      $pros = new \Model\ProductsModel();
      $id = (int)$this->getParameter('id');
      $vals = $form->getValues();
      $mailing = $mams->load($vals["mamid"]);

      //naplnim produkty
      $arrPro = explode("\n", trim($mailing->mamproducts));
      $arrDesc1 = explode("\n", trim($mailing->mamdesc1));
      $arrDesc2 = explode("\n", trim($mailing->mamdesc2));
      $arrDesc3 = explode("\n", trim($mailing->mamdesc3));

      $cnt = 0;
      $products = array();
      foreach ($arrPro as $proid) {
        $pro = $pros->load($proid);
        if ($pro) {
          $pro->promamdesc1 = (isset($arrDesc1[$cnt]) ? $arrDesc1[$cnt] : "");
          $pro->promamdesc2 = (isset($arrDesc2[$cnt]) ? $arrDesc2[$cnt] : "");
          $pro->promamdesc3 = (isset($arrDesc3[$cnt]) ? $arrDesc3[$cnt] : "");
          $products[$cnt] = $pro;
          $cnt ++;
        }
      }
      //nastavim sablonu
      $mailTemplate = $this->createTemplate();
      $mailTemplate->setTranslator($this->translator);
      $mailTemplate->lang = $this->lang;
      $mailTemplate->products = $products;

      //pro cilove servery nageneruju maily do logu
      $config = dibi::query('SELECT * FROM config')->fetchPairs('cfgcode', 'cfgvalue');

      $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailing.latte');
      //adresati
      $mails = explode(',', trim($vals["testmails"], ','));
      $user = dibi::fetch("SELECT usrid, usrmail, usrdatec FROM users WHERE usrid=1");
      $mailTemplate->mailing = $mailing;
      foreach ($mails as $mailAddress) {
        //kontrola formatu mailu
        $mailAddress = trim($mailAddress);
        if (!Nette\Utils\Validators::isEmail($mailAddress)) continue;

        $mailTemplate->usrid = $user->usrid;
        $mailTemplate->loKey = md5($user->usrid.$user->usrdatec);
        $mailTemplate->usrmail = $user->usrmail;
        $mailTemplate->mamid = $mailing->mamid;
        $mailTemplate->isMail = TRUE;

        $mail = new Nette\Mail\Message();
        //$mail->mailer->commandArgs = '-f'.$config["SERVER_MAILING_MAIL"];
        $mail->setFrom($config["SERVER_MAILING_MAIL"]);
        $mail->setReturnPath($config["SERVER_MAILING_MAIL"]);
        $mail->addReplyTo($config["SERVER_MAILING_MAIL"]);
        $mail->addTo($mailAddress);
        $mail->setSubject($mailing->mamsubject);
        $mail->setHtmlBody($mailTemplate);
        try {
          $this->mailMail($mail);
        } catch (\Exception $e) {
          \Tracy\Debugger::log($e->getMessage());
          $this->flashMessage($e->getMessage(), 'danger');
        }
        unset($mail);
      }
      $this->flashMessage("Testovací mail byl odeslán");
    }
    $this->redirect('edit', $id);
  }
}
?>
