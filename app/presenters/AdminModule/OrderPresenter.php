<?php
namespace AdminModule;

use <PERSON>te,
  dibi,
  Model;

final class OrderPresenter extends BasePresenter {

  /** @persistent */
  public $sCode = '';

  /** @persistent */
  public $sName = '';

  /** @persistent */
  public $sAdmin = Null;

  /** @persistent */
  public $sStatus = '';

  /** @persistent */
  public $sCoupon = '';

  /** @persistent */
  public $sNotClosed =  true;

  /** @persistent */
  public $sOrderBy = 'orddatec';

  /** @persistent */
  public $sOrderByType = 'ASC';

  public function orderEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $orders = new \Model\OrdersModel();
      $id = $this->getParameter('id');
      $values = $form->getValues();
      //nactu si objednavku pokud existuje
      $order = false;
      if ($id > 0) {
        $order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
        $orders->setCurrency($this->currencies, $order->ordcurid);
      }

      $orders->save($id, $values);
      $order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
      $orders->recalcOrder($id);
      $this->flashMessage('Uloženo v pořádku');
    }
    $this->redirect('edit', $id);
  }

  private function  changeOrderStatus($formVals) {
      $id = $formVals["ordid"];
      unset($formVals["ordid"]);
      $orders = new \Model\OrdersModel();
      $order = $orders->load($id);
      $orders->setCurrency($this->currencies, $order->ordcurid);
      if ($order) {
        //ujistim jestli se meni status
        if ($order->ordstatus == $formVals["ordstatus"]) return(TRUE);
      }
      //pokud storno vymazu cislo fa a datum vystaveni
      if ($formVals["ordstatus"] == 5) {
        $formVals["ordinvcode"] = Null;
        $formVals["ordinvdate"] = Null;
        $this->flashMessage('Byla vystornována fa, pokud existovala.');
      } else if ($formVals["ordstatus"] == 6) {
        //nastavim zaplaceno
        $formVals["ordpaystatus"] = 1;
      } else if ($formVals["ordstatus"] == 3) {
        //nastaveno odeslano
        $formVals["ordadmid"] = $this->adminData->admid;
      }

      $orders->update($id, $formVals);
      $orders->logStatus($id, $formVals["ordstatus"], $this->adminData->admid);
      $this->flashMessage('Změna stavu provedena v pořádku [ID:'.$id.']');
      if ($formVals["ordstatus"] == 3 || $formVals["ordstatus"] == 4 || $formVals["ordstatus"] == 5) {
        //prepocitam statistiku prodejnosti
        $products = new \Model\ProductsModel();
        $products->recalcSaleStat() ;
        $this->flashMessage('Byla přepočtena statistika prodejnosti [ID:'.$id.']');
      }

      //mailuju zakaznikovi zmenu stavu jen některé stavy
      if ($formVals["ordstatus"] == 1 || $formVals["ordstatus"] == 3 || $formVals["ordstatus"] == 5 || $formVals["ordstatus"] == 2 || $formVals["ordstatus"] == 6 || $formVals["ordstatus"] == 8 || $formVals["ordstatus"] == 9) {
        $mailTemplate = $this->createTemplate();
        $orderRow = $orders->load($id);
        $mailTemplate->orderRow = $orderRow;
        $delModes = new \Model\DeliveryModesModel();
        $delModes->setCurrency($this->currencies, $orderRow->ordcurid);
        $mailTemplate->payMode = $delModes->load($orderRow->orddelid);
        $mailTemplate->delMode = $delModes->load($mailTemplate->payMode->delmasid);
        if (!empty($orderRow->ordparcode)) {
          //zjistim URL
          $url = $mailTemplate->delMode->delurlparcel;
          $url = str_replace('#CODE#', $orderRow->ordparcode, $url);
          $mailTemplate->parcelURL = $url;
        }
        $mailTemplate->enum_ordStatus = $orders->getEnumOrdStatus();
        $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailOrderChanged.latte');

        if ($formVals["ordstatus"] == 3) {
          //nastaveno odeslano - poslu SMS
          $smsTemplate = $this->createTemplate();
          $smsTemplate->delMode = $mailTemplate->delMode;
          $smsTemplate->orderRow = $orderRow;
          $smsTemplate->setFile(WWW_DIR.'/../templates/Mails/smsOrderSend.latte');
          //try {
            $this->smsSend($orderRow->ordtel, $smsTemplate);
          //} catch (InvalidStateException $e) {
          //  $this->flashMessage("Nepodařilo se odeslat informační SMS o odeslání objednávky (".$e->getMessage().")".' [ID:'.$id.']', "danger");
          //}
        }

        if ($formVals["ordstatus"] == 1 || $formVals["ordstatus"] == 3 || $formVals["ordstatus"] == 5 || $formVals["ordstatus"] == 2 || $formVals["ordstatus"] == 6 || $formVals["ordstatus"] == 8) {
          //mailuju zakaznikovi
          try {
            $this->mailSend($orderRow->ordmail, $this->translator->translate("Změna stavu objednávky č.")." ".$orderRow->ordcode, $mailTemplate);
          } catch (Nette\InvalidStateException $e) {
            $this->flashMessage("Nepodařilo se odeslat informační email o změně stavu objednávky (".$e->getMessage().")".' [ID:'.$id.']', "danger");
          }
        }
      }
  }


  public function orderChangeStateFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $formVals = $form->getValues();
      $formVals["ordid"] = $this->getParameter('id');
      $this->changeOrderStatus($formVals);
    }
    $this->redirect('default');
  }

  /********************* view default *********************/

  public function renderDefault() {
    $orders = new \Model\OrdersModel();
    //$where = "orddatec + INTERVAL 3 DAY >= Now() AND ";
    $where = "";
    if (!empty($this->sCode)) $where .= " ordcode LIKE '%$this->sCode%' AND ";
    if (!empty($this->sAdmin)) $where .= " ordadmid = ".$this->sAdmin." AND ";
    if (!empty($this->sName)) $where .= " (ordilname LIKE '%$this->sName%' OR ordstlname LIKE '%$this->sName%' OR ordstfirname LIKE '%$this->sName%' OR ordifirname LIKE '%$this->sName%') AND ";
    if ($this->sNotClosed) $where .= " ordstatus NOT IN (4,5,7) AND ";
    if ((string)$this->sStatus!='') $where .= " ordstatus=$this->sStatus AND ";
    if (!empty($this->sCoupon)) $where .= " ordcoucode = '$this->sCoupon' AND ";
    if (!empty($where)) {
      $where = " WHERE ".substr($where, 0, -5);
    }

    if (!empty($this->sOrderBy)) {
      $orderBy = " ORDER BY ".$this->sOrderBy." ".$this->sOrderByType;
    } else {
      $orderBy = " ORDER BY orddatec ASC";
    }
    $dataSource = dibi::dataSource("
    SELECT orders.*, d.delname AS delname, dm.delname AS delnamemas, admname,
    (SELECT orldatec FROM orders_log WHERE orlordid=ordid AND orlstatus=3 ORDER BY orldatec DESC LIMIT 1) AS datesend,
    (SELECT MAX(oriprobigsize) FROM orditems WHERE oriordid=ordid) AS oriprobigsize,
    (SELECT MAX(oriprooffer) FROM orditems WHERE oriordid=ordid) AS oriprooffer
    FROM orders
    LEFT JOIN deliverymodes AS d ON (orddelid=d.delid)
    LEFT JOIN deliverymodes AS dm ON (d.delmasid=dm.delid)
    LEFT JOIN admins ON (ordadmid=admid)
    $where
    GROUP BY ordid
    $orderBy
    ");

    $paginator = $this['paginator']->paginator;
    $paginator->itemsPerPage = $this->config["ADMIN_ROWSCNT"];
    $paginator->itemCount = $dataSource->count();
    $dataRows = $dataSource->applyLimit($paginator->itemsPerPage, $paginator->offset)->fetchAll();
    $this->template->page = $paginator->page;
    $this->template->dataRows = $dataRows;

    /*
    foreach ($dataRows as $row) {
      $row->bl = $orders->blAnalyse($row);
    }
    */

    //ciselnik statusu
    $this->template->enum_ordstatus = $orders->getEnumOrdStatus();
  }

  public function renderAutocompleteProducts() {
    $term = $this->getParameter('term');
    if (!empty($term)) $this->template->rows = dibi::fetchAll("SELECT proid, procode, proname, proprice".$this->curId."a AS propricea FROM products WHERE proismaster=0 AND proname LIKE '%$term%' OR  procode LIKE '$term%'");
  }

  public function renderEdit($id) {
    $form = $this['orderEditForm'];
    $formState = $this['orderChangeStateForm'];

    if (!$form->isSubmitted() && !$formState->isSubmitted()) {
      $orders = new \Model\OrdersModel();
      $dataRow = $orders->load($id);
      if (!$dataRow) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen');
      }
      //naformatuju datum

      $this->template->ordPayType = dibi::fetch("SELECT * from deliverymodes where delid=%i", $dataRow->orddelid);
      $ordDelType = dibi::fetch("SELECT * from deliverymodes where delid=%i", $this->template->ordPayType->delmasid);

      //zkontroluji platné odběrné místa ulozenky
      if (!empty($dataRow->orddelspec)) {
        $dels = new Model\DeliveryModesModel();

        if ($ordDelType->delcode === "ULOZENKA") {
          $arr = $dels->getEnumUlozenkaPlaces();
          if (!array_key_exists($dataRow->orddelspec, $arr)) {
            $this->template->message = "Chybně zadáno odběrné místo!!! ($dataRow->orddelspec)";
            unset($dataRow->orddelspec);
          }
        } else if ($ordDelType->delcode === "WEDO") {
          $arr = $dels->getEnumWedoPlaces();
          if (!array_key_exists($dataRow->orddelspec, $arr)) {
            $this->template->message = "Chybně zadáno odběrné místo!!! ($dataRow->orddelspec)";
            unset($dataRow->orddelspec);
          }
        }
      }

      $form->setDefaults($dataRow);
      $formState->setDefaults($dataRow);

      $this->template->dataRow = $dataRow;

      //doplnim polozky objednavky
      $this->template->ordItems = dibi::query("SELECT * FROM orditems LEFT JOIN products ON (proid=oriproid) WHERE oriordid=%i", $dataRow->ordid)->fetchAssoc('oriid');

      //doplnim polozku se slevou
      $this->template->ordItemDisc = dibi::fetch("SELECT * from orditems where oriordid=%i AND oritypid=3", $dataRow->ordid);
      //načtu zpusb dopravy

       //doplnim log zmen
      $this->template->statusLog = dibi::fetchAll("
      SELECT orldatec, orlstatus, admname AS orladmname, orlnote
      FROM orders_log
      LEFT JOIN admins ON (admid=orladmid)
      WHERE
      orlordid=%i", $dataRow->ordid, "ORDER BY orlid DESC");

      $this->template->enum_ordstatus = $orders->getEnumOrdStatus();

      //kontrola cerne listiny
      $this->template->bl = $orders->blAnalyse($dataRow);

      //eet data
      $this->template->eetRows = dibi::fetchAll('SELECT * FROM eet_log WHERE eet_log.logordid=%i', $dataRow->ordid);

    }
  }

  public function actionDelete($id) {
    if ($id > 0) {
      $orders = new \Model\OrdersModel();
      $orders->delete($id);
      $this->flashMessage('Záznam byl vymazán');
    }
    $this->redirect('default');
  }

  public function actionCsobReverse($ordid) {
    $ords = new \Model\OrdersModel();
    $ord = $ords->load($ordid);
    if (!empty($ord->ordpaymentid)) {
      $config = $this->neonParameters["csob"];
      $csob = new \CsobPayment($config);
      if ($csob->paymentReverse($ord->ordpaymentid)){
        $this->flashMessage("Platbu se podařilo zrušit");
      } else {
        $this->flashMessage("Platbu se NEpodařilo zrušit", 'err');
      }
    } else {
      $this->flashMessage("ID platby se nepodařilo zjistit", 'err');
    }
    $this->redirect("edit", $ordid);
  }

  public function actionMakeInvoice($id) {
    if ($id > 0) {
      $orders = new \Model\OrdersModel();
      try {
        $orders->makeInvoice($id);
        $this->flashMessage('Faktura byla vystavena');
      } catch (ModelException $e) {
        $this->flashMessage($e->getMessage(), "danger");
      }
    }
    $this->redirect('edit', $id);
  }

  public function actionPrint($id) {
    if ($id > 0) {
      $this->printOrder($id, 'D');
    }
    //$this->redirect('default');
  }

  public function actionExportPohodaXph($id) {
    if ($id > 0) {
      $ordCode = dibi::fetchSingle("SELECT ordcode FROM orders WHERE ordid=%i", $id);
      $xml=$this->exportPohodaXph($id);
      header('Content-Description: File Transfer');
      header('Cache-Control: public, must-revalidate, max-age=0');
      header('Pragma: public');
      header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
      header('Last-Modified: '.gmdate('D, d M Y H:i:s').' GMT');
      header('Content-Type: application/force-download');
      header('Content-Type: application/octet-stream', false);
      header('Content-Type: application/download', false);
      header('Content-Type: application/xml', false);
      if (!isset($_SERVER['HTTP_ACCEPT_ENCODING']) OR empty($_SERVER['HTTP_ACCEPT_ENCODING'])) {
        // don't use length if server using compression
        header('Content-Length: '.strlen($xml));
      }
      header('Content-disposition: attachment; filename="'.$ordCode.'.xph"');
    }
    echo $xml;
    $this->terminate();
  }

  public function actionPrintInvoice($id, $target='I') {
    if ($id > 0) {
      $this->printOrder($id, $target, 'Invoice.latte');
    }
    //$this->redirect('default');
  }

  public function actionBatchAction() {
    $ulozenka = $this->getParameter('export_ulozenka');
    if (isset($ulozenka)) {
      $ids = $this->getParameter('ordid');
      $this->redirect(':Front:Export:ulozenka', array('ids'=>$ids));
    }
    $changeStatus = $this->getParameter('change_status');
    if (isset($changeStatus)) {
      $status = $this->getParameter('ordstatus');
      $this->redirect('batchStatus', array('statuses'=>$status));
    }

    $sendWedo = $this->getParameter('send_wedo');
    if (isset($sendWedo)) {
      $ids = $this->getParameter('ordid');

      $this->sendToWeDo($ids);

      $this->redirect('default');

    }
  }

  public function actionBatchStatus(array $statuses) {
    foreach ($statuses as $key => $value) {
      $this->changeOrderStatus(array('ordid'=>$key, 'ordstatus'=>$value));
    }
    $this->redirect('default');
  }

  public function actionDeleteItem($id, $ordid) {
    if ($id > 0) {
      $orders = new \Model\OrdersModel();
      $ordItems = new \Model\OrdItemsModel();
      $ordItems->delete($id);
      $orders->recalcOrder($ordid);
      $this->flashMessage('Položka byla vymazána');
    }
    $this->redirect('edit', $ordid);
  }

  /********************* facilities *********************/
  protected function createComponentOrderChangeStateForm() {
    $order = new \Model\OrdersModel();
    $form = $this->createAppForm();

    $enum_ordstatus = $order->getEnumOrdStatus();
    /*
    //načtu zpusb dopravy
    $ordPayType = dibi::fetch("SELECT * from deliverymodes where delid=%i", $ord->orddelid);
    $ordDelType = dibi::fetch("SELECT * from deliverymodes where delid=%i", $ordPayType->delmasid);


    //podle typu dopravy vyhodím některé statusy
    if ($ordDelType->delcode === 'OSOBNE') {
      unset($enum_ordstatus[3]);
    }

    $id = (int)$this->getParameter('id');
    $ord = $order->load($id);
    */

    $form->addSelect("ordstatus", "Nový stav objednávky", $enum_ordstatus);
    $form->addSubmit('newstate', 'Zmenit stav');
    $form->onSuccess[] = array($this, 'orderChangeStateFormSubmitted');
    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }


  protected function createComponentOrderEditForm() {
    $order = new \Model\OrdersModel();
    $dels = new \Model\DeliveryModesModel();
    $enums = new \Model\EnumcatsModel();
    $admins = new \Model\AdminsModel();
    $enumCountries = $enums->getEnumCountries(false);
    $id = $this->getParameter('id');
    $ord = $order->load($id);
    $payMode = $dels->load($ord->orddelid);
    $delMode = $dels->load($payMode->delmasid);

    $form = $this->createAppForm();

    $form->addGroup('');
    $form->addCheckbox("ordpaystatus", "Zaplaceno");
    $form->addText("ordpricevat", "Celková cena:", 15)
      ->setDisabled(True);
    $form->addText("orddisc", "Celková sleva:", 15)
      ->setDisabled(True);

    $form->addText("ordweight", "Celková hmotnost:", 15)
      ->setDisabled(True);

    $form->addSelect("ordadmid", "Obchodník:", $admins->getEnumAdmins())
      ->setPrompt('');

    $form->addText("ordparcode", "Číslo balíku:", 15);

    $form->addText("orddiscpercent", "Sleva v %:", 15)
      ->setRequired(FALSE)
      ->addRule(Nette\Forms\Form::NUMERIC, "Sleva v procentech musí být číslo;");

    $form->addText("ordcoucode", "Slevový kupón:", 15);

    $form->addselect("orddelid", "Doprava / platba:", $order->getEnumOrdDelId(FALSE))
      ->addRule(Nette\Forms\Form::FILLED, "Způsob dopravy musí být vyplněný")
      ->setPrompt('Vyberte ...');

    if ($delMode->delcode == 'ULOZENKA') {
      $form->addselect("orddelspec", "Uloženka:", $dels->getEnumUlozenkaPlaces())
        ->setPrompt("Nutno zadat ...");
    }

    if ($delMode->delcode == 'WEDO') {
      $form->addselect("orddelspec", "WEDO:", $dels->getEnumWedoPlaces())
        ->setPrompt("Nutno zadat ...");
    }

    $form->addText("ordmail", "Email:", 20);
    $form->addText("ordtel", "Telefon:", 10);

    $form->addGroup('Fakturační adresa');

    $form->addText("ordiname", "Jméno:", 60);
    $form->addText("ordilname", "Přijmení:", 60);
    $form->addText("ordifirname", "Název firmy:", 60);

    $form->addText("ordistreet", "Ulice:", 60);
    $form->addText("ordistreetno", "Číslo popisné:", 60);
    $form->addText("ordicity", "Město, obec:", 60);
    $form->addText("ordipostcode", "PSČ:", 6);
    //$form->addSelect("ordicouid", "Země:", $enumCountries);

    $form->addText("ordic", "IČ:", 10);
    $form->addText("orddic", "DIČ:", 10);
    $form->addCheckbox("ordusrvat", "Plátce DPH");

    $form->addGroup('Dodací adresa');

    $form->addText("ordstname", "Jméno:", 60);
    $form->addText("ordstlname", "Přijmení:", 60);
    $form->addText("ordstfirname", "Název firmy:", 60);

    $form->addText("ordststreet", "Ulice:", 60);
    $form->addText("ordststreetno", "Číslo popisné:", 60);
    $form->addText("ordstcity", "Město, obec:", 60);
    $form->addText("ordstpostcode", "PSČ:", 6);
    //$form->addSelect("ordstcouid", "Země:", $enumCountries);

    $form->addGroup('Poznámka');

    $form->addTextArea("ordnote", "", 100, 3);
    /*
    $form->addGroup('Fakturační údaje');
    $form->addText("ordinvcode", "Číslo faktury:", 10);
    //$form->addText("ordinvdate", "Datum vystavení:", 10);
    */

    $form->addSubmit('makeorder', 'Uložit');
    $form->onSuccess[] = array($this, 'orderEditFormSubmitted');
    return $form;
  }

  protected function createComponentOrdItemsEditForm() {
    $form = $this->createAppForm();
    $ordid = (int)$this->getParameter("id");
    //nactu polozky objedmavky
    if ($ordid > 0) {
      $form->addContainer('items');
      $rows = dibi::fetchAll("SELECT * FROM orditems WHERE oritypid=0 AND oriordid=%i", $ordid, " ORDER BY oriname");
      foreach ($rows as $row) {
        $key = 'item_'.$row->oriid;
        $form["items"]->addContainer($key);
        $form["items"][$key]->addHidden("oriid", $row->oriid);
        $form["items"][$key]->addText("oriproid", "", 5)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ID zboží.')
          ->setDefaultValue($row->oriproid);
        $form["items"][$key]->addText("oriname", "", 50)
          ->setAttribute('class', 'autocomplete')
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte název položky.')
          ->setDefaultValue($row->oriname);
        $form["items"][$key]->addText("oriprice", "", 5)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte cenu položky.')
          ->setDefaultValue($row->oriprice);
        $form["items"][$key]->addText("sn", "", 20);
        $form["items"][$key]->addText("oriqty", "", 3)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte počet položek.')
          ->setDefaultValue($row->oriqty);
      }
      //postovne
      $deliv = dibi::fetch("SELECT * FROM orditems WHERE oritypid=1 AND oriordid=%i", $ordid, " ORDER BY oriname");
      if ($deliv!==false) {
        $form['items']->addContainer('delivery');
        $form['items']['delivery']->addHidden("oriid", $deliv->oriid);
        $form['items']['delivery']->addHidden("oritypid", 1);
        $form['items']['delivery']->addHidden("oriqty", 1);
        $form['items']['delivery']->addText("oriname", "", 50)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte popis poštovného.')
          ->setDefaultValue($deliv->oriname);
        $form['items']['delivery']->addText("oripricemaster", "", 5)
          ->setDefaultValue($deliv->oripricemaster);
      }
      //nova polozka
      $form->addContainer('newitem');
      $form['newitem']->addHidden("oriordid", $ordid);
      $form['newitem']->addText("oriproid", "", 5);
      $form['newitem']->addText("oriname", "", 50)
        ->setAttribute('class', 'autocomplete');
      $form['newitem']->addText("oriprice", "", 5);
      $form['newitem']->addText("oriqty", "", 3)
        ->setDefaultValue(1);
    }
    $form->addSubmit('saveitems', 'Uložit');
    $form->onSuccess[] = array($this, 'ordItemsEditFormSubmitted');

    return $form;
  }

   public function ordItemsEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $ordid = (int)$this->getParameter("id");
      $formVals = $form->getValues(TRUE);

      $orders = new \Model\OrdersModel();
      $products = new \Model\ProductsModel();
      $ordItems = new \Model\OrdItemsModel();
      //nejdrive zjistim jestli nepipnul S/N
      $isSn = false;
      foreach ($formVals['items'] as $item) {
        if (!empty($item["sn"])) {
          $isSn = true;
          dibi::query("UPDATE orditems SET orisn=CONCAT(COALESCE(orisn, ''), IF(orisn IS NOT NULL, '|', ''), '".$item["sn"]."') WHERE oriid=%i", $item["oriid"]);
        }
      }
      if ($isSn) $this->redirect('edit#edititems', $ordid);

      foreach ($formVals['items'] as $item) {
        $id = $item["oriid"];
        unset($item["oriid"]);
        unset($item["sn"]);
        if (isset($item["oripricemaster"])) {
          if (trim($item["oripricemaster"]) == "") $item["oripricemaster"] = NULL;
        }
        if (!empty($item["oriproid"])) {
          $product = dibi::fetch("SELECT * FROM products WHERE proid=%i", $item["oriproid"]);
          $item["oriprocode"] = $product->procode;
          $item["oriprocode2"] = $product->procode2;
          $item["orivatid"] = $product->provatid;
        }
        $ordItems->update($id, $item);

      }
      if (!empty($formVals['newitem']['oriname'])) {
        if (!empty($formVals['newitem']["oriproid"])) {
          $product = dibi::fetch("SELECT * FROM products WHERE proid=%i", $formVals['newitem']["oriproid"]);
          $formVals['newitem']["oriprocode"] = $product->procode;
          $formVals['newitem']["oriprocode2"] = $product->procode2;
          $formVals['newitem']["orivatid"] = $product->provatid;
          $oriid = $ordItems->insert($formVals['newitem']);

          //pokud má dárek vložím dárek
          $gifts = $products->getGifts($product);
          if (!empty($gifts)) {
            foreach ($gifts as $grow) {
              if ($grow) {
                $gifori = array();
                $gifori['orioriid'] = $oriid;
                $gifori['oriordid'] = $ordid;
                $gifori['oriproid'] = $grow->proid;
                $gifori['oriprocode'] = $grow->procode;
                $gifori['oriprocode2'] = $grow->procode2;
                $gifori['oritypid'] = 0;
                $gifori['oriname'] = $grow->proname;
                $gifori['oriprice'] = 0;
                $gifori['oripriceoriginal'] = 0;
                $gifori['orivatid'] = $grow->provatid;
                $gifori['oricredit'] = $grow->procredit;
                $gifori['oriqty'] = $formVals['newitem']["oriqty"];
                $gifori['oriprobigsize'] = $grow->probigsize;
                $gifori['oriprooffer'] = $grow->prooffer;
                $ordItems->insert($gifori);
              }
            }
          }

        } else {
          $this->flashMessage("Nelze vložit položku, která není v databázi. Id není vyplněno.", "danger");
          $this->redirect('edit', $ordid);
        }
      }
      $orders->recalcOrder($ordid);

      $this->flashMessage("Položky objednávky byly aktualizovány, celková cena objednávky byla prepočítána.");
    }
    $this->redirect('edit#edititems', $ordid);
  }

  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) {
        $this->sCode = Null;
        $this->sName = Null;
        $this->sAdmin = Null;
        $this->sStatus = Null;
        $this->sCoupon = Null;
        $this->sNotClosed = Null;
        $this->sOrderBy = Null;
        $this->sOrderByType = Null;
      } else {
        $vals = $form->getValues();
        $this->sCode = $vals["code"];
        $this->sName = $vals["name"];
        $this->sAdmin = $vals["admin"];
        $this->sStatus = $vals["status"];
        $this->sCoupon = $vals["coupon"];
        $this->sNotClosed = $vals["notclosed"];
        $this->sOrderBy = $vals["orderby"];
        $this->sOrderByType = $vals["orderbytype"];
      }
    }
    $this->redirect("Order:default");
  }

  protected function createComponentSearchForm() {
    $orders = new \Model\OrdersModel();
    $catalogs = new \Model\CatalogsModel();
    $admins = new \Model\AdminsModel();

    $form = $this->createAppForm();
    $form->addGroup("Vyhledávání");
    $form->addText("code", "Kód objednávky", 10)
      ->setDefaultValue($this->sCode);
    $form->addText("name", "Příjmení nebo název firmy", 10)
      ->setDefaultValue($this->sName);
    $form->addSelect("admin", "Obchodník", $admins->getEnumAdmins())
      ->setPrompt('')
      ->setDefaultValue($this->sAdmin);
    $form->addSelect("status", "Stav", $orders->getEnumOrdStatus())
      ->setPrompt('');
    if (!empty($this->sStatus)) $form["status"]->setDefaultValue($this->sStatus);

    $form->addText("coupon", "Slevový kupón", 10)
      ->setDefaultValue($this->sCoupon);

    $form->addCheckbox("notclosed", "Neuzavřené")
      ->setDefaultValue($this->sNotClosed);

    $arr = array(
      'orddatec'=>'Data vytvoření',
    );
    $form->addSelect("orderby", "Řadit podle", $arr)
      ->setDefaultValue($this->sOrderBy);

    $arr = array(
      'ASC'=>'Vzestupně',
      'DESC'=>'Sestupně',
    );
    $form->addSelect("orderbytype", "Typ řazení", $arr)
      ->setDefaultValue($this->sOrderByType);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vyčistit');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }

  protected function createComponentOrderQuickForm() {
    $order = new \Model\OrdersModel();
    $enums = new \Model\EnumcatsModel();
    $admins = new \Model\AdminsModel();
    $form = $this->createAppForm();

    $form->addHidden("ordadmid", $this->adminData->admid);

    $form->addText("ordiname", "Jméno:", 60)
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno");
    $form->addText("ordilname", "Přijmení:", 60)
      ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněno");

    $form->addselect("orddelid", "Doprava / platba:", $order->getEnumOrdDelId())
      ->addRule(Nette\Forms\Form::FILLED, "Způsob dopravy musí být vyplněný")
      ->setPrompt('Vyberte ...');

    if ($this->secondCurrency) {
      $form->addselect("ordcurid", "Měna:", $this->getEnumCurr())
        ->addRule(Nette\Forms\Form::FILLED, "%label musí být vyplněná")
        ->setPrompt('Vyberte ...');
    } else {
      $form->addHidden("ordcurid", 1);
    }

    $form->addText("ordtel", "Telefon:", 30);
    $form->addText("ordmail", "Email:", 30)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addSubmit('makeorder', 'Vytvořit objednávku a upravit položky');
    $form->onSuccess[] = array($this, 'orderQuickFormSubmitted');
    return $form;
  }

  public function orderQuickFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $values = $form->getValues();
      $orders = new \Model\OrdersModel();
      $dels = new \Model\DeliveryModesModel();
      $dels->setCurrency($this->currencies, $values["ordcurid"]);
      $orders->setCurrency($this->currencies, $values["ordcurid"]);
      $id = $orders->insert($values);

      //vlozim dopravu
      //nactu si zpusob dopravy

      $paymode = $dels->load($values["orddelid"]);
      $delmode = $dels->load($paymode->delmasid);
      $delValues['oriordid'] = $id;
      $delValues['oritypid'] = 1;
      $delValues['oriproid'] = 0;
      $delValues['oriname'] = "Doprava: ".$delmode->delname." - ".$paymode->delname;
      $delValues['oriprice'] = $paymode->delprice;
      $delValues['orivatid'] = Null;
      $delValues['oricredit'] = 0;
      $delValues['oriqty'] = 1;
      $delValues['oriprobigsize'] = 0;
      $delValues['oriprooffer'] = 0;
      $orditems = new \Model\OrdItemsModel();
      $orditems->insert($delValues);
      $orders->recalcOrder($id);
      $this->flashMessage('Uloženo v pořádku');
      $this->redirect('edit#edititems', $id);
    }
  }

  protected function sendToWeDo($ids) {
    $weDoApi = new \WedoApi();
    $weDoApi->getImportData($ids, $this->config["CURR_RATE"]);
    $this->terminate();
  }
}
