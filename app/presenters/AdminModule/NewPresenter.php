<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  Model;

final class NewPresenter extends BasePresenter {

  public function renderDefault() {
    //seznam aktualnich upozorneni
    $new = new \Model\NewsModel();
    $where = "";

    $this->template->news = $new->fetchAll("SELECT * FROM news ORDER BY newdate DESC");
  }

  public function renderEdit($id) {
    $form = $this['editForm'];
    $imageRows = array();
    if (!$form->isSubmitted()) {
      $id = $this->getParameter('id');
      $this->template->id = $id;
      $new = new \Model\NewsModel();
      $this->template->mainImageName = "";
      if ($id > 0) {
        $row = $new->load($id);
        if (!$row) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
        $newdate = New \DibiDateTime($row->newdate);
        $row->newdate = $newdate->format('d.m.Y');
        $form->setDefaults($row);
        //vlozim hlavni obrazek
        $fileName = 'new_'.$id.'.jpg';
        if (file_exists(WWW_DIR."/pic/new/list/$fileName")) $this->template->mainImageName = $fileName;
      }
    }
  }

  public function renderDelete($id) {
    if ($id > 0) {
      $product = new \Model\NewsModel();
      if ($product->delete($id)) {
        @unlink(WWW_DIR."/pic/list/new_".$id.".jpg");
        @unlink(WWW_DIR."/pic/detail/new_".$id.".jpg");
        $image = new \Model\ImagesModel();
        $image->deleteObjImages('new', $id);
      }

      $this->flashMessage('Záznam byl vymazán');
    }
    $this->redirect('default');
  }

  public function editFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $id = $this->getParameter('id');
      $formVals = $form->getValues();

      $product = new \Model\NewsModel();
      $image = null;
      if ($formVals["imageMain"]->isOk()) $image = $formVals["imageMain"]->toImage();
      unset($formVals["imageMain"]);

      $formVals['newdate'] = NEW \DibiDateTime($formVals['newdate']);
      if ($id > 0) {
        $product->update($id, $formVals);
        $this->flashMessage('Změny uloženy v pořádku');
      } else {
        $id = $product->insert($formVals);
        $this->flashMessage('Nová novinka uložen v pořádku');
      }
      if (isset($image)) {
        $sizes = (array($this->config["NEWPICSIZE_BIG"].'xbig', $this->config["NEWPICSIZE_DETAIL"].'xdetail', $this->config["NEWPICSIZE_LIST"].'xlist'));
        //ulozim obrazek
        $this->saveImage($image, WWW_DIR."/pic/new", 'new_'.$id.".jpg", $sizes);
      }
      $this->redirect('edit', $id);
    }
  }


  /********************* facilities *********************/


  protected function createComponentEditForm() {

    $product = new \Model\NewsModel();
    $form = $this->createAppForm();
    $id = $this->getParameter('id');

    //doplnim ciselnik katalogu
    $catalogs = new \Model\CatalogsModel();


    $form->addtext('newtitle', 'Titulek:', 100, $product->getColProperty('newtitle', 'size'))
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte titulek.');

    $form->addtext('newdate', 'Datum:', 10, $product->getColProperty('newdate', 'size'))
      ->setOption('description', 'dd.mm.rrrr');

    $form->addTextArea('newannot', 'Anotace', 100, 4)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte text anotaci.');

    $form->addTextArea('newtext', 'Text', 100, 8)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte text novinky.');
    $form['newtext']->getControlPrototype()->class('mceEditor');

    $form->addUpload("imageMain", "Hlavní obrázek:")
      ->setOption('description', 'Nahráním nového obrázku ten původní přepíšete');

    $form->getElementPrototype()->onsubmit('tinyMCE.triggerSave()');
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = callback($this, 'editFormSubmitted');

    return $form;
  }
}
