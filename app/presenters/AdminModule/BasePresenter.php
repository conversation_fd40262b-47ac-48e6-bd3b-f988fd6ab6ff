<?php
namespace AdminModule;

use <PERSON><PERSON>,
  dibi,
  <PERSON>;

use IPub\VisualPaginator\Components as VisualPaginator;

abstract class BasePresenter extends \BasePresenter {
  /** @persistent */
  public $backlink = '';

  const LOGIN_NAMESPACE = 'admin';
  /** identita */
  protected $user;
  protected $adminData = Null;

  protected function startup() {
    parent::startup();

    // autorizace administratora
    $this->user = $this->getUser();
    $this->user->getStorage()->setNamespace(self::LOGIN_NAMESPACE);

    if ($this->user->isLoggedIn() && $this->action == "login") {
      $this->redirect('Admin:default');
    } else {

      if ($this->action != "login" && !($this->name === 'Admin:WidexImport' && $this->action == "import")) {
        if (!$this->user->isLoggedIn()) {
          if ($this->user->getLogoutReason() === Nette\Security\IUserStorage::INACTIVITY) {
            $this->flashMessage('Byl/a jste odhlášen/a z důvodu delší neaktivity.');
          }
          $backlink = $this->storeRequest();
          $this->redirect('Admin:login', $backlink);
        }
      }
    }
    $this->adminData = false;
    if ($this->user->isLoggedIn()) {
      $admins = new \Model\AdminsModel();
      $this->adminData = $admins->load($this->user->id);
    }

    if (!$this->adminData) {
      $this->user->logout();
      $this->adminData = new \DibiRow(array('admid'=>0));
    }

    //kontrola prav
    if (!$this->user->isAllowed($this->name, $this->action)) {
      $this->flashMessage('Nemáte povolen přístup. ('.$this->name.':'.$this->action.')', "danger");
      $this->redirect('Admin:default');
    }



  }

  protected function beforeRender() {
    //nactu administratora
    $this->template->identity = $this->user;
    $this->template->admin = $this->adminData;

    //nactu nazev DB ripojeny
    $config = dibi::getConnection()->getConfig();
    $this->template->dbName  = $config["database"];

    if (isset($this->neonParameters["adminModule"])) {
      $this->template->neonConfigAdminModule = $this->neonParameters["adminModule"];
    }

    parent::beforeRender();
  }

  protected function saveImage($image, $path, $filename, $sizes) {
    //upravim a ulozim obrazek
    if (isset($image) && is_array($sizes)) {
      foreach ($sizes as $size) {
        $img = clone $image;
        $arr = explode("x", $size);
        $w = 0;
        $h = 0;
        $dir = '';
        if (isset($arr[0])) $w = (int)$arr[0];
        if (isset($arr[1])) $h = (int)$arr[1];
        if (isset($arr[2])) $dir = $arr[2];

        if ($w > 0 || $h > 0) {
          $img->resize($w, $h); // resize, který prostor vyplní a možná překročí
            //->crop('50%', '50%', $w, $h); // ořezání po stranách
          $blank = Nette\Utils\Image::fromBlank($w, $h, Nette\Utils\Image::rgb(255, 255, 255));
          $blank->place($img, '50%', '50%');

          $watermarkFile = $path.($dir!="" ? '/'.$dir : '').'/watermark.png';
          if (file_exists($watermarkFile)) {
            $watermark = Nette\Utils\Image::fromFile($watermarkFile);
            $blank->place($watermark, '50%', '50%');
          }
          $quality = 75;
          if ($size == 'detail' || $size == 'big') {
            $quality = 90;
          }
          $blank->save($path.($dir!="" ? '/'.$dir : '').'/'.$filename, $quality, Nette\Utils\Image::JPEG);
        }
      }
    }
  }

  /**
   * pro našeptávač produktů
   */
  public function renderAutocompleteProducts() {
    $term = $this->getParameter('term');
    if (!empty($term)) $this->template->rows = dibi::fetchAll("SELECT proid, procode, proname, proprice".$this->curId."a AS propricea FROM products WHERE proismaster=0 AND proname LIKE '%$term%' OR  procode LIKE '$term%'");
  }

  /**
  * Create items paginator
  *
  * @return \IPub\VisualPaginator\Components\Control
  */
  function createComponentPaginator(){
    // Init visual paginator
		$control = new \IPub\VisualPaginator\Components\Control;
		$control->setTemplateFile(WWW_DIR . '/../templates/AdminModule/@paginator.latte');
		$control->disableAjax();
		return $control;
  }

  protected function createAppForm() {
    $form = new Nette\Application\UI\Form();
    $form->setTranslator($this->translator);
    $form = $this->setBootstrapForm($form);
    return $form;
  }

}
