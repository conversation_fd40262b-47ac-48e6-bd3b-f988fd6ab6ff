<?php

abstract class BasePresenter extends Nette\Application\UI\Presenter {
  /**
   * @var MobileDetect
   */
  protected $mobileDetect;

  /**
   * @var Helpers\DeviceView
   */
  protected $deviceView;

  /**
   * @var \TemplateFilters @inject
   */
  public $myTemplateFilters;

  /** nastaveni serveru */
  public $config = array();

  /** nastaveni z neonu */
  public $neonParameters = array();

  /** aktualni mena */
  public $currency = array();

  /** vsechny aktivni meny */
  public $currencies = array();

  /** id aktualni meny */
  public $curId = 0;

  /** id aktualni meny */
  public $curKey = '';

  /** kody men */
  public $curCodes = array();


  /** zda eshop vyuziva dve meny */
  public $secondCurrency = FALSE;

  /** prekladac */
  protected $translator;

  public $lang = 'cs';

  /** @var Nette\Mail\IMailer @inject */
  public $mailer;

  public function injectNeonParametersRepository(Classes\NeonParametersRepository $paramRepository) {
    $this->neonParameters = $paramRepository->getParameters();
  }

  public function injectMobileDetector(\IPub\MobileDetect\MobileDetect $mobileDetect, \IPub\MobileDetect\Helpers\DeviceView $deviceView) {
    $this->mobileDetect  = $mobileDetect;
    $this->deviceView  = $deviceView;
  }

  protected function startup() {
    parent::startup();
    $curId = 1;

    if (isset($this->neonParameters["hosts"][$_SERVER["SERVER_NAME"]]["curId"])) {
      $curId =$this->neonParameters["hosts"][$_SERVER["SERVER_NAME"]]["curId"];
    }

    $this->currency = $this->neonParameters["currency"][$curId];
    $this->currencies = $this->neonParameters["currency"];
    $this->curId = $this->currency["id"];
    $this->curKey = $this->currency["key"];

    $this->secondCurrency = isset($this->neonParameters["currency"][2]);
    $this->curCodes[1] = $this->neonParameters["currency"][1]["code"];
    $this->curCodes[2] = "";
    if (isset($this->neonParameters["currency"][2]["code"])) $this->curCodes[2] = $this->neonParameters["currency"][2]["code"];
    //nactu nastaveni z datatabaze
    //nactu uzivatelske nastaveni do cache
    $config = new \Model\ConfigModel();
    $this->config = $config->getConfig();

    //pro SK jen platbu kartou
    if (isset($this->neonParameters["onlinePayTypes"]) && $this->curId == 2) {
      $payTypeOnline = $this->neonParameters["onlinePayTypes"];
      foreach ($payTypeOnline as $key => $val) {
        if ($key != 'c') unset($payTypeOnline[$key]);
      }
      $this->neonParameters["onlinePayTypes"] = $payTypeOnline;
    }

    //nastaveni prekladani
    $this->lang = $this->config["SERVER_LANGUAGE"];
    $dic = array();
    If ($this->lang != 'cs') {
      //nactu slovnik
      //pokud neni cache vytvorim - mela by byt vzdy vytvorena
      $dic = DictionariesModel::getDictionary($this->lang);
    }
    $this->translator = new MyTranslator($this->lang, $dic);
  }

  protected function beforeRender() {
    //predzvykam velikosti obrazku
    $size = explode('x', $this->config["PROPICSIZE_LIST"]);
    $this->template->picListWidth = $size[0];
    $this->template->picListHeight = $size[1];

    $size = explode('x', $this->config["PROPICSIZE_DETAIL"]);
    $this->template->picDetailWidth = $size[0];
    $this->template->picDetailHeight = $size[1];

    $size = explode('x', $this->config["PROPICSIZE_BIG"]);
    $this->template->picBigWidth = $size[0];
    $this->template->picBigHeight = $size[1];

    $this->template->secondCurrency = $this->secondCurrency;
    $this->template->curKey = $this->curKey;

    if (isset($this->neonParameters["labels"])) $this->template->neonLabels = $this->neonParameters["labels"];
    $this->template->curId = $this->curId;
  }

  public function exportPohodaXph($id) {
    $ords = new \Model\OrdersModel();
    $template = $this->createTemplate();
    $template->order = $ords->load($id);
    $delModes = new \Model\DeliveryModesModel();
    $delModes->setCurrency($this->currencies, $template->order->ordcurid);

    $template->payMode = $delModes->load($template->order->orddelid);
    if ($template->payMode) $template->delMode = $delModes->load($template->payMode->delmasid);
    $template->ordItems = dibi::fetchAll("
    SELECT * 
    FROM orditems 
    LEFT JOIN products ON (oriproid=proid)
    WHERE oriordid=%i", $id, " ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END");
    $template->setFile(WWW_DIR.'/../templates/Mails/pohodaXph.latte');
    return (string)$template;
  }


  public function printOrder($id, $dest="I", $templateName='Order.latte') {
    $enums = new \Model\EnumcatsModel();
    $delModes = new \Model\DeliveryModesModel();
    $orders = new \Model\OrdersModel();
    $template = $this->getTemplate();

    $template->order = dibi::fetch("SELECT *, ordinvdate + INTERVAL 14 DAY AS datepay FROM orders WHERE ordid=%i", $id);
    //nastavim menu objednavky
    $orders->setCurrency($this->currencies, $template->order->ordcurid);
    $delModes->setCurrency($this->currencies, $template->order->ordcurid);

    $ordItems = dibi::fetchAll("SELECT * FROM orditems LEFT JOIN products ON (oriproid=proid) WHERE oriordid=%i", $id, "  ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END, orivatid");

    $vatLev[0] = (double)$this->config["VATTYPE_0"]/100;
    $vatLev[1] = (double)$this->config["VATTYPE_1"]/100;
    $delRow = Null;
    $vatSum = 0;
    $sum = 0;
    $vatSumaryPro[0] = array();
    $vatSumaryPro[0]['price'] = 0;
    $vatSumaryPro[0]['vat'] = 0;
    $vatSumaryPro[0]['vatLevel'] = $this->config["VATTYPE_0"];
    $vatSumaryPro[0]['pricevat'] = 0;

    $vatSumaryPro[1] = array();
    $vatSumaryPro[1]['price'] = 0;
    $vatSumaryPro[1]['vat'] = 0;
    $vatSumaryPro[1]['vatLevel'] = $this->config["VATTYPE_1"];
    $vatSumaryPro[1]['pricevat'] = 0;

    foreach ($ordItems as $key => $row) {
      $priceV = (double)$ordItems[$key]->oriprice*(double)$ordItems[$key]->oriqty-(double)$ordItems[$key]->oridisc;
      if ($row->oritypid == 0) {
        //jedna se o zbozi
        $vat = round($priceV*$vatLev[$ordItems[$key]->orivatid], 2);
        $ordItems[$key]->oripricecntvat = $priceV;
        $ordItems[$key]->oripricecntnovat = $priceV - ($priceV*($row->orivatid == 1 ? 0.1304 : 0.1736));
        $ordItems[$key]->oricntvat = $ordItems[$key]->oripricecntvat - $ordItems[$key]->oripricecntnovat;
        // sumarizace DPH
        $vatSumaryPro[$row->orivatid]['price'] += $ordItems[$key]->oripricecntnovat;
        $vatSumaryPro[$row->orivatid]['vat'] += $ordItems[$key]->oricntvat;
        $vatSumaryPro[$row->orivatid]['pricevat'] += $ordItems[$key]->oripricecntvat;
        $vatSum += $ordItems[$key]->oripricecntvat;
        $sum += $ordItems[$key]->oripricecntnovat;
      } else if ($row->oritypid == 1) {
        $delRow = $row;
      }
    }
    //rozpocitam dopravu
    $vatSumaryDel[0] = array();
    $vatSumaryDel[0]['price'] = 0;
    $vatSumaryDel[0]['vat'] = 0;
    $vatSumaryDel[0]['pricevat'] = 0;
    $vatSumaryDel[1] = array();
    $vatSumaryDel[1]['price'] = 0;
    $vatSumaryDel[1]['vat'] = 0;
    $vatSumaryDel[1]['pricevat'] = 0;
    if ($vatSumaryPro[0]['pricevat'] > 0) {
      $vatSumaryDel[0]['pricevat'] = round($vatSumaryPro[0]['pricevat']/$vatSum*(double)$delRow->oriprice, 2);
      $vatSumaryDel[0]['price'] = round($vatSumaryDel[0]['pricevat'] - ($vatSumaryDel[0]['pricevat']*0.1736), 2);
      $vatSumaryDel[0]['vat'] = $vatSumaryDel[0]['pricevat']-$vatSumaryDel[0]['price'];
    }
    if ($vatSumaryPro[1]['pricevat'] > 0) {
      $vatSumaryDel[1]['pricevat'] = round($vatSumaryPro[1]['pricevat']/$vatSum*(double)$delRow->oriprice, 2);
      $vatSumaryDel[1]['price'] = round($vatSumaryDel[1]['pricevat'] - ($vatSumaryDel[1]['pricevat']*0.1304), 2);
      $vatSumaryDel[1]['vat'] = $vatSumaryDel[1]['pricevat']-$vatSumaryDel[1]['price'];
    }

    $template->ordItems = $ordItems;
    $template->vatSumaryPro = $vatSumaryPro;
    $template->vatSumaryDel = $vatSumaryDel;

    $template->ordItemDelivery = dibi::fetch("SELECT * FROM orditems WHERE oritypid=1 AND oriordid=%i", $id);
    $template->payDate = dibi::fetchSingle("SELECT ordinvdate + INTERVAL 14 DAY FROM orders WHERE ordid=%i", $id);
    $template->enum_delmode = $orders->getEnumOrdDelIdSimple();
    $template->payMode = $delModes->load($template->order->orddelid);
    $template->delMode = $delModes->load($template->payMode->delmasid);
    $template->enum_countries = $enums->getEnumCountries();
    $template->setFile(WWW_DIR.'/../templates/pdf/'.$templateName);
    $fname = 'objednavka-'.$template->order->ordcode;
    // mPDF
    require(LIBS_DIR."/mpdf/mpdf.php");
    $mpdf = new mPDF('utf-8','A4', 12,'',10,10,10,10,9,9,'P');
    $mpdf->useOnlyCoreFonts = true;
    $mpdf->SetDisplayMode('real');
    $mpdf->SetAutoFont(0);
    $template->headers = (object) NULL;
    $pdfHtml = (string) $template; // vyrenderujeme šablonu už nyní
    $mpdf->AddPage('P');
    $mpdf->WriteHTML($pdfHtml, 2);
    $mpdf->SetHTMLFooter('<p style="text-align: center; font-size: 12"><small></small></p>');
    if ($dest=="I") {
      $name = TEMP_DIR."/".$fname.".pdf";
    } else {
      $name = $fname.".pdf";
    }
    $mpdf->Output($name, $dest);
  }

  protected function getVerifyCode($length = 6) {
    $base = "abcdefghjkmnpqrstwxyz123456789";
    $max = strlen($base)-1;
    $string = "";
    mt_srand((double)microtime()*1000000);
    while (strlen($string) < $length) $string .= $base[mt_rand(0,$max)];
    return $string;
  }

  protected function mailSend($mailTo, $subject, $bodyTemplate, $mailFrom="", $attachments=array()) {
    if (empty($mailTo)) return (True);
    $mail = new Nette\Mail\Message();
    if ($mailFrom == "") $mailFrom = $this->config["SERVER_MAIL"];
    $mail->setFrom($this->config["SERVER_NAMESHORT"].' <'.$mailFrom.'>');
    $mail->addTo($mailTo);
    $mail->setSubject($subject." - ".$this->config["SERVER_NAMESHORT"]);
    $mail->setHtmlBody($bodyTemplate);

    //doplnim prilohy pokud jsou
    foreach ($attachments as $fileName) {
      $mail->addAttachment($fileName);
    }

    try {
      if ($_SERVER["SERVER_NAME"] === '127.0.0.1') {
        $mailer = $this->mailer;
        $mailer->send($mail);
      } else {
        $mailer = new Nette\Mail\SendmailMailer;
        $mailer->send($mail);
      }
    } catch (Exception $e) {
      return false;
    }
    return true;
  }

  protected function mailMail($mail) {
    if ($_SERVER["SERVER_NAME"] === '127.0.0.1') {
      $mailer = $this->mailer;
      $mailer->send($mail);
    } else {
      $mailer = new Nette\Mail\SendmailMailer;
      $mailer->send($mail);
    }
  }

  public function injectMailer(Nette\Mail\IMailer $mailer) {
    $this->mailer = $mailer;
  }

  protected function smsSend($gsm, $bodyTemplate) {
    include_once LIBS_DIR.'/sms/SMSlib.php';
    if (empty($gsm)) return (True);
    //if ($_SERVER["SERVER_NAME"] == '') return(TRUE);
    $text = (string)$bodyTemplate;

    $smsConfig = $this->neonParameters['sms'];
    if (!empty($smsConfig["login"])) {
      $sms = new sms($smsConfig["login"], $smsConfig["passw"]);
      $gsm = trim($gsm);
      $gsm = substr($gsm, -9);
      return ($sms->send('+420'.$gsm, $text, $this->config["SERVER_MAIL"]));
    } else {
      return(TRUE);
    }
  }

  /**
   * Formats view template file names.
   * @return array
   */
  public function formatTemplateFiles() {
    $root = WWW_DIR . '/../templates';
    $name = $this->getName();
    $presenter = substr($name, strrpos(':' . $name, ':'));
    $module = substr($name, 0, (int) strrpos($name, ':')).'Module';

    return array(
      "$root/$module/$presenter.$this->view.latte",
    );
  }

  public function getPriceVat($price, $vatid) {
    $vatLevel = (int)$this->config["VATTYPE_".$vatid];
    return($price * (1+($vatLevel / 100)));
  }

  /**
   * Formats layout template file names.
   * @return array
   */
  public function formatLayoutTemplateFiles() {

    $root = WWW_DIR . '/../templates';
    $name = $this->getName();
    $presenter = substr($name, strrpos(':' . $name, ':'));
    $module = substr($name, 0, (int) strrpos($name, ':')).'Module';
    $layout = $this->layout ? $this->layout : 'layout';
    return array(
      "$root/$module/@$layout.latte",
      "$root/@$layout.latte",
    );
  }

  public function formatNumberMySQL($value) {
    $value = str_replace(',', '.', $value);
    $value = str_replace(' ', '', $value);
    return $value;
  }

  /**
  * formátuje datum do SQL formátu
  *
  * @param strund $date datum ve formátu dd.mm.rrr
  */
  public function formatDateMySQL($date) {
    $d = "";
    $m = "";
    $y = "";

    if (!empty($date) && strpos($date, '.') > 0) {
      list($d, $m, $y) = explode('.', $date);
    } else {
      return($date);
    }
    return(trim($y).'-'.trim($m).'-'.trim($d));
  }

  /**
  * formátuje datum do dd.mm.rrr
  *
  * @param string $date datum SQL formátu
  */
  public function formatDate($date) {
    $d = "";
    $m = "";
    $y = "";
    list($date, $time) = explode(' ', $date);
    if (!empty($date) && strpos($date, '-') > 0) {
      list($y, $m, $d) = explode('-', $date);
    } else {
      return($date);
    }
    return($d.'.'.$m.'.'.$y);
  }

  public function createTemplate($class = NULL) {
    $template = parent::createTemplate($class);

    $template->addFilter(NULL, [
      $this->myTemplateFilters,
      'loader'
    ]);

    // Add mobile detect and its helper to template
    $template->_mobileDetect    = $this->mobileDetect;
    $template->_deviceView      = $this->deviceView;

    $template->config = $this->config;
    $template->setTranslator($this->translator);
    $template->lang = $this->lang;
    return $template;
  }

  /**
  * číselník na meny
  *
  */
  public function getEnumCurr() {
    $items = array();
    if ($this->secondCurrency) {
      $items = array(
        1 => $this->curCodes[1],
        2 => $this->curCodes[2]
      );
    }
    return $items;
  }


  /**
  * sazby DPH
  *
  */
  public function getVatLevels() {
    $items = array();
    for ($i = 0; $i <= 3; $i++) {
      if (isset($this->config["VATTYPE_$i"])) {
        $items[$i] = (int)$this->config["VATTYPE_$i"];
      }
    }
    return $items;
  }

  /**
  * číselník cenove kategorie
  *
  */
  public function getEnumPrcCat() {
    $usrs = new \Model\UsersModel();
    $labels = array();
    if (isset($this->neonParameters["labels"])) $labels = $this->neonParameters["labels"];
    return $usrs->getEnumUsrPrcCat($labels);
  }

  function curl_get_contents($url) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    $data = curl_exec($ch);
    curl_close($ch);
    return $data;
  }

  protected function setBootstrapForm($form) {
    $renderer = $form->getRenderer();
    $renderer->wrappers['controls']['container'] = NULL;
    $renderer->wrappers['pair']['container'] = 'div class=form-group';
    $renderer->wrappers['pair']['.error'] = 'has-error';
    $renderer->wrappers['control']['container'] = 'div class=col-sm-10';
    $renderer->wrappers['label']['container'] = 'div class="col-sm-2 control-label"';
    $renderer->wrappers['control']['description'] = 'span class=help-block';
    $renderer->wrappers['control']['errorcontainer'] = 'span class=help-block';
    $renderer->wrappers['error']['container'] = '';
    $renderer->wrappers['error']['item'] = 'div class="alert alert-danger" role=alert';

    $form->getElementPrototype()->class('form-horizontal');
    $form->onRender[] = function ($form) {
      foreach ($form->getControls() as $control) {
        $type = $control->getOption('type');
        if ($type === 'button') {
          $control->getControlPrototype()->addClass(empty($usedPrimary) ? 'btn btn-primary' : 'btn btn-default');
          $usedPrimary = TRUE;
        } elseif (in_array($type, ['text', 'textarea', 'select'], TRUE)) {
          $control->getControlPrototype()->addClass('form-control');
        } elseif (in_array($type, ['checkbox', 'radio'], TRUE)) {
          $control->getSeparatorPrototype()->setName('div')->addClass($type);
        }
      }
    };
    return $form;
  }

  protected function passwordHash($password) {
    $hash = Nette\Security\Passwords::hash($password); // Zahashuje heslo
    return $hash;
  }

  protected function passwordVerify($password, $hash) {
    return Nette\Security\Passwords::verify($password, $hash);
  }

  public function actionCsobPaymentStart($ordid, $key) {
    $ords = new \Model\OrdersModel();
    $ord = $ords->load($ordid);
    $key2 = substr(md5($ord->ordid.$ord->orddatec), 0, 6);
    if ($key2 != $key) {
      $this->flashMessage("Neplatné volání platební brány", "error");
      $this->redirect("default");
    }
    $config = $this->neonParameters["csob"];
    $csob = new \CsobPayment($config);

    $rows = dibi::fetchAll('SELECT * FROM orditems WHERE oriordid=%i', $ord->ordid);
    $payId = NULL;

    $vatInfo = $this->getVatLevels();
    $response = $csob->paymentInit($ord, $rows, $vatInfo, $this->link("//:Front:PaymentCsob:return"));
    if ($response === false) {
      $this->flashMessage($csob->lastError);
      $this->redirect("default");
    } else {
      header('Location: ' . $csob->paymentUrl);
    }
    $this->terminate();
  }

  public function actionCsobPaymentStatus($ordid) {
    $ords = new \Model\OrdersModel();
    $ord = $ords->load($ordid);
    if (!empty($ord->ordpaymentid)) {
      $this->redirect(":Front:PaymentCsob:status", $ord->ordpaymentid);
    } else {
      $this->flashMessage("ID platby se nepodařilo zjistit", 'err');
      $this->redirect("default");
    }
  }


    /**
   * @param string $string
   * @return string
   */
  public function iconv2win1250($string) {
    $string = \Nette\Utils\Strings::fixEncoding($string);
    $string = iconv("utf-8//IGNORE", "windows-1250//IGNORE", $string);
    return ($string);
  }

  /**
   * @param string $string
   * @return string
   */
  public function iconv2utf8($string) {
    $string = iconv("windows-1250//IGNORE", "utf-8//IGNORE", $string);
    $string = \Nette\Utils\Strings::fixEncoding($string);
    return ($string);
  }

  public function formatNumber($value) {
    $value = str_replace('.', ',', $value);
    return $value;
  }
}
