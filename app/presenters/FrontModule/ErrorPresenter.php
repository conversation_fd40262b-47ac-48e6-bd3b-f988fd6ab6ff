<?php
//TODO pořešit fungování Error Presenteru i v př<PERSON>padě, že nepojede databáze - error presenter v případě chyby 500 nemůže být potomkem BasePresenteru


namespace FrontModule;
use dibi;
use Nette;

final class ErrorPresenter extends BasePresenter {

	/**
	 * @param  Exception
	 * @return void
	 */
	public function renderDefault($exception) {
		if ($this->isAjax()) { // AJAX request? Just note this error in payload.
			$this->payload->error = TRUE;
			$this->terminate();

		} elseif ($exception instanceof \Nette\Application\BadRequestException) {
      $this->template->dataRow = dibi::fetch("SELECT * FROM pages where pagurlkey='nenalezeno-404' LIMIT 1");
			$this->setView('404'); // load template 404.latte

		} else {
			$this->setView('500'); // load template 500.latte
			\Nette\Diagnostics\Debugger::log($exception, \Nette\Diagnostics\Debugger::ERROR); // and handle error by Nette\Debug
		}
	}

  /*
  protected function shutdown($response) {
    $exception = $this->params['exception'];
    NDebug::processException($exception); // pozor, volá exit()
  }
  */
}
