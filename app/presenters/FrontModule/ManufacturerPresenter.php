<?php
namespace FrontModule;
use dibi;
use Nette;

final class ManufacturerPresenter extends BasePresenter {
  /** @persistent */
  public $t = array(); //typy

  /** @persistent */
  public $o = 'pa'; //řazeni

  public function renderDetailOld($key) {
    $name = str_replace('-', ' ', $key);
    //zkusím najít výrobce s tím jménem
    $man = dibi::fetch("SELECT * FROM manufacturers WHERE manname=%s", $name);
    if ($man) {
      $urlkey = Nette\Utils\Strings::webalize($man->manname);
      $this->redirect(301, 'Manufacturer:detail', array('id'=>$man->manid, 'key'=>$urlkey));
    } else {
      throw new Nette\Application\BadRequestException('Položka nenalezena', '404');
    }
  }


  public function renderDetail($id, $key) {
    $this->o = $this->getParameter('o');
    $this->t = $this->getParameter('t');


    $mans = new \Model\ManufacturersModel();
    $man = $mans->load($id);
    if ($man === false) {
      throw new Nette\Application\BadRequestException('Položka nenalezena', '404');
    } else{
      //kontrola platnosti URL
      $urlkey = Nette\Utils\Strings::webalize($man->manname);

      //pokud se zmenil klic presmeruju na novy
      if ($key != $urlkey) $this->redirect(301, 'Manufacturer:detail', array('id'=>$id, 'key'=>$urlkey));
      $this->template->man = $man;
    }

    //sestavim WHERE
    $orderBy = "ORDER BY ";
    switch ($this->o) {
       case '':
         $orderBy .= " proorder";
         break;
       case 'na':
         $orderBy .= " proname ASC";
         break;
       case 'nd':
         $orderBy .= " proname DESC";
         break;
       case 'pa':
         $orderBy .= " IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) ASC";
         break;
       case 'pd':
         $orderBy .= " IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) DESC";
         break;
       default:
         $orderBy .= " proorder";
    }
    //nactu do filtru podminky
    $where = "WHERE promasid=0 AND prostatus=0 AND promanid=".(int)$id;

    //typy
    if (count($this->t) > 0) {
      $tWhere = "";
      foreach ($this->t as $key => $value) {
        switch ($key) {
          case 'sk': //Skladem
            $tWhere = $tWhere."proaccess=0 OR ";
            break;
          case 'no': //Novinky
            $tWhere = $tWhere."protypid2=1 OR ";
            break;
          case 'ak': //Akce
            $tWhere = $tWhere."protypid=1 OR ";
            break;
          case 'dz': //Doprava zdarma
            $tWhere = $tWhere."prodelfree=1 OR ";
            break;
          case 'ti': //tip
            $tWhere = $tWhere."protypid3=1 OR ";
            break;
          case 'zd': //zlate dny
            $tWhere = $tWhere."protypid4=1 OR ";
            break;
        }
      }
      if (!empty($tWhere)) $tWhere = " AND (".substr($tWhere, 0, -4).")";
      $where = $where.$tWhere;
    }


    $sql = "SELECT proid, protypid, promasid, proismaster, protypid2, protypid3, protypid4, protypid5, prokey, procode, proname AS proname1, 
    concat(proname, ' ', proname2) AS proname, proname2, propicname, prodescs, proaccess, proaccesstext, proqty, proprice".$this->curId."com AS pricecom, 
    IF(proprice".$this->curId.$this->userData->usrprccat.">0,proprice".$this->curId.$this->userData->usrprccat.",proprice".$this->curId."a) AS proprice, 
    proprice".$this->curId."a AS propricea, proprice".$this->curId."b AS propriceb, proprice".$this->curId."c AS propricec, proprice".$this->curId."d AS propriced, 
    proprice".$this->curId."com AS propricecom, provatid, prostatus, proorder, manname, prorating, ".($this->curId == 1 ? "prodelfree" : "0 AS prodelfree").", pronotdisc,
    catid, catname, catkey, catpathids
FROM products
INNER JOIN catplaces ON (proid=capproid)
INNER JOIN catalogs ON (capcatid=catid)
INNER JOIN manufacturers ON (promanid=manid)
$where
GROUP BY proid
$orderBy";
    $productsData = dibi::fetchAll($sql);
    $rootCats = dibi::fetchAll("SELECT * FROM catalogs WHERE catlevel=2 ORDER BY catorder");
    $productsByCatId = array();
    foreach ($productsData as $key => $row) {
      //zjistim korenovou kategorii
      $catpathids = $row->catpathids;
      $arr = explode('|', trim($catpathids, '|'));
      $rootId = 0;
      if (!empty($arr[1])) $rootId = (int)$arr[1];
      if ($rootId > 0)$productsByCatId[$rootId][] = $row;
    }

    $this->template->productsData = $productsData;
    $this->template->rootCats = $rootCats;
    $this->template->productsByCatId = $productsByCatId;

    //naplnim filtr vybranymi hodnotami
    $arr = array();
    if (!empty($this->t)) $arr["t"] = $this->t;
    if (!empty($this->o)) $arr["o"] = $this->o;
    if (!empty($arr)) {
      $form = $this->getComponent("manufacturerSearchForm");
      $form->setDefaults($arr);
    }

  }

  public function renderDefault() {
    //seznam aktualnich upozorneni
    $mans = new \Model\ManufacturersModel();
    $this->template->mans = $mans->fetchAll("SELECT * FROM manufacturers ORDER BY manname");
  }

  protected function createComponentManufacturerSearchForm() {
    $form = $this->createAppForm();

    $pros = new \Model\ProductsModel();

    $arr = $this->getEnumTypes();
    $container = $form->addContainer('t');
    foreach ($arr as $key=>$name) {
      $container->addCheckbox($key, $name);
    }

    $form->addSubmit('search', 'Filtrovat')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'manufacturerSearchFormSubmitted');
    return $form;
  }

  public function manufacturerSearchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $this->t = array();
      foreach ($vals['t'] as $key => $val) {
        if ($val) $this->t[$key] = $key;
      }
      $this->redirect('this');
    }
  }

  /**
  * číselínk pro filtrování Types
  *
  */
  private function getEnumTypes() {
    return array(
      'sk' => 'Skladem',
      'ak' => 'Akce',
      'no' => 'Novinka',
      'ti' => 'Tip',
      'zd' => 'Zlaté dny',
      'dz' => 'Doprava zdarma',
    );
  }

}
