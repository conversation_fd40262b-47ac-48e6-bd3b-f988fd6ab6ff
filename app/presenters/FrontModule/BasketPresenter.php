<?php
namespace FrontModule;
use dibi;
use <PERSON>ureka\ShopCertification\Exception;
use Nette;

final class BasketPresenter extends BasePresenter {

  /** @persistent */
  public $delid = NULL;

  private $deliveryModeRows;
  private $basketOrderOnOnePage;

  protected function startup() {

    parent::startup();
    $this->basketOrderOnOnePage = (bool)$this->neonParameters["app"]["basketOrderOnOnePage"];
    $this->recalc();
  }

  protected function beforeRender() {
    parent::beforeRender();
    $this->template->showMenuLeft = FALSE;
  }

  /**
   * vymaze polozku z kosiku
   *
   * @param integer $proid id polozky
   */
  public function actionDelete($proid) {
    if (array_key_exists($proid, $this->basketNamespace->items)) {
      $this->basketDeleteItem($proid);
      $this->recalc();
    }
    $this->redirect('default');
  }

  /**
   * vymaze kupon z kosiku
   *
   * @param integer $proid id polozky
   */
  public function actionDeleteCoupon() {
    $this->basketNamespace->coupon = array();
    $this->recalc(true);
    $this->redirect('default');
  }

  /**
  * prida polozku do kosiku
  *
  * @param integer $proid id polozky
  * @param integer $count pocet kusu
  */
  public function actionAdd($proid, $count=NULL) {
    if ($count === NULL) $count = 1;
    if (!array_key_exists($proid, $this->basketNamespace->items)) {
      //kontrola zda je zbozi skladem a zda je aktivni
      $products = new \Model\ProductsModel();
      $row = $products->load($proid);
      if ($row) {
        //polozka nalezna v db a je aktivni
        if ((int)$row->prostatus != 0) {
          $this->flashMessage("Zboží nejde přidat do košíku, je momentálně vyřazeno z nabídky.", "danger");
        } else if ((int)$row->proqty == 0 && $this->config["CHECK_STOCK"] == 1) {
          $this->flashMessage("Zboží nejde přidat do košíku, není momentálně skladem.", "danger");
        } else {
          if ((int)$row->proqty < $count && $this->config["CHECK_STOCK"] == 1) {
            $this->flashMessage(array("Položka byla přídána do košíku, ale v menším počtu, skladem je pouze",$row->proqty,"ks"), "danger");
            $count = (int)$row->proqty;
          }
          $this->basketNamespace->items[$proid] = $count;
        }
      }
    }
    $this->redirect('default');
  }

  public function renderAccepted() {
    $orders = new \Model\OrdersModel();
    $ordid = $this->basketNamespace->ordid;
    if (empty($ordid)) {
      $this->redirect("default");
    }
    $order = $orders->load($ordid);
    if ($order) {
      $order->items = dibi::fetchAll("SELECT orditems.*, catpath, catpathids, procode, proname FROM orditems
INNER JOIN products ON (oriproid=proid)
LEFT JOIN catplaces ON (oriproid=capproid)
LEFT JOIN catalogs ON (catid=capcatid)
WHERE oriordid=%i GROUP BY oriid", $order->ordid);
      $priceSum = 0;
      $priceSumVat = 0;
      $price = 0;
      foreach ($order->items as $key=>$row) {
        $price = 0;
        $vatType = (string)$this->config["PRICEVAT"];
        if ($vatType == 'inclvat') {
          $priceSumVat += $row->oriprice * $row->oriqty;
          $vatLevel = (int)$this->config["VATTYPE_".$row->orivatid];
          $price = round($row->oriprice / (1+($vatLevel / 100)) * $row->oriqty, 2);
          $order->items[$key]->oripricenovat = $price;
          $priceSum += $price;
        } else {
          $price = $row->oriprice * $row->oriqty;
          $priceSum += $price;
          $order->items[$key]->oripricenovat = $price;
          $priceSumVat += round($this->getPriceVat($row->oriprice, $row->orivatid) * $row->oriqty, 2);
        }
      }

      $order->ordpricenovat = $priceSum;
      $order->ordpriceinclvat = $priceSumVat;
      $order->orddelprice = dibi::fetchSingle("SELECT oriprice FROM orditems WHERE oritypid=1 AND oriordid=%i", $order->ordid);
    }
    $this->template->payment = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $order->orddelid);
    $this->template->delivery = dibi::fetch("SELECT * FROM deliverymodes WHERE delid=%i", $this->template->payment->delmasid);
    if ($this->template->payment->delcode == 'cetelem') {
      $cetelem  = new Cetelem($this->config["CETELEM"]["kodProdejce"]);
      $cetelem->cenaZbozi = $order->ordpricevat;
      $this->template->cetelem = $cetelem;
    }
    $this->template->order = $order;
    $this->template->blockOrderAccepted = dibi::fetch("SELECT * FROM pages WHERE pagurlkey='objednavka_odeslana' AND pagstatus=0");

    //pokud je platba prevodem, nactu QR kod a info k platbe
    if ($this->template->payment->delcode == 'paybefore') {
      $accNo = $this->config["SERVER_ACCNO"];
      $qrPlatba = new \QRPlatba($accNo, $order->ordpricevat);
      $qrPlatba->setVariableSym($order->ordcode);
      $qrPlatba->setMessage($order->ordiname." ".$order->ordilname.", ".$order->ordicity);
      if ($this->curId == 2) $qrPlatba->setCurrency("EUR");
      $this->template->qrPlatba = urlencode((string)$qrPlatba);
    }

    //payu
    $payUPar = $this->neonParameters["payU".$this->curId];
    if (!empty($payUPar["posId"])) {
      if (!empty($order->ordpaycode)) {
        $payuCode = $order->ordpaycode;
        $payUPar["clientIp"] = $_SERVER['REMOTE_ADDR'];
        //testovací platba
        //$payuCode = 't';
        $pos_id = $payUPar["posId"];
        $key1 = $payUPar["key1"];
        $pos_auth_key = $payUPar["posAuthKey"];
        $ts = time();
        $session_id = $order->ordid.'-'.$ts;
        $value = $order->ordpricevat * 100;
        $desc = $this->config["SERVER_NAMESHORT"].' c. '.$order->ordcode;
        $desc2 = '';

        $sig = md5($pos_id.$payuCode.$session_id.$pos_auth_key.$value.$desc.$desc2.$order->ordcode.$order->ordiname.$order->ordilname.$order->ordistreet.' '.$order->ordistreetno.$order->ordicity.$order->ordipostcode.$order->ordmail.'cs'.$payUPar["clientIp"].$ts.$key1);

        $payUPar["sig"] = $sig;
        $payUPar["ts"] = $ts;
        $payUPar["sesId"] = $session_id;
        $payUPar["value"] = $value;
        $payUPar["payCode"] = $payuCode;
        $payUPar["desc"] = $desc;

        //nastavim session id
        $orders->update($order->ordid, array('ordsesid' => $session_id));
        $this->template->payUPar = $payUPar;
      }
    }

    if ($_SERVER["SERVER_NAME"] != '127.0.0.1' && $this->currency["key"] == 'CZK') {
      if ((int)$order->ordheurekagdpr === 1) {
        //zavolam heureku, overeno zakazniky
        $heurekaConfig = $this->neonParameters["heureka"];
        if (count($order->items) > 0 && !empty($heurekaConfig["IdOverenoZakazniky"])) {
          try {
            $overeno = new \Heureka\ShopCertification($heurekaConfig["IdOverenoZakazniky"]);
            $overeno->setOrderId($order->ordid);
            $overeno->setEmail($order->ordmail);
            foreach ($order->items as $row) {
              $overeno->addProductItemId($row->oriproid);
            }
            $overeno->logOrder();
            $this->template->heurekaConfig = $heurekaConfig;
          } catch (Exception $e) {
            // handle errors
            $mailText = "ORDID: $order->ordid, msg:".$e->getMessage();
            $mail = new Nette\Mail\Message();
            $mail->setFrom($this->config["SERVER_MAIL"]);
            $mail->addTo('<EMAIL>');
            $mail->setSubject('nová objednávka - heureka ERR');
            $mail->setHtmlBody($mailText);
            $mail->send();
          }
        }
      }
    }
    $this->basketNamespace->remove();
  }

  public function renderDefault() {
    $this->basketNamespace->delid = 0;
    $form = $this->getComponent("basketForm");
    $product = new \Model\ProductsModel();
    $product->setPrcCat($this->userData->usrprccat);
    $product->setCurrency($this->currencies, $this->curId);
    $this->template->blockPromoRegistrace = dibi::fetch("SELECT * FROM pages WHERE pagurlkey='promo_registrace' AND pagstatus=0");
    if (!$form->isSubmitted()) {
      if (count($this->basketNamespace->items) > 0) {
        $productRows = dibi::query($product->getSqlList("proid IN (".implode(",", array_keys($this->basketNamespace->items)).")"))->fetchAssoc('proid');
        foreach ($productRows as $key => $productRow) {
          $productRows[$key]->proprice = $this->getCouponProductPrice($productRow);
          $productRows[$key]->gifts = array();
          if (!empty($productRow->progifts)) {
            //nactu darky
            $arr = explode(',', trim($productRow->progifts, ','));
            $productRows[$key]->gifts = dibi::fetchAll("SELECT proid, proname, prokey FROM products WHERE procode IN (%s)", $arr);
          }
        }
        $this->template->productRows = $productRows;
      } else {
        $this->template->productRows = array();
      }

      //zjistim nejblizsi slevu
      $nextDisc = dibi::fetch("SELECT dispercent, disfrom - ".$this->basketNamespace->priceSumVatDisc." AS diff FROM discounts WHERE distypid='volume' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, " AND disstatus=0 AND disfrom > ".$this->basketNamespace->priceSumVatDisc);
      $nextDelsFree = Null;
      $nextDelFreeMas = Null;

      $rows = false;
      if ($this->basketNamespace->delFree == false) {
        //nejblizsi doprava zdarma
        $rows = dibi::query("
        SELECT *, disfrom - ".$this->basketNamespace->priceSumVat." AS diff
        FROM deliverymodes
        INNER JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
        WHERE delmasid=0 AND disfrom > ".$this->basketNamespace->priceSumVat." ORDER BY disfrom")->fetchAssoc("disfrom,delid");
      }

      if ($rows) $nextDelsFree = $rows;

      $this->template->nextDisc = $nextDisc;
      $this->template->nextDelsFree = $nextDelsFree;
      //naplnim access
      $this->template->enum_proaccess = $product->getEnumProAccess();
    }
  }

  public function renderOrderDelMode() {
    if (count($this->basketNamespace->items) == 0) {
      $this->flashMessage("Košík je prázdný. Vyberte nejprve nějaké zboží.");
      $this->redirect('Basket:default');
    }

    $dels = new \Model\DeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    $dels->setPrcCat($this->userData->usrprccat);

    $basketPrice = $this->basketNamespace->priceSumVat;

    $this->template->delModes = $dels->getDelModes($basketPrice, $this->basketNamespace->delFree);
    $this->template->payModes = $dels->getPayModes(0, $basketPrice, $this->basketNamespace->payFree, "" ,"delmasid,delid");
    $this->template->payModesJs = $dels->getPayModes(0, $basketPrice, $this->basketNamespace->payFree);
    $this->template->priceSumVat = $basketPrice;

    $this->template->selectedDeliveryPrice = 0;
    if (!empty($this->basketNamespace->contact["orddelid"])) {
      $currentPaymentMode = $this->template->payModesJs[$this->basketNamespace->contact["orddelid"]];
      $currentDeliveryMode = $this->template->delModes[$currentPaymentMode->delmasid];
      $this->template->selectedDeliveryPrice = $currentDeliveryMode->delprice + $currentPaymentMode->delprice;
    }

    if (is_array($this->basketNamespace->contact)) {
      if (isset($this->basketNamespace->contact["orddelid"])) {
        $payId = (int)$this->basketNamespace->contact["orddelid"];
        $this->template->payid = $payId;
        if (isset($this->template->payModesJs[$payId])) {
          $payMode = $this->template->payModesJs[$payId];
          $this->template->delid = $payMode->delmasid;
        }
      }
      $form = $this['orderDelModeForm'];
      $form->setDefaults($this->basketNamespace->contact);
    }
  }

  public function renderMakeOrder() {
    if (count($this->basketNamespace->items) == 0) {
      $this->flashMessage("Košík je prázdný. Vyberte nejprve nějaké zboží.");
      $this->redirect('Basket:default');
    }
    $this->template->productRows = $this->basketNamespace->products;
    $this->template->basket = $this->basketNamespace;
    $basketPrice = $this->basketNamespace->priceSumTotalVat;
    $this->template->delModes = dibi::query("
      SELECT delid, delname, delcode, ".($this->basketNamespace->delFree ? "0" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice".$this->curId.$this->userData->usrprccat.")")." AS delprice, delurlparcel, deldesc, deltext".$this->curId." AS deltext
      FROM deliverymodes
      LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
      WHERE delstatus=0 AND delmasid=0 ORDER BY delorder
    ")->fetchAssoc('delid');
    $delId = 0;
    //vezmu prvni dopravu ze seznamu
    if ($delId == 0) {
      $first = current($this->template->delModes);
      $delId = $first->delid;
    }
    $this->delid = $delId;

    $this->template->payModes = dibi::fetchAll("
      SELECT delid, delmasid, delname, delcode, ".($this->basketNamespace->delFree ? "0" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice".$this->curId.$this->userData->usrprccat.")")." AS delprice, delurlparcel, deldesc, deltext".$this->curId." AS deltext
      FROM deliverymodes
      LEFT JOIN discounts ON (disdelid=delmasid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
      WHERE delcode!='payonline' AND delstatus=0 AND delmasid=%i", $this->delid, " ORDER BY delmasid, delorder
    ");
    $payMO = dibi::fetch("
      SELECT delid, delmasid, delname, delcode, ".($this->basketNamespace->delFree ? "0" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice".$this->curId.$this->userData->usrprccat.")")." AS delprice, delurlparcel, deldesc, deltext".$this->curId." AS deltext
      FROM deliverymodes
      LEFT JOIN discounts ON (disdelid=delmasid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
      WHERE delcode='payonline' AND delstatus=0 AND delmasid=%i", $this->delid, " ORDER BY delmasid, delorder
    ");
    $payModesOnline = Array();
    $payTypeOnline = $this->neonParameters["onlinePayTypes"];
    if (count($payTypeOnline) > 0 && $payMO) {
      foreach ($payTypeOnline as $ikey => $iname) {
        $row = clone $payMO;
        $row->delid = $ikey.'_'.$payMO->delid;
        $row->delname = $iname;
        $payModesOnline[] = $row;
      }
    }
    $this->template->payModesOnline = $payModesOnline;

    $this->template->payModesJs = dibi::query("
      SELECT delid, delmasid, delname, ".($this->basketNamespace->delFree ? "0" : "IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < $basketPrice, 0, delprice".$this->curId.$this->userData->usrprccat.")")." AS delprice, delurlparcel, deldesc
      FROM deliverymodes
      LEFT JOIN discounts ON (disdelid=delmasid AND distypid='delfree' AND disprccat=%s", $this->userData->usrprccat, " AND discurid=%i", $this->curId, ")
      WHERE delstatus=0 AND delmasid=%i", $this->delid, " ORDER BY delmasid, delorder")->fetchAssoc('delid');
    $this->template->priceSumTotalVat = $basketPrice;

    $LoggedUser = $this->userData;
      if ($LoggedUser->usrid > 0) {
        $defVals = array(
          'ordiname' => $LoggedUser->usriname,
          'ordilname' => $LoggedUser->usrilname,
          'ordifirname' => $LoggedUser->usrifirname,
          'ordistreet' => $LoggedUser->usristreet,
          'ordistreetno' => $LoggedUser->usristreetno,
          'ordicity' => $LoggedUser->usricity,
          'ordipostcode' => $LoggedUser->usripostcode,
          'ordicouid' => $LoggedUser->usricouid,
          'ordic' => $LoggedUser->usric,
          'orddic' => $LoggedUser->usrdic,
          'ordstname' => $LoggedUser->usrstname,
          'ordstlname' => $LoggedUser->usrstlname,
          'ordstfirname' => $LoggedUser->usrstfirname,
          'ordststreet' => $LoggedUser->usrststreet,
          'ordststreetno' => $LoggedUser->usrststreetno,
          'ordstcity' => $LoggedUser->usrstcity,
          'ordstpostcode' => $LoggedUser->usrstpostcode,
          'ordstcouid' => $LoggedUser->usrstcouid,
          'ordtel' => $LoggedUser->usrtel,
          'ordmail' => $LoggedUser->usrmail,
          'shipto' => ($LoggedUser->usrstname != ""),
        );
        $form = $this->getComponent("makeOrderForm");
        $form->setDefaults($defVals);
      }

  }

  public function renderOrderContact() {
    if (count($this->basketNamespace->items) == 0) {
      $this->flashMessage("Košík je prázdný. Vyberte nejprve nějaké zboží.");
      $this->redirect('Basket:default');
    }
    if (empty($this->basketNamespace->contact["orddelid"])) {
      $this->flashMessage("Vyberte nejprve dopravu a platbu.");
      $this->redirect('Basket:orderDelMode');
    }
    if (is_array($this->basketNamespace->contact)) {
      //nactu kontaktni údaje
      if (empty($this->basketNamespace->contact["ordiname"])) {
        $LoggedUser = $this->userData;
        if ($LoggedUser->usrid > 0) {
          $defVals = array(
            'ordiname' => $LoggedUser->usriname,
            'ordilname' => $LoggedUser->usrilname,
            'ordifirname' => $LoggedUser->usrifirname,
            'ordistreet' => $LoggedUser->usristreet,
            'ordistreetno' => $LoggedUser->usristreetno,
            'ordicity' => $LoggedUser->usricity,
            'ordipostcode' => $LoggedUser->usripostcode,
            'ordicouid' => $LoggedUser->usricouid,
            'ordic' => $LoggedUser->usric,
            'orddic' => $LoggedUser->usrdic,
            'ordstname' => $LoggedUser->usrstname,
            'ordstlname' => $LoggedUser->usrstlname,
            'ordstfirname' => $LoggedUser->usrstfirname,
            'ordststreet' => $LoggedUser->usrststreet,
            'ordststreetno' => $LoggedUser->usrststreetno,
            'ordstcity' => $LoggedUser->usrstcity,
            'ordstpostcode' => $LoggedUser->usrstpostcode,
            'ordstcouid' => $LoggedUser->usrstcouid,
            'ordtel' => $LoggedUser->usrtel,
            'ordmail' => $LoggedUser->usrmail,
            'shipto' => ($LoggedUser->usrstname != ""),
          );
          $form = $this->getComponent("orderContactForm");
          $form->setDefaults($defVals);
        }
      } else {
        $form = $this['orderContactForm'];
        $form->setDefaults($this->basketNamespace->contact);
      }
    }
  }


  public function renderOrderSumarize() {
    $dels = new \Model\DeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    $dels->setPrcCat($this->userData->usrprccat);

    if (count($this->basketNamespace->items) == 0) {
      $this->flashMessage("Košík je prázdný. Vyberte nejprve nějaké zboží.");
      $this->redirect('Basket:default');
    }
    if (empty($this->basketNamespace->contact["orddelid"])) {
      $this->flashMessage("Vyberte nejprve dopravu a platbu.");
      $this->redirect('Basket:orderDelMode');
    }
    if (empty($this->basketNamespace->contact["ordiname"])) {
      $this->flashMessage("Vyplňte nejprve Dodací údaje.");
      $this->redirect('Basket:orderContact');
    }

    //doplnim dopravu
    $orddelid = $this->basketNamespace->contact["orddelid"];
    $basketPrice = $this->basketNamespace->priceSumVat;

    //zjistim cenu platby
    $delprice = 0;
    $delname = "";

    $pay = $dels->getPayMode($orddelid, $basketPrice, $this->basketNamespace->payFree);
    if ($pay) {
      $delprice = $pay->delprice;

      //zjistim cenu dopravy
      $delivery = $dels->getDelMode($pay->delmasid, $basketPrice, $this->basketNamespace->delFree);
      if ($delivery) {
        $delprice += $delivery->delprice;
        $delname = $delivery->delname." - ".$pay->delname;
      }
    }
    $this->template->delprice = $delprice;
    $this->template->delname = $delname;
    $this->template->delivery = $delivery;


    $this->template->productRows = $this->basketNamespace->products;
    $this->template->basket = $this->basketNamespace;
    $basketPrice = $this->basketNamespace->priceSumTotalVat;
    $this->template->formData = $this->basketNamespace->contact;
  }


  public function basketFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $formVars = $form->getValues();
      $datachanged = false;
      foreach ($formVars as $key => $value) {
        if (substr($key, 0, 6) == 'count_')  {
          $proid = substr($key, 6);
          if ($value > 0) {
            $datachanged = true;
            $this->basketEditItem($proid, $value);
          } else {
            $datachanged = true;
            $this->basketDeleteItem($proid);
          }
        }
      }
      //$this->basketNamespace->delid = $formVars["delid"];
      if($form["recalc"]->isSubmittedBy()) {
        if (!empty($formVars["coupon"])) {
          //nactu kupon slevovy
          $cous = new \Model\CouponsModel();
          $cou = $cous->validateCoupon($formVars["coupon"]);
          if ($cou["status"] != 'ok') {
            $this->flashMessage($cou["text"], 'err');
          } else {
            $this->basketNamespace->coupon = $cou["data"];
          }
        }
      }
      $this->recalc();
      if($form["recalc"]->isSubmittedBy()) {
        $this->redirect("this");
      } else if($form["makeorder"]->isSubmittedBy()) {
        if ($this->basketOrderOnOnePage) {
          $this->redirect("Basket:makeOrder");
        } else {
          $this->redirect("Basket:orderDelMode");
        }
      }
    }
  }

  protected function createComponentBasketForm() {
    $form = $this->createAppForm();
    $product = new \Model\ProductsModel();
    $product->setPrcCat($this->userData->usrprccat);
    $product->setCurrency($this->currencies, $this->curId);
    foreach ($this->basketNamespace->items as $key => $count) {
      $row = $product->load($key);
      if ($row->proid > 0  && $row->prostatus == 0) {
        $form->addText("count_".$row->proid, "", 3)
          ->setRequired(TRUE)
          ->addRule(Nette\Forms\Form::INTEGER, "Počet kusů musí být celé číslo")
          ->setDefaultValue($count);
      }
    }

    //slevovy kupon
    if (empty($this->basketNamespace->coupon)) {
      $form->addText("coupon", "Slevový kupón", 10);
      $form->addSubmit('couponAdd', 'Přidat slevu')->getControlPrototype()->class('button');
    }

    $form->addSubmit('recalc', 'Přepočítat')->getControlPrototype()->class('btn');
    $form->addSubmit('makeorder', 'Vybrat dopravu a platbu');
    $form->onSuccess[] = array($this, 'basketFormSubmitted');
    return $form;
  }

  protected function createComponentOrderDelModeForm() {
    $enums = new \Model\EnumcatsModel();
    $enumCountries = $enums->getEnumCountries();
    $dels = new \Model\DeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    $form = $this->createAppForm();

    //cena objednaneho zbozi v kosiku
    $basketPrice = $this->basketNamespace->priceSumVat;
    //bezne platby
    $rows = $dels->getPayModes(0, $basketPrice, $this->basketNamespace->payFree);
    $payModes = array();
    foreach ($rows as $row) {
      $payModes[$row->delid] = $row->delname;
    }

    $form->addRadioList('orddelid', '', $payModes)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte způsob dodání a platby.');

    $items = $dels->getEnumUlozenkaPlaces();
    $form->addSelect('orddelspec_ulozenka', '', $items)
      ->setPrompt("Vyberte prosím výdejní místo ...");

    $items = $dels->getEnumWedoPlaces();
    $form->addSelect('orddelspec_wedo', '', $items)
      ->setPrompt("Vyberte prosím výdejní místo ...");

    $form->addSubmit('submit', 'Zadat dodací údaje');
    $form->onSuccess[] = array($this, 'orderDelModeFormSubmitted');
    return $form;
  }

  public function orderDelModeFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $formVars = $form->getValues();
      //unset
      unset($formVars["shipto"]);
      unset($formVars["delmasid"]);

      $payMode = dibi::fetch("SELECT delid, delcode, delname, delmasid FROM deliverymodes WHERE delid=%i", $formVars["orddelid"]);
      if ($payMode) {
        $delMode = dibi::fetch("SELECT delid, delcode, delname, delmasid FROM deliverymodes WHERE delid=%i", $payMode->delmasid);
      }

      if ($delMode->delcode === 'ULOZENKA' && empty($formVars["orddelspec_ulozenka"])) {
        $form->addError("Prosim vyberte výdejní místo");
      } else if ($delMode->delcode !== 'ULOZENKA' && !empty($formVars["orddelspec_ulozenka"])) {
        unset($formVars["orddelspec_ulozenka"]);
      }

      if ($delMode->delcode === 'WEDO' && empty($formVars["orddelspec_wedo"])) {
        $form->addError("Prosim vyberte výdejní místo");
      } else if ($delMode->delcode !== 'WEDO' && !empty($formVars["orddelspec_wedo"])) {
        unset($formVars["orddelspec_wedo"]);
      }

      $this->basketNamespace->contact = array_replace((array)$this->basketNamespace->contact, (array)$formVars);

      if ($form->hasErrors()) {
        return;
      }

      $this->redirect("Basket:orderContact");
    }
  }

  protected function createComponentOrderContactForm() {
    $enums = new \Model\EnumcatsModel();
    $enumCountries = $enums->getEnumCountries();
    $dels = new \Model\DeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    $form = $this->createAppForm();

    //cena objednaneho zbozi v kosiku
    $basketPrice = $this->basketNamespace->priceSumTotalVat;

    $form->addGroup('Fakturační a dodací adresa');

    $form->addText("ordiname", "Jméno", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno.');

    $form->addText("ordilname", "Přijmení", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte přijmení.');

    $form->addText("ordifirname", "Název firmy", 60);

    $form->addText("ordistreet", "Ulice", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ulici.');

    $form->addText("ordistreetno", "Číslo popisné", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte číslo popisné.');

    $form->addText("ordicity", "Město, obec", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte město, obec.');

    $form->addText("ordipostcode", "PSČ", 6)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte PSČ.')
      ->addRule(Nette\Forms\Form::LENGTH, 'Prosím vyplňte přesně %d číslic', 5);

    $form->addText("ordtel", "Mobilní telefon", 10)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte telefon.');

    $form->addText("ordmail", "Email", 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addText("ordic", "IČ", 10);
    $form->addText("orddic", "DIČ", 10)
      ->setHtmlId("orddic");

    $form->addCheckbox("ordusrvat", "Jsem plátce DPH")
      ->setHtmlId("ordusrvat");

    $form->addGroup('Fakturační adresa');

    $form->addCheckbox('shipto', 'Chci zadat jinou adresu dodání')
      ->setDefaultValue(FALSE)
      ->addCondition(Nette\Forms\Form::EQUAL, TRUE);

    $form->addCheckbox('onfirm', 'Chci objednat na firmu')
      ->setDefaultValue(FALSE)
      ->addCondition(Nette\Forms\Form::EQUAL, TRUE);

    $form->addText("ordstname", "Jméno", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno.');

    $form->addText("ordstlname", "Příjmení", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte přijmení.');

    $form->addText("ordstfirname", "Název firmy", 60);

    $form->addText("ordststreet", "Ulice", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ulici.');

    $form->addText("ordststreetno", "Číslo popisné", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte číslo popisné.');

    $form->addText("ordstcity", "Město, obec", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte město, obec.');

    $form->addText("ordstpostcode", "PSČ", 6)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte PSČ.')
        ->addRule(Nette\Forms\Form::LENGTH, 'Prosím vyplňte přesně %d číslic', 5);

    if ($this->userData->usrid > 0) {
      if ($this->userData->usrmaillist == 0) {
        $form->addCheckbox('maillist', 'Chci dostávat novinky emailem')
          ->setDefaultValue(TRUE);
      }
    } else {
      $form->addCheckbox('maillist', 'Chci dostávat novinky emailem')
          ->setDefaultValue(TRUE);

      $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"],  10)
        ->setOption('description', 'test proti robotum')
        ->setRequired(TRUE)
        ->setHtmlId('antispam')
        ->addRule(Nette\Forms\Form::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"]);
    }

    $form->addTextArea("ordnote", "Vzkaz k objednávce", 60, 3);

    $form->addSubmit('submit', 'Souhrn a objednávka')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'orderContactFormSubmitted');
    return $form;
  }

  public function orderContactFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $formVars = $form->getValues();
      //unset
      unset($formVars["antispam"]);
      unset($formVars["shipto"]);
      unset($formVars["delmasid"]);

      $this->basketNamespace->contact = array_replace((array)$this->basketNamespace->contact, (array)$formVars);
      $this->redirect("Basket:orderSumarize");
    }
  }

  protected function createComponentMakeOrderForm() {
    $enums = new \Model\EnumcatsModel();
    $enumCountries = $enums->getEnumCountries();
    $dels = new \Model\DeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    $form = $this->createAppForm();

    //cena objednaneho zbozi v kosiku
    $basketPrice = $this->basketNamespace->priceSumTotalVat;

    $form->addGroup('Doprava a platba');
    $payModes = dibi::query("
      SELECT delid, delname
      FROM deliverymodes
      WHERE delstatus=0 AND delmasid>0
    ")->fetchPairs("delid", "delname");
    $form->addRadioList('orddelid', '', $payModes)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Způsob dodání a platby.');


    $items = $dels->getEnumUlozenkaPlaces();
    $form->addSelect('orddelspec', '', $items)
      ->setPrompt("Vyberte prosím výdejní místo ...");

    //$form->getElementPrototype()->id = 'makeOrderForm';
    $form->addGroup('Fakturační a dodací adresa');

    $form->addText("ordiname", "Jméno", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno.');

   $form->addText("ordilname", "Přijmení", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte přijmení.');

    $form->addText("ordifirname", "Název firmy", 60);

    $form->addText("ordistreet", "Ulice", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ulici.');

    $form->addText("ordistreetno", "Číslo popisné", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte číslo popisné.');

    $form->addText("ordicity", "Město, obec", 60)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte město, obec.');

    $form->addText("ordipostcode", "PSČ", 6)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte PSČ.');

    $form->addText("ordtel", "Mobilní telefon", 10);

    $form->addText("ordmail", "Email", 20)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte Váš email.')
      ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');

    $form->addText("ordic", "IČ", 10);
    $form->addText("orddic", "DIČ", 10)
      ->setHtmlId("orddic");

    $form->addCheckbox("ordusrvat", "Jsem plátce DPH")
      ->setHtmlId("ordusrvat");

    $form->addGroup('Fakturační adresa');

    $form->addCheckbox('shipto', 'Chci zadat jinou adresu dodání')
      ->setDefaultValue(FALSE)
      ->addCondition(Nette\Forms\Form::EQUAL, TRUE);

    $form->addText("ordstname", "Jméno", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno.');

    $form->addText("ordstlname", "Příjmení", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte přijmení.');

    $form->addText("ordstfirname", "Název firmy", 60);

    $form->addText("ordststreet", "Ulice", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte ulici.');

    $form->addText("ordststreetno", "Číslo popisné", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte číslo popisné.');

    $form->addText("ordstcity", "Město, obec", 60)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte město, obec.');

    $form->addText("ordstpostcode", "PSČ", 6)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte PSČ.');
    /*
    $form->addSelect("ordstcouid", "Země", $enumCountries)
      ->addConditionOn($form["shipto"], Nette\Forms\Form::FILLED, True)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte zemi.');
    */

    if ($this->userData->usrid > 0) {
      if ($this->userData->usrmaillist == 0) {
        $form->addCheckbox('maillist', 'Chci dostávat novinky emailem')
          ->setDefaultValue(TRUE);
      }
    } else {
      $form->addCheckbox('maillist', 'Chci dostávat novinky emailem')
          ->setDefaultValue(TRUE);

      $form->addText('antispam', 'Vyplňte číslo '.$this->config["ANTISPAM_NO"],  10)
        ->setOption('description', 'test proti robotum')
        ->setHtmlId('antispam')
        ->addRule(Nette\Forms\Form::EQUAL, 'Vyplňte prosím číslo %d, jde o test proti robotum', $this->config["ANTISPAM_NO"]);
    }

    $form->addTextArea("ordnote", "Vzkaz k objednávce", 60, 3);

    $form->addSubmit('submit', 'Odeslat objednávku')->getControlPrototype()->class('btn');
    $form->onSuccess[] = array($this, 'makeOrderFormSubmitted');
    //$form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    return $form;
  }

  protected function createComponentOrderSumarizeForm() {
    $form = $this->createAppForm();
    $form->addCheckbox("agreement", "")
      ->addCondition(Nette\Forms\Form::EQUAL,  FALSE)
        ->setRequired('Je třeba souhlasit s obchodními podmínkami');

    $form->addCheckbox("ordheurekagdpr", "Souhlasím se zasláním dotazníku spokojenosti v rámci programu Ověřeno zákazníky, který pomáhá zlepšovat vaše služby.");

    $form->addSubmit('submit', 'Závazně objednat');
    $form->onSuccess[] = array($this, 'makeOrderFormSubmitted');
    return $form;
  }


  public function makeOrderFormSubmitted(Nette\Application\UI\Form $form) {
    $dels = new \Model\DeliveryModesModel();
    $dels->setCurrency($this->currencies, $this->curId);
    if ($form->isSubmitted()) {
      if (count($this->basketNamespace->items) == 0) $this->redirect('Basket:default');

      $LoggedUser = $this->userData;
      $formVars = $form->getValues();
      $formVars = array_merge((array)$formVars, (array)$this->basketNamespace->contact);
      unset($formVars["antispam"]);
      unset($formVars["shipto"]);
      unset($formVars["onfirm"]);
      unset($formVars["delmasid"]);
      unset($formVars["agreement"]);
      $maillist = false;
      if(isset($formVars["maillist"])) {
        $maillist = $formVars["maillist"];
        unset($formVars["maillist"]);
      }

      $formVars["ordusrid"] = $LoggedUser->usrid;
      $formVars["ordweight"] = $this->basketNamespace->weightSum;
      $formVars["ordcurid"] = $this->curId;

      if (!empty($this->basketNamespace->coupon["coucode"])) {
        $formVars["orddisccoupon"] = (int)$this->basketNamespace->coupon["couvalue"];
        $formVars["ordcoucode"] = $this->basketNamespace->coupon["coucode"];
      }

      //zjistim jestli neni specialni doprava
      if (!is_numeric($formVars["orddelid"])) {
        //vyseparuju kod platby
        $arr = explode('_', $formVars["orddelid"]);
        $formVars["orddelid"] = (int)$arr[1];
        $formVars["ordpaycode"] = $arr[0];
      }
      $payMode = $dels->load($formVars["orddelid"]);
      if ($payMode) {
        $delMode = $dels->load($payMode->delmasid);
      }

      if (!$payMode || !$delMode) {
        $form->addError("Špatné zadání dopravy a platby");
      }

      if ($delMode->delcode == 'ULOZENKA') {
        if (empty($formVars["orddelspec_ulozenka"])) {
          $form->addError("Prosim vyberte výdejní místo");
        } else {
          $formVars["orddelspec"] = $formVars["orddelspec_ulozenka"];
        }
      }

      if ($delMode->delcode == 'WEDO') {
        if (empty($formVars["orddelspec_wedo"])) {
          $form->addError("Prosim vyberte výdejní místo");
        } else {
          $formVars["orddelspec"] = $formVars["orddelspec_wedo"];
        }
      }

      unset($formVars["orddelspec_ulozenka"], $formVars["orddelspec_wedo"]);

      if ($form->hasErrors()) {
        return;
      }
      $orders = new \Model\OrdersModel();
      $orders->setCurrency($this->currencies, $this->curId);
      //vyberu firmu na kterou se objednavka vystavi
      $formVars["ordfirid"] = 2;
      //pokud je platce dam firmu pro platce
      if (!$formVars["ordusrvat"]) $formVars["ordfirid"] = 1;

      //ulozim hlavicku objednavky
      $ordid = $orders->insert($formVars);

      //do profilu nakopiruju posledni kontaktni udaje
      $usrs = new \Model\UsersModel();
      if ($this->userData->usrid > 0) {
        $usr = $usrs->load($this->userData->usrid);
        $usrUpd = array();
        if (empty($usr->usriname)) {
          $usrUpd["usriname"] = $formVars["ordiname"];
          $usrUpd["usrilname"] = $formVars["ordilname"];
          $usrUpd["usrifirname"] = $formVars["ordifirname"];
          $usrUpd["usristreet"] = $formVars["ordistreet"];
          $usrUpd["usristreetno"] = $formVars["ordistreetno"];
          $usrUpd["usricity"] = $formVars["ordicity"];
          $usrUpd["usripostcode"] = $formVars["ordipostcode"];
          $usrUpd["usrtel"] = $formVars["ordtel"];
          $usrUpd["usric"] = $formVars["ordic"];
          $usrUpd["usrdic"] = $formVars["orddic"];
          if (empty($usr->usrstname) && !empty($formVars["ordstname"])) {
            $usrUpd["usrstname"] = $formVars["ordstname"];
            $usrUpd["usrstlname"] = $formVars["ordstlname"];
            $usrUpd["usrstfirname"] = $formVars["ordstfirname"];
            $usrUpd["usrststreet"] = $formVars["ordststreet"];
            $usrUpd["usrststreetno"] = $formVars["ordststreetno"];
            $usrUpd["usrstcity"] = $formVars["ordstcity"];
            $usrUpd["usrstpostcode"] = $formVars["ordstpostcode"];
          }
        }

        if ($maillist)  $usrUpd["usrmaillist"] = 1;

        if (count($usrUpd) > 0) $usrs->update($this->userData->usrid, $usrUpd);
      } else {
        if ($maillist) {
          $usrs = new \Model\UsersModel();
          //zjistim jestli neni profil s timto mailem
          $usrid = dibi::fetchSingle("SELECT usrid FROM users WHERE usrmail=%s", trim($formVars["ordmail"]));
          if ($usrid > 0) {
            $usrs->update($usrid, array('usrmaillist'=>1));
          } else {
            $usrid = $usrs->insert(array('usrmail'=>trim($formVars["ordmail"]), 'usrmaillist'=>1));
          }
          $orders->update($ordid, array('ordusrid'=>$usrid));
        }
      }

      if ($ordid > 0) {
        $ordItems = new \Model\OrdItemsModel();

        //vlozim polozky
        $product = new \Model\ProductsModel();
        $product->setPrcCat($LoggedUser->usrprccat);
        $product->setCurrency($this->currencies, $this->curId);
        foreach ($this->basketNamespace->items as $id => $cnt) {
          $ordItemsVals = array();
          $row = $product->load($id);
          if ($row->proid == $id && $row->prostatus == 0) {
            //polozka nalezena v db a je aktivni - vlozim mezi polozky objednavky
            $proprice = $this->getCouponProductPrice($row);

            $ordItemsVals['oriordid'] = $ordid;
            $ordItemsVals['oriproid'] = $id;
            $ordItemsVals['oriprocode'] = $row->procode;
            $ordItemsVals['oriprocode2'] = $row->procode2;
            $ordItemsVals['oritypid'] = 0;
            $ordItemsVals['oriname'] = $row->proname;
            $ordItemsVals['oriprice'] = (double)$proprice;
            $ordItemsVals['oripriceoriginal'] = $row->proprice;
            $ordItemsVals['orivatid'] = $row->provatid;
            $ordItemsVals['orivat'] = (int)(isset($this->config["VATTYPE_".$row->provatid]) ? $this->config["VATTYPE_".$row->provatid] : 0);
            $ordItemsVals['oricredit'] = $row->procredit;
            $ordItemsVals['oriqty'] = $cnt;
            $ordItemsVals['oriprobigsize'] = $row->probigsize;
            $ordItemsVals['oriprooffer'] = $row->prooffer;

            //zjistim jestli neni vyplneno proqty
            $vals = array();
            if ($row->proqty > 0) {
              //odečtu stav skladu
              $vals["proqty"] = MAX(($row->proqty - $cnt), 0);
              if ($vals["proqty"] == 0) $vals["proaccess"] = 100;
              $product->update($id, $vals);
            }
            $ordItems->insert($ordItemsVals);
          }
          $ordItemsVals['oriprocode'] = Null;
          $ordItemsVals['oriprocode2'] = Null;
        }
        //vlozim postovne

        $payMode = $dels->load($formVars["orddelid"]);
        $delMode = $dels->load($payMode->delmasid);
        $delPrice = (double)$delMode->delprice + (double)$payMode->delprice;
        $ordItemsVals = array();
        $ordItemsVals['oriordid'] = $ordid;
        $ordItemsVals['oritypid'] = 1;
        $ordItemsVals['oriproid'] = 0;
        $ordItemsVals['oriname'] = "Doprava: ".$delMode->delname." - ".$payMode->delname;
        $ordItemsVals['oriprice'] = ($this->basketNamespace->specDel ? 0 : (double)$delPrice);
        $ordItemsVals['orivatid'] = 0;
        $ordItemsVals['orivat'] = $this->config["VATTYPE_0"];
        $ordItemsVals['oricredit'] = 0;
        $ordItemsVals['oriqty'] = 1;
        $ordItemsVals['oriprobigsize'] = 0;
        $ordItemsVals['oriprooffer'] = 0;
        $ordItems->insert($ordItemsVals);

        $orders->recalcOrder($ordid);
        $this->basketNamespace->ordid = $ordid;

        $order = $orders->load($ordid);

        //odmailuju
        $mailTemplate = $this->createTemplate();
        $mailTemplate->orderRow = $order;
        $delModes = new \Model\DeliveryModesModel();
        $delModes->setCurrency($this->currencies, $order->ordcurid);

        $enums = new \Model\EnumcatsModel();
        $mailTemplate->basket = $this->basketNamespace;
        $mailTemplate->payMode = $delModes->load($order->orddelid);
        $mailTemplate->delMode = $delModes->load($mailTemplate->payMode->delmasid);
        $mailTemplate->enum_ulozenka = $delModes->getEnumUlozenkaPlaces();
        $mailTemplate->ordItemRows = dibi::fetchAll("SELECT * FROM orditems WHERE oriordid=%i", $ordid, " ORDER BY CASE WHEN oritypid=0 THEN 0 WHEN oritypid=3 THEN 1 WHEN oritypid=1 THEN 3 ELSE 10 END");
        $mailTemplate->ordSpecDel = ((int)dibi::fetchSingle("SELECT COUNT(*) FROM orditems WHERE oriordid=%i", $ordid, " AND oritypid=0 AND (oriprobigsize=1 OR oriprooffer=1)") > 0);
        $mailTemplate->key = substr(md5($ordid.$mailTemplate->orderRow->orddatec), 0, 4);
        $mailTemplate->setTranslator($this->translator);
        $mailTemplate->lang = $this->lang;
        $mailTemplate->user = $this->userData;
        $mailTemplate->enum_usrprccat = $this->getEnumPrcCat();

        $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailOrderCreated.latte');
        try {
          //mailuju zakaznikovi
          $mails = explode(',', $this->config["SERVER_MAILORDERS"]);
          $mailOrders = $mails[0];
          $this->mailSend($order->ordmail, "Objednávka č."." ".$order->ordcode, $mailTemplate, $mailOrders);
          //mailuju obchodnikovi
          $pohodaXphBody = $this->exportPohodaXph($order->ordid);
          $fileName = TEMP_DIR."/".$order->ordcode.".xph";
          file_put_contents($fileName, $pohodaXphBody);
          foreach ($mails as $mail) {
            $this->mailSend($mail, "Nová objednávka č. ".$order->ordcode, $mailTemplate, '', array($fileName));
          }
        } catch (Nette\InvalidStateException $e) {
          $this->flashMessage("Nepodařilo se ale odeslat informační email o nové objednávce", "danger");
        }

        //zjistím způsob platby, pokud je platba předem nastavím staus čeká na platbu
        $payCode = (string)dibi::fetchSingle("SELECT delcode FROM deliverymodes WHERE delid=%i", $order->orddelid);
        if ($payCode === 'paybefore') {
          $orders->update($order->ordid, array('ordstatus'=>2));
          $orders->logStatus($order->ordid, 2, NULL);
          $order->ordstatus = 2;
          //odmailuju změnu stavu
          $mailTemplate = $this->createTemplate();
          $orderRow = $order;
          $mailTemplate->orderRow = $orderRow;
          $delModes = new \Model\DeliveryModesModel();
          $delModes->setCurrency($this->currencies, $orderRow->ordcurid);
          $mailTemplate->payMode = $delModes->load($orderRow->orddelid);
          $mailTemplate->delMode = $delModes->load($mailTemplate->payMode->delmasid);
          $mailTemplate->enum_ordStatus = $orders->getEnumOrdStatus();
          $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailOrderChanged.latte');
          try {
            $this->mailSend($orderRow->ordmail, "Změna stavu objednávky č. ".$orderRow->ordcode, $mailTemplate);
          } catch (Nette\InvalidStateException $e) {
            $this->flashMessage("Nepodařilo se ale odeslat informační email o nové objednávce", "danger");
          }
        }

        //vymazu kosik
        $specDel = $this->basketNamespace->specDel;
        $this->basketNamespace->remove();
        $this->basketNamespace = $this->getSession('basket');
        $this->basketNamespace->ordid = $ordid;

        $this->redirect("accepted");
      }
    }
  }
}
