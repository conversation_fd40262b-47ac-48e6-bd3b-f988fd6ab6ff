<?php
namespace FrontModule;
use dibi;
use Nette;
use Model;

final class BatchPresenter extends BasePresenter {

  public function actionNightBatch() {
    $k = (string)$this->getParameter('k');
    if ($k != '942a13a8cb5840') $this->terminate();

    //vyberu vsechny produkty co maji nastavenou novinku a datum vytvoreni je starsi nez X dni.
    $days = (int)$this->config["PRODUCT_NEWAGE"];
    if ($days > 0) {
      $rows = dibi::fetchAll("SELECT proid FROM products WHERE protypid2=1 AND DATE(prodatec + INTERVAL ".$days." DAY) < CURDATE()");
      $ids = array();
      foreach ($rows as $key => $row) {
        $ids[] = $row->proid;
      }
      if (count($ids) > 0) {
        $idsStr = implode(',', $ids);
        dibi::query("UPDATE products SET WHERE proid IN ($idsStr)");
      }
    }

    //kontrola akčních cen
    $rows = dibi::fetchAll("SELECT * FROM product_prices WHERE prastatus=0 AND CURDATE() BETWEEN pradatefrom And pradateto");
    $pras = new Model\ProductPricesModel();
    foreach ($rows as $row) {
      $pras->activate($row);
    }
    $rows = dibi::fetchAll("SELECT * FROM product_prices WHERE prastatus=1 AND CURDATE() NOT BETWEEN pradatefrom And pradateto");
    foreach ($rows as $row) {
      $pras->deactivate($row);
    }

    file_get_contents('http://www.google.com/webmasters/tools/ping?sitemap=https://www.'.$this->config["SERVER_NAMESHORT"].'/export/sitemap');
    file_get_contents('http://www.bing.com/webmaster/ping.aspx?siteMap=https://www.'.$this->config["SERVER_NAMESHORT"].'/export/sitemap');

    $this->terminate();
  }

  public function actionMailing() {
    $k = (string)$this->getParameter('k');
    if ($k != '942a13a8cb5840') $this->terminate();
    dibi::getConnection()->onEvent = NULL;

    $pros = new Model\ProductsModel();
    $mails = new Model\MailsModel();
    $mailings = new Model\MailingsModel();
    $usersTable = $mailings->usersTable;

    //zjistim jestli neni nachystane nejake mailovani
    $rows = dibi::fetchAll("SELECT * FROM mailings WHERE mamstatus=1 AND (mamdate<=CURDATE() OR mamdate IS NULL)");
    foreach ($rows as $mailing) {
      //naplnim produkty
      $prosArr = explode("\n", trim($mailing->mamproducts));

      $arrDesc1 = explode("\n", trim($mailing->mamdesc1));
      $arrDesc2 = explode("\n", trim($mailing->mamdesc2));
      $arrDesc3 = explode("\n", trim($mailing->mamdesc3));

      $cnt = 0;
      $products = array();
      foreach ($prosArr as $proid) {
        $pro = $pros->load($proid);
        if ($pro) {
          $pro->promamdesc1 = (isset($arrDesc1[$cnt]) ? $arrDesc1[$cnt] : "");
          $pro->promamdesc2 = (isset($arrDesc2[$cnt]) ? $arrDesc2[$cnt] : "");
          $pro->promamdesc3 = (isset($arrDesc3[$cnt]) ? $arrDesc3[$cnt] : "");
          $products[$cnt] = $pro;
          $cnt ++;
        }
      }
      //nastavim sablonu
      $mailTemplate = $this->createTemplate();
      $mailTemplate->products = $products;


      $mailTemplate->config = $this->config;
      $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/mailing.latte');
      //adresati
      //Poskládám cenové hladiny
      $priceLevels = array();
      if ($mailing->mampricea == 1) $priceLevels[] = 'a';
      if ($mailing->mampriceb == 1) $priceLevels[] = 'b';
      if ($mailing->mampricec == 1) $priceLevels[] = 'c';
      if ($mailing->mampriced == 1) $priceLevels[] = 'd';

      $users = dibi::fetchAll("SELECT usrid, usrmail, usrprccat, usrdatec FROM ".$usersTable." WHERE usrmaillist IN (%i)", ($mailing->mammaillist === 1 ? 1 : array(0,1)) , " AND usrprccat IN (%s)", $priceLevels, " AND usrstatus=0 ORDER BY usrid");
      $mailTemplate->mailing = $mailing;
      $mailsCnt = 0;
      foreach ($users as $user) {
        //kontrola formatu mailu
        if (!Nette\Utils\Validators::isEmail($user->usrmail)) {
          dibi::query("UPDATE ".$usersTable." SET usrmaillist=0 WHERE usrid=%i", $user->usrid, " LIMIT 1");
          continue;
        }

        $mailTemplate->usrid = $user->usrid;
        $mailTemplate->user = $user->usrid;
        $mailTemplate->loKey = md5($user->usrid.$user->usrdatec);
        $mailTemplate->usrmail = $user->usrmail;
        $mailTemplate->mamid = $mailing->mamid;
        $mailTemplate->isMail = TRUE;
        $date = (empty($mailing->mamdate) ? NULL : $mailing->mamdate);
        $templateHtml = (string)$mailTemplate;
        $data = array(
          'malmamid' => $mailing->mamid,
          'malusrid' => $user->usrid,
          'maldate' => $date,
          'malfrom' => $this->config["SERVER_MAILING_MAIL"],
          'malmail' => $user->usrmail,
          'malsubject' => $mailing->mamsubject,
          'malbody' => $templateHtml,
        );
        $mails->insert($data);
        $mailsCnt ++;
      }

      //nastavim ze se maily odesilaji
      $mailings->update($mailing->mamid, array(
        'mamstatus'=>2,
        'mamcntall'=>$mailsCnt,
        'mamdatestart'=> new \DateTime()
      ));
    }
    //spoustim mailovaci davku
    $rows = dibi::fetchAll("SELECT * FROM mails WHERE (maldate<=CURDATE() OR maldate IS NULL) AND malstatus=0 ORDER BY malid LIMIT ".(int)$this->config["SERVER_MAILING_CNT"]);
    $cnt = 0;
    $cntSleep = 0;
    $mamIsBlocked = array();
    foreach ($rows as $row) {

      //zjistim jestli prislusny mailing neni pozastaveny
      if (!isset($mamIsBlocked[$row->malmamid])) {
        $mamIsBlocked[$row->malmamid] = (bool)dibi::fetchSingle("SELECT mamstatus=4 FROM mailings WHERE mamid=%i", $row->malmamid);
      }
      if ($mamIsBlocked[$row->malmamid]) continue;

      if (!Nette\Utils\Validators::isEmail($row->malmail)) {
        $mails->delete($row->malid);
        continue;
      }

      try {
        $mail = new Nette\Mail\Message();
        //$mail->mailer->commandArgs = '-f'.$row->malfrom;
        if (!empty($row->malmamid)) $mail->setHeader('X-mamid', $row->malmamid);
        if (!empty($row->malusrid)) $mail->setHeader('X-usrid', $row->malusrid);
        if (!empty($row->malserid)) $mail->setHeader('X-serid', $row->malserid);
        $mail->setFrom($this->config["SERVER_NAMESHORT"].' <'.$row->malfrom.'>');
        $mail->setReturnPath($row->malfrom);
        $mail->addReplyTo($row->malfrom);
        $mail->addTo($row->malmail);
        $mail->setSubject($row->malsubject);
        $mail->setHtmlBody($row->malbody);

        $this->mailMail($mail);
        $mails->delete($row->malid);
      } catch (Exception $e) {
        \Tracy\Debugger::log($row->malid.":".$e->getMessage());
      }
      /*
      if ($cnt % 10 == 0) {
        sleep(30);
        $cntSleep++;
      }
      */
      $cnt ++;
      unset($mail);
    }
    foreach ($mamIsBlocked as $key => $value) {
      if ($value) \Tracy\Debugger::log("Maily pro mailing ID $key jsou pozastaveny.");
    }
    \Tracy\Debugger::log("Odmailováno $cnt mailů.");

    //zjistim jestli neni nejaka davka uz cela odmailovana
    $rows = dibi::fetchAll("SELECT mamid FROM mailings WHERE mamstatus=2");
    foreach ($rows as $row) {
      $cnt = (int)dibi::fetchSingle("SELECT COUNT(malmamid) FROM mails WHERE malmamid=%i", $row->mamid, " AND malstatus=0");
      if ($cnt == 0) {
        $mailings->update($row->mamid, array(
          'mamstatus'=>3,
          'mamleft'=>0,
          'mamdateend'=> new \DateTime()
        ));
      } else {
        $mailings->update($row->mamid, array('mamleft'=>$cnt));
      }
    }
    $this->terminate();
  }

  /**
 * vytvoří mailling pro  GDPR
 *
 * @return bool
 * @throws \DibiException
 * @throws \Dibi\Exception
 * @throws Nette\Application\AbortException
 */
  public function actionGdprMailling() {
    dibi::getConnection()->onEvent = NULL;
    $mails = new Model\MailsModel();
    $mailings = new Model\MailingsModel();
    $usrs = New Model\UsersModel();

    $mamid = $mailings->insert(array(
      'mamsubject' => 'Chceme s vámi zůstat v kontaktu',
      'mamdatec' => new Nette\Utils\DateTime(),

    ));
    $mailing = $mailings->load($mamid);
    if (!$mailing) {
      return false;
    }
    //navolit zda všem posílat nebo jen registrovaným: usrmaillist=1
    $rows = dibi::fetchAll("select * from users WHERE usrstatus=0");
    $mailsCnt = 0;
    foreach ($rows as $key => $row) {

      $usrs->update($row->usrid, array(
        'usrmailvcode' => $this->getVerifyCode(),
        'usrmailverified' => 0,
      ));

      $row = $usrs->load($row->usrid);
      $mailTemplate = $this->createTemplate();
      $mailTemplate->user = $row;
      $mailTemplate->mailing = $mailing;
      $mailTemplate->key = substr(md5($row->usrid.$row->usrdatec.$row->usrmailvcode), 0, 8);
      $mailTemplate->config = $this->config;
      $mailTemplate->setFile(WWW_DIR.'/../templates/Mails/gdprMailing.latte');
      $templateHtml = (string)$mailTemplate;
      $data = array(
        'malmamid' => $mailing->mamid,
        'malusrid' => $row->usrid,
        'maldate' => New Nette\Utils\DateTime(),
        'malfrom' => $this->config["SERVER_MAILING_MAIL"],
        'malmail' => $row->usrmail,
        'malsubject' => $mailing->mamsubject,
        'malbody' => $templateHtml,
      );
      $mails->insert($data);
      $mailsCnt ++;
      $usrs->logEvent($row->usrid, Model\UsersModel::EVENT_MAIL_VERIFICATION_SEND);
    }
    //nastavim ze se maily odesilaji
    $mailings->update($mailing->mamid, array(
      'mamstatus'=>2,
      'mamcntall'=>$mailsCnt,
      'mamdatestart'=> new \DateTime()
    ));
    $this->terminate();
  }

  /**
   * ověření zák účtu GDPR
   *
   * @param $i
   * @param int $t
   * @param string $k
   * @throws \Dibi\Exception
   * @throws Nette\Application\AbortException
   */
  public function actionVerifyAccount($i, $t, $k) {
    /*
     * 1 - zůstat registrovaný, přihlásit maillingu
     * 2 - zůstat registrovaný
     * 3 - smazat
    */
    $t = (int)$t;

    $usrs = new Model\UsersModel();
    $usr = $usrs->load($i);
    $ok = FALSE;
    if ($usr) {
      $uKey = substr(md5($usr->usrid.$usr->usrdatec.$usr->usrmailvcode), 0, 8);
      if ($uKey === $k) {
        $ok = TRUE;
        if ($t === 1 || $t === 2) {
          //verifikuji mail a zaloguji souhlas s uchováním maillist
          $usrs->update($i, array(
            'usrmailvcode' => NULL,
            'usrmailverified' => 1,
            'usrmaillist' => $t === 1 ? 1 : 0,
          ));
          $usrs->logEvent($i, Model\UsersModel::EVENT_MAIL_VERIFIED);
          if ($t === 1) {
            $usrs->logEvent($i, Model\UsersModel::EVENT_MAILLIST_ADD);
            $usrs->logEvent($i, Model\UsersModel::EVENT_GDPR);
          } else if ($t === 2) {
            $usrs->logEvent($i, Model\UsersModel::EVENT_MAILLIST_REM);
            $usrs->logEvent($i, Model\UsersModel::EVENT_GDPR);
          }
          $this->flashMessage("Děkujeme za Vaši důvěru. Vítejte zpět!");

        } else if ($t === 3) {
          //vymazu
          $usrs->clearPersonalData($i);
          $this->flashMessage("Mrzí nás, že odcházíte, ale kdykoliv vás rádi přivítáme zpět!");
        }
      } else {
        $this->flashMessage("Nejspíš se snažíte reagovat na naši prosbu ohledně GDPR opakovaně.");
        $ok = TRUE;
      }
    }
    if ($ok === FALSE) {
      $this->flashMessage("Omlouváme se, ale něco se pokazilo a požadovanou akci se nepodařil provést.");
    }
    $this->redirect("Homepage:default");
  }

  public function actionUpdateUlozenkaBranches() {
    $uloapi = new \UlozenkaApi($this->neonParameters["ulozenka"]);
    $uloapi->updateBranches();
    echo "Aktualizace uloženky provedena.";
    $this->terminate();
  }

  public function actionUpdateWeDoBranches() {
    $wedoApi = new \WedoApi();
    $wedoApi->updateBranches();
    echo "Aktualizace WEDO provedena.";
    $this->terminate();
  }
}
