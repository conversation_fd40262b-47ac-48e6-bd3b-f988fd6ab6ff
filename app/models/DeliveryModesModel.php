<?php
namespace Model;
use <PERSON><PERSON>, dibi;

class DeliveryModesModel extends BaseModel {

  protected $tableName = "deliverymodes";
  protected $fieldPrefix = "del";

  public function getDataSource($sql="") {
    if ($sql == "") $sql = $this->getSql();
    return dibi::dataSource($sql);
  }

  public function getSql () {
    return "SELECT *,
      delprice".$this->curId."a AS delprice
      FROM $this->tableName";
  }

  public function getDelFreeLimit($usrPrcCat) {
    $cacheKey = 'delFreeLimit_'.$this->curId.$usrPrcCat;
    $delFreeLimit = $this->cacheGet($cacheKey);
    if ($delFreeLimit === FALSE) {
      //cache neni musim naplnit
      $delFreeLimit = dibi::fetchSingle("
      SELECT MIN(disfrom)
      FROM deliverymodes
      INNER JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $usrPrcCat, " AND discurid=%i", $this->curId, ")
      WHERE (delcode<>'OSOBNE' OR delcode IS NULL) AND delmasid=0 AND delstatus=0
      ORDER BY disfrom");
      $this->cacheSave($cacheKey, $delFreeLimit);
    }
    return $delFreeLimit;
  }

  /**
  * vrati dopravu podle delid
  */
  public function getDelMode($delid, $basketPrice, $delFree) {
    $dels = $this->getDelModes($basketPrice, $delFree, "delid=" . $delid);
    return current($dels);
  }

  /**
   * vrati platbu podle delid
   */
  public function getPayMode($delid, $basketPrice, $delFree) {
    $dels = $this->getPayModes(NULL, $basketPrice, $delFree, "delid=" . $delid);
    return current($dels);
  }

  /**
   * vrati seznam možných doprav
   */
  public function getDelModes($basketPrice, $delFree, $where="") {
    return dibi::query("
      SELECT delid, delname, delcode, delcouid, IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$basketPrice." OR (".(($delFree) ? "TRUE" : 'FALSE')." AND delnodelfree=0), 0, delprice".$this->curId.$this->prccat.") AS delprice,
      delurlparcel, deldesc, deltext".$this->curId." AS deltext
      FROM deliverymodes
      LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->prccat, " AND discurid=%i", $this->curId, ")
      WHERE delstatus=0 AND (delcurid=0 OR delcurid=%i", $this->curId, ") AND delmasid=0 AND (delpricelimit=0 OR (delpricelimit=1 AND ".$basketPrice."<=500)) ".(!empty($where) ? " AND $where " : "")." ORDER BY delorder
        ")->fetchAssoc('delid');
  }

  /**
   * vrati seznam možných plateb
   */
  public function getPayModes($delMasId, $basketPrice, $payFree, $where="", $fetchAssoc="delid") {
    return dibi::query("
      SELECT delid, delmasid, delname, delcode,
      IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$basketPrice." OR (".(($payFree) ? "TRUE" : 'FALSE')." AND delnodelfree=0), 0, delprice".$this->curId.$this->prccat.") AS delprice,
      delurlparcel, deldesc, deltext".$this->curId." AS deltext
      FROM deliverymodes
      LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $this->prccat, " AND discurid=%i", $this->curId, ")
      WHERE delstatus=0 AND (delcurid=0 OR delcurid=%i", $this->curId, ") ".
      (!empty($delMasId) ? " AND delmasid=".$delMasId : "").
      (!empty($where) ? " AND $where " : "")."
      ORDER BY delorder")
      ->fetchAssoc($fetchAssoc);
  }

  /**
   * nastavi cenovou kategorii pro preddefinovane SQL
   *
   */
  public function setPrcCat($val) {
    if ($val == "") $val = "a";
    $this->prccat = $val;
  }

  /********************* ciselniky *********************/

  /**
  * ciselnik delstatus
  * @return array
  */
  public function getEnumDelStatus() {
    return array(
      0 => 'Aktivní',
      1 => 'Blokovaný',
    );
  }

  public function getEnumPayTypes() {
    return array(
      'paybefore' => 'Platba předem',
      'cetelem' => 'Na splátky Cetelem',
      'dobirka' => 'Dobírka',
      'cash' => 'Platba v hotovosti',
      'creditcard' => 'Platba kartou',
    );
  }

public function getEnumDelTypes() {
    return array(
      'CESKA_POSTA' => 'Česká pošta - Balík do ruky',
      'CESKA_POSTA_NA_POSTU' => 'Česká pošta - Balík Na poštu',
      'CSAD_LOGISTIK_OSTRAVA' => 'ČSAD Logistik Ostrava',
      'DPD' => 'DPD',
      'DHL' => 'DHL',
      'EMS' => 'EMS',
      'FOFR' => 'FOFR',
      'GEBRUDER_WEISS' => 'Gebrüder Weiss',
      'GEIS' => 'Geis',
      'GENERAL_PARCEL' => 'General Parcel',
      'GLS' => 'GLS',
      'HDS' => 'HDS',
      'ULOZENKA' => 'Uloženka',
      'WEDO' => 'WE|DO',
      'WEDO_HOME' => 'WE|DO HOME',
      'INTIME' => 'InTime',
      'PPL' => 'PPL',
      'RADIALKA' => 'Radiálka',
      'SEEGMULLER' => 'Seegmuller',
      'TNT' => 'TNT',
      'TOPTRANS' => 'TOPTRANS',
      'ZASILKOVNA' => 'Zásilkovna',
      'UPS' => 'UPS',
      'OSOBNE' => 'Osobně',
    );
  }

  /**
  * ciselnik zpusoby dodani
  * @return array
  */
  public function getEnumDelModes() {
    $items = dibi::query("SELECT * FROM deliverymodes WHERE delmasid=0 ORDER BY delorder")
      ->fetchPairs('delid', 'delname');
    return($items);
  }

  Public function getEnumUlozenkaPlacesXml() {
    $xmlFname = TEMP_DIR.'/cache/ulozenka_pobocky.xml';
    $str = "";
    if (!file_exists($xmlFname)) {
      //stahnu XML pro ulozenku
      $str = @file_get_contents('https://www.ulozenka.cz/download/pobocky.xml');
      if (!empty($str)) {
        @file_put_contents(TEMP_DIR.'/cache/ulozenka_pobocky.xml', $str);
      }
    }

    $ulozenkaPobocky = @simplexml_load_file($xmlFname);
    $items = array();
    if (!empty($ulozenkaPobocky)) {
      foreach ($ulozenkaPobocky as $pobocka) {
        $items[(string)$pobocka->zkratka] = $pobocka->nazev.' - '.$pobocka->ulice.', '.$pobocka->psc;
      }
    }
    return $items;
  }

  Public function getEnumUlozenkaPlaces() {
    return dibi::query("SELECT uloshortcut, uloname FROM ulozenkapoints WHERE ulostatus=0 ORDER BY uloname")->fetchPairs("uloshortcut", "uloname");
  }

  Public function getEnumWedoPlaces() {
    return dibi::query("SELECT wedcode, CONCAT(wedcity, \", \", wedstreet, \", \", wedname) as name FROM wedopoints WHERE wedstatus=0 ORDER BY wedcity")->fetchPairs("wedcode", "name");
  }
}
