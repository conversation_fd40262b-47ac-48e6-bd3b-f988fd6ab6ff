<?php
namespace Model;
use Dibi\DateTime;
use Nette, dibi;

class OrdersModel extends BaseModel {

  protected $tableName = "orders";
  protected $fieldPrefix = "ord";

  public function insert($data) {
    $cous = new CouponsModel();

    //naplnim kod objednavky - jedna spolecna ciselna rada pro vsechny objednavky pro aktualni rok
    $today = dibi::fetch("SELECT YEAR(CURDATE()) AS today_year, MONTH(CURDATE()) AS today_month");
    $month = substr('0'.$today->today_month, -2);
    $year = substr($today->today_year, 2, 2);
    $lastOrdCode = (string)dibi::fetchSingle("SELECT ordcode FROM orders ORDER BY ordid DESC LIMIT 1");
    $ordcodeFirst = $year.$month.'00001';

    if (!empty($lastOrdCode)) {
      $lastOrdCode = $year . $month . substr($lastOrdCode, 4);
      $data["ordcode"] = (int)$lastOrdCode + 1;
    } else {
      $data["ordcode"] = $ordcodeFirst;
    }
    if (empty($data["ordcurid"])) $data["ordcurid"] = $this->curId;

    //pokud je vyplneny kupon udelam validaci
    if (!empty($data["ordcoucode"])) {
      $cou = $cous->validateCoupon($data["ordcoucode"]);
      if ($cou["status"] != 'ok') throw New ModelException($cou["text"]);
    }

    //doplním IP adresu
    if (isset($_SERVER['REMOTE_ADDR'])) $data["ordip"] = $_SERVER['REMOTE_ADDR'];

    $ordid = parent::insert($data);
    if ($ordid > 0) {
      $this->logStatus($ordid, 0, (!empty($data["ordadmid"]) ? $data["ordadmid"] : NULL));
      return $ordid;
    }
    return false;
  }

  public function update($id, $data, $setDateU = Null) {
    //pokud prijde kod faktury tak nastavim i datum fakturace
    $ord = $this->load($id);
    if (!empty($data["ordinvcode"])) {
      //overim zda je nastaveny
      if (empty($ord->ordinvcode) && empty($ord->ordinvdate)) {
        $data["ordinvdate"] = new DateTime();
      } else if (!empty($ord->ordinvcode)) {
        unset($data["ordinvcode"]);
        unset($data["ordinvdate"]);
      }
    } else if (isset($data["ordinvcode"]) && empty($data["ordinvcode"])) {
      $data["ordinvcode"] = NULL;
      $data["ordinvdate"] = NULL;
    }

    //pokud je vyplněn kód kupónu
    if (isset($data["ordcoucode"])) {
      if (!empty($data["ordcoucode"]) && empty($ord->ordcoucode)) {
        $cous = new CouponsModel();
        $ret = $cous->useCoupon($data["ordcoucode"]);
        if ($ret["status"] != "ok")  throw New Exception($ret["text"]);
      } else if (empty($data["ordcoucode"]) && !empty($ord->ordcoucode)) {
        $cous = new CouponsModel();
        $cous->unsetCoupon($ord->ordcoucode);
      }
    }
    return (parent::update($id, $data, $setDateU));
  }

  public function delete($id) {
    if (parent::delete($id)) {
      //vymazi vsechny polozky
      return dibi::query("DELETE FROM orditems WHERE oriordid=%i", $id);
    }
    return false;
  }

  public function logStatus($ordid, $status, $admid, $note="")  {
    //zapisu do logu
    return dibi::insert('orders_log', array(
      'orlordid'=>$ordid,
      'orlstatus'=>$status,
      'orladmid'=>$admid,
      'orlnote'=>$note,
      'orldatec'=>new \DateTime,
    ))->execute(dibi::IDENTIFIER);
  }

  /**
   * @param $ordid
   * @param $data
   * @param string $note
   * @return \Dibi\Result|int
   * @throws \Dibi\Exception
   */
  public function logEet($ordid, $data, $note="") {
    //zapisu do logu
    $vals = array(
      "logordid" => $ordid,
      "logprovozid" => $data->data->premiseId,
      "logpoklid" => $data->data->cashRegisterId,
      "logprice" => (double)$data->data->totalPrice,
      "loguuid" => $data->uuid,
      "logbkp" => $data->bkp,
      "logfik" => $data->fik,
      "lognote" => $note,
      "logdatec" => $data->receiptTime,
    );
    return \dibi::insert("eet_log", $vals)->execute(dibi::IDENTIFIER);
  }

  //prepocita objednavku
  public function recalcOrder($id) {
    $config = $this->getConfig();
    $order = $this->load($id);
    if (!$order) return;
    $ordItems = new \Model\OrdItemsModel();

    //suma objednane zbozi pro vypocet slevy
    $priceSumDisc = (double)dibi::fetchSingle("
      SELECT SUM(oriprice*oriqty)
      FROM orditems
      INNER JOIN products ON (oriproid=proid)
      WHERE
      oriordid=%i AND
      pronotdisc=0 AND
      oritypid=0", $order->ordid);

    //jestli je doprava zdarma
    $delFree = (bool)dibi::fetchSingle("
      SELECT COUNT(proid) > 0 
      FROM orditems 
      INNER JOIN products ON (oriproid=proid) 
      WHERE 
      oriordid=%i AND
      prodelfree=1 AND  
      oritypid IN (0)", $order->ordid);

    //zjistim jestli ma narok na slevu
    $discount = 0;
    $discountName = "";
    $discountType = "";
    $payFree = FALSE;
    $userDiscPer = 0;
    $userPrcCat = 'a';
    //pokud je nastaven user na obj. zjistim uzivatelskou slevu
    //nactu zakaznika
    $usr = false;
    if ($order->ordusrid > 0) $usr = dibi::fetch("SELECT * FROM users WHERE usrid=%i", $order->ordusrid);
    if ($usr) {
      $userDiscPer = (int)$usr->usrdiscount;
      $userPrcCat = $usr->usrprccat;
    }
    //načtu kupon
    if (!empty($order->ordcoucode)) {
      $cous = new CouponsModel();
      $cou = $cous->load($order->ordcoucode, "code");
      if ($cou->coudelfree == 1) $delFree = TRUE;
      if ($cou->coupayfree == 1) $payFree = TRUE;
    }
    if (!empty($order->orddisccoupon)) {
      $discount = (double)$order->orddisccoupon;
      $discountName = "Slevový kupón ".$order->ordcoucode;
      $discountType = 'value';
    } else if (!empty($order->ordcoucode) && empty($order->orddisccoupon)) {
      //sleva na produkty jednotlivé - musím přepočítat položky
      $discountType = 'products';
    } else if ($order->orddiscpercent == 0) {
      //je tam slevova polozka ale neni nastavena sleva na objednavce - zjistim jestli ma narok na slevu
      if ($priceSumDisc > 0) {
        //zjistim jestli je mnozstevni sleva
        $disc = (int)dibi::fetchSingle("SELECT dispercent FROM discounts WHERE distypid='volume' AND disprccat=%s", $userPrcCat, " AND discurid=%i", $this->curId, " AND $priceSumDisc BETWEEN disfrom AND disto AND disstatus=0");
        //vyssi slevu pouziju
        if ($disc < $userDiscPer) {
          $discount = $userDiscPer;
          $discountName = "Zákaznická sleva";
          $discountType = "percent";
        } else {
          $discount = $disc;
          $discountName = "Množstevní sleva";
          $discountType = "percent";
        }
      }
    } else {
      $discount = $order->orddiscpercent;
      $discountName = "Sleva";
      $discountType = "percent";
    }

    if ((int)$config["DISCOUNT_DISSOLVE"] == 1 && $discountType != 'products') {
      if ($discount == 0) {
        //vymazu slevu
        dibi::query("UPDATE orditems SET oridisc=0 WHERE oriordid=%i", $id);
      } else if ($discount > 0) {
        //u vsech polozek vypocitam slevu
        $rows = dibi::fetchAll("
        SELECT *
        FROM orditems
        INNER JOIN products ON (oriproid=proid)
        WHERE
        oriordid=%i AND
        pronotdisc=0 AND
        oritypid=0", $order->ordid);
        foreach ($rows as $key => $row) {
          $disc = round((double)$row->oriprice * (double)$row->oriqty * (double)$discount / 100);
          dibi::query("UPDATE orditems SET oridisc=$disc WHERE oriid=%i", $row->oriid);
        }
      }
    } else {
      //sleva u položek
      $rows = dibi::fetchAll("
      SELECT * 
      FROM orditems
      INNER JOIN products ON (oriproid=proid) 
      WHERE 
      oriordid=%i AND
      pronotdisc=0 AND  
      oritypid=0", $order->ordid);
      foreach ($rows as $key => $row) {
        $d = 0;
        if (isset($cou->products[$row->oriproid])) {
          $product = $cou->products[$row->oriproid];
          $d = $product["prodiscount"];
        }
        dibi::query("UPDATE orditems SET oridisc=0, oridiscount=$d, oriprice=(oripriceoriginal-$d) WHERE oriid=%i", $row->oriid);
      }

      //zjistim ID polozky slevy
      $oriid = (int)dibi::fetchSingle("SELECT oriid FROM orditems WHERE oriordid=%i", $id , " AND oritypid=3");

      if ($oriid > 0 && $discount == 0) {
        //vymazu slevu
        $ordItems->delete($oriid);
      } else if ($oriid == 0 && $discount > 0) {
        //zalozim polozku slevy
        $values = array(
          'oriordid' => $id,
          'oritypid' => 3,
          'oriqty' => 1,
          'oriname' => $discountName." ".$discount."%",
          'oriprice' => round(-1 * $priceSumDisc * $discount / 100, 0),
        );
        $ordItems->insert($values);
      } else if ($oriid > 0 && $discount > 0) {
        //upravim polozku
        $values = array(
          'oritypid' => 3,
          'oriname' => $discountName." ".$discount."%",
          'oriprice' => round(-1 * $priceSumDisc * $discount / 100, 0),
        );
        $ordItems->update($oriid, $values);
      }
    }
    //doprava, aktualizuju polozku
    //zjistim cenu objednaneho zbozi vcetne slevy pro vypocet dopravy zdarma
    $priceSum = (double)dibi::fetchSingle("SELECT SUM((oriprice*oriqty)-oridisc) FROM orditems WHERE oriordid=%i AND oritypid IN (0, 3)", $id);
    //nactu si zpusob dopravy
    $paymode = dibi::fetch("
      SELECT delid, delmasid, delname, IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$priceSum." OR (".(($payFree) ? "TRUE" : 'FALSE')." AND delnodelfree=0), 0, delprice".$this->curId.$userPrcCat.") AS delprice
      FROM deliverymodes 
      LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $userPrcCat, " AND discurid=%i", $this->curId, ")
      WHERE delid=%i", $order->orddelid
    );
    $delmode = dibi::fetch("
      SELECT delid, delmasid, delname, IF(COALESCE(disfrom, 0) > 0 AND COALESCE(disfrom, 0) < ".$priceSum." OR (".(($delFree) ? "TRUE" : 'FALSE')." AND delnodelfree=0), 0, delprice".$this->curId.$userPrcCat.") AS delprice
      FROM deliverymodes 
      LEFT JOIN discounts ON (disdelid=delid AND distypid='delfree' AND disprccat=%s", $userPrcCat, " AND discurid=%i", $this->curId, ")
      WHERE delid=%i", $paymode->delmasid
    );
    //zjistim ID polozky dopravy
    $delRow = dibi::fetch("SELECT oriid, oripricemaster FROM orditems WHERE oriordid=%i", $order->ordid , " AND oritypid=1");
    $delPrice = (double)$delRow->oripricemaster > 0 ? $delRow->oripricemaster : ((double)$delmode->delprice + (double)$paymode->delprice);
    $values = array(
      'oriprice' => $delPrice,
    );
    $orditems = new OrdItemsModel();
    $orditems->update($delRow->oriid, $values);

    //zjistim cenu objednavky
    $priceSum = dibi::fetchSingle("SELECT SUM((oriprice*oriqty)-oridisc) FROM orditems WHERE oriordid=%i", $id);
    $discSum = dibi::fetchSingle("SELECT SUM(oridisc) FROM orditems WHERE oriordid=%i", $id);


    $vat = (int)$config['VATTYPE_0'];
    $vatLow = (int)$config['VATTYPE_1'];
    $vatType = (string)$config['PRICEVAT'];
    if ($vatType == 'inclvat') {
      $priceSumVat = $priceSum;
      $priceSum = dibi::fetchSingle("SELECT SUM(((oriprice*oriqty)-oridisc)/(1+(IF(COALESCE(orivatid,0)=0,$vat,$vatLow)/100))) FROM orditems WHERE oriordid=%i", $id);
    } else {
      $priceSumVat = dibi::fetchSingle("SELECT SUM(((oriprice*oriqty)-oridisc)*(1+(IF(COALESCE(orivatid,0)=0,$vat,$vatLow)/100))) FROM orditems WHERE oriordid=%i", $id);
    }
    $priceSumVat = round($priceSumVat, 2);

    //zjistim celkovou hmotnost
    $weightSum = (double)dibi::fetchSingle("SELECT SUM(proweight*oriqty) FROM orditems INNER JOIN products ON (proid=oriproid) WHERE oriordid=%i", $id);

    $vals = array(
      'ordprice'=>round($priceSum, $this->curDigits),
      'ordpricevat'=>round($priceSumVat, $this->curDigits),
      'orddisc'=>round($discSum, $this->curDigits),
      'ordweight'=>$weightSum,
    );
    $this->update($id, $vals);
  }

  /**
  * put your comment there...
  *
  * @param integer $id
  */
  public function blAnalyse($order) {
    $orders = array();
    //$order = dibi::fetch("SELECT * FROM orders WHERE ordid=%i", $id);
    if ($order->ordstatus != 7) {
      //vyberu vsechny objednavky ktere maji priznak spam a maji stejne udaje o ojednavateli
      $sql = "SELECT ordid, ordcode FROM orders WHERE ordstatus=7 AND ordid!=$order->ordid AND (
      ordilname='$order->ordilname' OR
      ".(empty($order->ordstlname) ? "" : " ordstlname='$order->ordstlname' OR ")."
      (ordistreet='$order->ordistreet' AND ordistreetno='$order->ordistreetno' AND ordicity='$order->ordicity' AND ordipostcode='$order->ordipostcode') OR
      ".(empty($order->ordststreet) ? "" : "(ordststreet='$order->ordststreet' AND ordststreetno='$order->ordststreetno' AND ordstcity='$order->ordstcity' AND ordstpostcode='$order->ordstpostcode') OR ")."
      ordmail='$order->ordmail'
      ".(empty($order->ordtel) ? "" : " OR ordtel='$order->ordtel'").")";
      $rows = dibi::fetchAll($sql);
      foreach ($rows as $row) {
        $orders[$row->ordid] = $row->ordid;
      }
    }
    return($orders);

  }

  public function makeInvoice($id) {
    $orders = new OrdersModel();
    $order = $orders->load($id);
    if (!$order) {
      throw new ModelException('Příslušná objednávka nenalezena.');
    } else {
      if (!empty($order->ordinvcode)) {
        throw new ModelException('Faktura je už vystavená.');
      } else {
        if (!$orders->update($id, array('ordinvcode'=>$order->ordcode, 'ordinvdate'=>new \DateTime))) {
          throw new ModelException('Fakturu se nepodařilo vystavit.');
        }
      }
    }
  }

  /********************* ciselniky *********************/

  /**
  * ciselnik usrstatus
  * @return array
  */
  public function getEnumOrdStatus() {
    return array(
      0 => 'Čeká na zpracování',
      1 => 'Vyřizuje se',
      2 => 'Čeká na platbu',
      6 => 'Zaplaceno',
      8 => 'Připraveno k odběru',
      3 => 'Odeslána',
      9 => 'Bude odeslána',
      4 => 'Uzavřená',
      5 => 'Stornovaná',
      7 => 'Černá listina',
    );
  }

  public function getEnumOrdDelId($onlyActive = TRUE) {
    //naplnim zpusoby dopravy
    $rows = dibi::fetchAll("SELECT delid, delname FROM deliverymodes WHERE delmasid=0 ".($onlyActive ? "AND delstatus=0" : "")." ORDER BY delorder");
    $deliveryModeRows  = array();
    foreach ($rows  as $row) {
      $deliveryModeRows[$row->delname] = dibi::query("SELECT delid, CONCAT('".$row->delname." / ', delname) AS delname FROM deliverymodes WHERE delmasid=%i ".($onlyActive ? "AND delstatus=0" : "")." ORDER BY delorder", $row->delid)->fetchPairs('delid', 'delname');
    }
    return $deliveryModeRows;
  }

  public function getEnumOrdDelIdSimple() {
    //naplnim zpusoby dopravy
     return dibi::query("SELECT delid, delname FROM deliverymodes WHERE delmasid> 0 AND delstatus=0 ORDER BY delorder")->fetchPairs('delid', 'delname');
  }

  public function getEnumFirms() {
    //naplnim zpusoby dopravy
    return array(
      1 => 'Dodavatel - NEplátce DPH',
      2 => 'Dodavatel - plátce DPH',
    );
  }

}
