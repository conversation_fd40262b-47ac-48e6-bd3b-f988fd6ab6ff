<?php

require __DIR__ . '/../vendor/autoload.php';

// Configure application
$configurator = new Nette\Configurator;

define("IS_LOCALHOST", $_SERVER["SERVER_NAME"] === '127.0.0.1');

$configurator->setDebugMode(IS_LOCALHOST); // enable for your remote IP
$configurator->enableTracy(__DIR__ . '/../log');

error_reporting(~E_USER_DEPRECATED ^ E_DEPRECATED); // note ~ before E_USER_DEPRECATED

$configurator->setTimeZone('Europe/Prague');
$configurator->setTempDirectory(__DIR__ . '/../temp');

$configurator->createRobotLoader()
  ->addDirectory(__DIR__)
  ->addDirectory(__DIR__ . '/../libs')
  ->register();

// Create Dependency Injection container from config.neon file
$configurator->addConfig(__DIR__ . '/config/config.neon');
$configurator->addConfig(__DIR__ . '/config/config.local.neon');
$container = $configurator->createContainer();

dibi::setConnection($container->getByType('Dibi\Connection'));

//Setup application router
$router = new Nette\Application\Routers\RouteList;

$router[] = new Nette\Application\Routers\Route('index.php', array(
    'module' => 'Front',
    'presenter' => 'Homepage',
    'action' => 'default',
), Nette\Application\Routers\Route::ONE_WAY);

// Admin
$router[] = new Nette\Application\Routers\Route('administrace/<presenter>/<action>', array(
  'module' => 'Admin',
  'presenter' => 'Admin',
  'action' => 'default',
));

// routa pro detail zbozi
$router[] = new Nette\Application\Routers\Route('<key>-d<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Product',
    'action' => 'detail',
));
//jednosmerna routa puvodni
$router[] = new Nette\Application\Routers\Route('<key>.html,det,<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Product',
    'action' => 'detailOld',
), Nette\Application\Routers\Route::ONE_WAY);

// routa pro detail katalogu
$router[] = new Nette\Application\Routers\Route('<key>-k<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Catalog',
    'action' => 'detail',
));
//jednosmerna routa puvodni
$router[] = new Nette\Application\Routers\Route('<key>.html,kat,<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Catalog',
    'action' => 'detailOld',
), Nette\Application\Routers\Route::ONE_WAY);

// routa pro textove stranky
$router[] = new Nette\Application\Routers\Route('<key>-t<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Page',
    'action' => 'detail',
));

// routa pro články
$router[] = new Nette\Application\Routers\Route('<key>-c<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Article',
    'action' => 'detail',
));
//jednosmerna routa puvodni
$router[] = new Nette\Application\Routers\Route('<key>.html,rec,<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Article',
    'action' => 'detailOld',
), Nette\Application\Routers\Route::ONE_WAY);

// routa pro novinky
$router[] = new Nette\Application\Routers\Route('<key>-n<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'New',
    'action' => 'detail',
));


// routa pro vyrobce
$router[] = new Nette\Application\Routers\Route('<key>-m<id [0-9]+>', array(
    'module' => 'Front',
    'presenter' => 'Manufacturer',
    'action' => 'detail',
));
//jednosmerna routa puvodni
$router[] = new Nette\Application\Routers\Route('vyrobce-<key>.html', array(
    'module' => 'Front',
    'presenter' => 'Manufacturer',
    'action' => 'detailOld',
), Nette\Application\Routers\Route::ONE_WAY);


// routa pro mailing
$router[] = new Nette\Application\Routers\Route('c/<typid [0-9]+>[/<mamid [0-9]+>][/<usrid [0-9]+>][/<par1>]', array(
    'module' => 'Front',
    'presenter' => 'Mailing',
    'action' => 'click',
));
$router[] = new Nette\Application\Routers\Route('d/<mamid [0-9]+>/<usrid [0-9]+>/<key>', array(
    'module' => 'Front',
    'presenter' => 'Mailing',
    'action' => 'detail',
));

// deklarace obecné dvousměrné routy s cool-url tvarem
$router[] = new Nette\Application\Routers\Route('<presenter>/<action>[/<id>]', array(
    'module' => 'Front',
    'presenter' => array(
      Nette\Application\Routers\Route::VALUE => 'Homepage',
      Nette\Application\Routers\Route::FILTER_TABLE => array(
        'kosik' => 'Basket',
        'zakaznik' => 'User',
      ),
    ),
    'action' => array(
      Nette\Application\Routers\Route::VALUE => 'default',
      Nette\Application\Routers\Route::FILTER_TABLE => array(
        'novy' => 'add',
        'prihlaseni' => 'login',
        'poslat-heslo' => 'sendPassword',
        'doprava-platba' => 'orderDelMode',
        'kontaktni-udaje' => 'orderContact',
        'sumarizace-objednavky' => 'orderSumarize',
      ),
    ),
    'id' => NULL,
));

$container->addService('router', $router);

return $container;
