php: # PHP configuration
  date.timezone: Europe/Prague
  zlib.output_compression: yes
nette:
  application:
    catchExceptions: TRUE
    errorPresenter: Front:Error
  session:
    autoStart: true
  security:
    debugger: true
    roles:
      guest:
      editor:
      expedice:
      admin:
    resources:
      Admin:Admin:
      Admin:User:
      Admin:Catalog:
      Admin:Product:
      Admin:ProductCA:
      Admin:ProparamDef:
      Admin:Export:
      Admin:Import:
      Admin:Order:
      Admin:Coupon:
      Admin:DeliveryMode:
      Admin:Manufacturer:
      Admin:Page:
      Admin:Article:
      Admin:Menu:
      Admin:New:
      Admin:MenuIndex:
      Admin:Config:
      Admin:Dictionary:
      Admin:Discount:
      Admin:Comment:
      Admin:Mailing:
      Admin:WidexImport:
  debugger:
      editor: 'http://localhost:8091?message=%file:%line'
      email: <EMAIL>
      strictMode: TRUE
parameters:
  app:
    domain: kosmetika-biolage.cz
    basketOrderOnOnePage: FALSE
  adminModule:
    menuBackgroundColor: "#009cdd"
  labels:
    #nazvy cenovych hladin
    com: Cena běž<PERSON>
    a: Cena A
    b: Cena B
    c: Cena C
    d: Cena D
    #nazvy priznaku u zbozi (akce, novinka, atd.)
    protypid: Akce
    protypid2: Novinka
    protypid3: Tip
    protypid4: Nejprodávánější
    protypid5: Připravujeme
  currency:
    1:
      id: 1
      key: CZK
      code: Kč
      decimals: 0
    #2:
    #  id: 2
    #  code: €
    #  key: EUR
    #  decimals: 1

  hosts:
    localhost:
      curId: 1
    127.0.0.1:
      curId: 1
    shop.widex.cz:
      curId: 1

  heureka:
    IdOverenoZakazniky: 060e647db9d67408d49bc968116cc8c7
    IdOverenoZakaznikyCertifikat:
    KeyMereniKonverzi: 25AE980CD434F571F91AC4CE26E229F7

  google:
    ua:  #GoogleAnalytics UA
    conversionId: #Google Retargeting

  seznam:
    conversionId: #seznam retargeting

  facebook:
    pixelCodeId:

  toplist:
    id:

  sms:
    login:
    passw:

  fio:
    account:
    token:

  csob:
    merchantId:
    password: NULL
    shopName:
    eetPremiseId:
    eetCashRegisterId: EshopOnline
    eetVerificationMode: FALSE
    isProduction: TRUE

  payU1:
    posId:
    key1:
    key2:
    posAuthKey:

  payU2:
    posId:
    key1:
    key2:
    posAuthKey:

  onlinePayTypes:
    c: "Platební kartou - online"
    rf: "eKonto (Raiffeisenbank)"
    kb: "MojePlatba (Komerční banka)"
    cs: "PLATBA 24 (Česká spořitelna)"
    pf: "Fio banka"
    pg: "GE Money Bank"
    mp: "mPeníze (mBank)"
    uc: "UniCredit bank"
    pv: "Sberbank"
    cb: "ČSOB"
    era: "Era banka"
    mo: "Mobito (peníze v mobilu)"
    psc: "PaySec"

  ulozenka:
    shopId: 309
    apiKey: ''

extensions:
  mobileDetect: IPub\MobileDetect\DI\MobileDetectExtension
  dibi: Dibi\Bridges\Nette\DibiExtension22
  recaptcha: Minetro\ReCaptcha\DI\ReCaptchaExtension
  visualPaginator: IPub\VisualPaginator\DI\VisualPaginatorExtension

recaptcha:
  secretKey: '6LeZn1oUAAAAAFEx18R7wXRcBhkW8KQrZNVBbzbW'
  siteKey: '6LeZn1oUAAAAAIikjRxuV-at8fo6r3d_wmPp-ff_'

dibi:
  driver: mysqli
  host: 127.0.0.1
  database: ekramek
  username: root
  password: root

services:
  - Classes\NeonParametersRepository(@container::getParameters())

  myTemplateFilters:
    factory: \TemplateFilters(%wwwDir%, @application)

  connection.panel:
    class: Dibi\Bridges\Tracy\Panel

  authenticator:
    class: MyAuthenticator

  nette.authorizator:
    setup:
     #guest
      - @self::allow( guest, 'Admin:Admin', ['login', 'logout', 'default'])
      - @self::allow( guest, 'Admin:WidexImport', ['import'])
     #admin - moduly
      - @self::allow( admin, 'Admin:Admin', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:User', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Catalog', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Product', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:ProparamDef', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Import', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Order', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:DeliveryMode', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Manufacturer', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Page', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Article', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Menu', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:New', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:MenuIndex', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Config', Nette\Security\Permission::ALL)
     #- @self::allow( admin, 'Admin:Discount', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Coupon', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Comment', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:Mailing', Nette\Security\Permission::ALL)
      - @self::allow( admin, 'Admin:WidexImport', Nette\Security\Permission::ALL)
     #admin - akce
      - @self::allow( admin, 'Admin:Admin', ['changeForeign', 'orderSetAuthor', 'multiaccounts'])
     #CustomAction práva Název modulu+CA
      - @self::allow( admin, 'Admin:ProductCA', ['gifts'])
     #- @self::allow( admin, 'Admin:ProductCA', ['ActionPrices'])
     #editor - moduly
      - @self::allow( editor, 'Admin:Admin', ['login', 'logout', 'default', 'edit'])
      - @self::allow( editor, 'Admin:Comment', Nette\Security\Permission::ALL)
      - @self::allow( editor, 'Admin:Article', Nette\Security\Permission::ALL)
     #expedice - moduly
      - @self::allow( expedice, 'Admin:Admin', ['login', 'logout', 'default', 'edit'])
      - @self::allow( expedice, 'Admin:Comment', Nette\Security\Permission::ALL)
      - @self::allow( expedice, 'Admin:Article', Nette\Security\Permission::ALL)
      - @self::allow( expedice, 'Admin:New', Nette\Security\Permission::ALL)
      - @self::allow( expedice, 'Admin:Order', Nette\Security\Permission::ALL)
      - @self::allow( expedice, 'Admin:User', Nette\Security\Permission::ALL)
      - @self::allow( expedice, 'Admin:DeliveryMode', Nette\Security\Permission::ALL)
