<?php

class TemplateFilters extends Nette\Object {
    /** @var string */
    private $wwwDir;

    /** @var Nette\Application\UI\Presenter */
    private $presenter;

    /** @var int */
    private $formatPriceDecimals=0;

    /** @var int */
    private $formatPriceCurrency='Kč';

    public function __construct($wwwDir, Nette\Application\Application $application) {
      $this->wwwDir = $wwwDir;
      $this->presenter = $application->getPresenter();
      if ($this->presenter !== NULL) {
        $this->formatPriceCurrency = (string)$this->presenter->currency["code"];
        $this->formatPriceDecimals = (int)$this->presenter->currency["decimals"];
      }
    }

    /**
    * Method we will register as callback
    * in method $template->addFilter().
    */
    public function loader($helper) {
      if (method_exists($this, $helper)) {
        //return [$this, $helper];
      }
      return call_user_func_array(callback($this, $helper), array_slice(func_get_args(), 1));
    }

    /**
    * vraci nazev obrazku zbozi
    *
    * @param string $size [list,detail,big]
    * @param dibirow $row
    * @param boolean $empty pokud obrázek neexistuje vrátí prázdný string
    *
    * @return string cesta k obrazku
    */
    public function getProductPicName($row, $size, $empty=FALSE) {
      $path = "pic/product/$size/";
      $picPath = $this->wwwDir."/".$path;
      $fileName = ($row->propicname != "" ? trim($row->propicname).'.jpg' : $row->procode.'.jpg');

      if (!file_exists($picPath.$fileName)) {
        if ($empty) {
          return "";
        } else {
          $fileName = "no.jpg";
          $path = "img/";
        }
      }

      $fileName = rawurlencode($fileName);
      return($path.$fileName);
    }

    /**
    * vraci nazev obrazku zbozi - pokud se jedna o variantu vrati nazev obrazku master polozky
    *
    * @param string $size [list,detail,big]
    * @param dibirow $row
    *
    * @return string cesta k obrazku
    */
    public function getProductPicNameMaster($row, $size, $empty=FALSE) {
      if ((int)$row->promasid > 0) {
        $row = dibi::fetch("SELECT procode, propicname FROM products WHERE proid=%i", $row->promasid);
      }
      return($this->getProductPicName($row, $size, $empty));
    }

    /**
    * vraci nazev obrazku dopravy
    *
    * @param dibirow $row doprava
    * @param boolean $empty pokud obrázek neexistuje vrátí prázdný string
    *
    * @return string cesta k obrazku
    */
    public function getDelModePicName($row, $empty=FALSE) {
      $path = "img/delivery/";
      $picPath = $this->wwwDir."/".$path;
      $fileName = \Nette\Utils\Strings::lower($row->delcode).'.jpg';

      if (!file_exists($picPath.$fileName)) {
        if ($empty) {
          return "";
        } else {
          $fileName = "no.jpg";
        }
      }

      $fileName = rawurlencode($fileName);
      return($path.$fileName);
    }

    /**
    * vraci nazev obrazku platby
    *
    * @param dibirow $row doprava
    * @param boolean $empty pokud obrázek neexistuje vrátí prázdný string
    *
    * @return string cesta k obrazku
    */
    public function getPayModePicName($row, $empty=FALSE) {
      $path = "img/payment/";
      $picPath = $this->wwwDir."/".$path;
      $fileName = \Nette\Utils\Strings::lower($row->delcode).'.jpg';

      if (!file_exists($picPath.$fileName)) {
        if ($empty) {
          return "";
        } else {
          $fileName = "no.jpg";
        }
      }

      $fileName = rawurlencode($fileName);
      return($path.$fileName);
    }


    /**
    * vraci nazev obrazku novinky
    *
    * @param string $size [list,detail,big]
    * @param dibirow $row
    *
    * @return string cesta k obrazku
    */
    public function getNewPicName($row, $size) {
      $path = "pic/new/$size/";
      $picPath = $this->wwwDir."/".$path;
      $fileName = 'new_'.$row->newid.'.jpg';

      if (!file_exists($picPath.$fileName)) {
        $fileName = "no.jpg";
        $path = "img/";
      }

      $fileName = rawurlencode($fileName);
      return($path.$fileName);
    }

    /**
    * vraci nazev obrazku clanku
    *
    * @param string $size [list,detail,big]
    * @param dibirow $row
    *
    * @return string cesta k obrazku
    */
    public function getArtPicName($row, $size) {
      $path = "pic/art/$size/";
      $picPath = $this->wwwDir."/".$path;
      $fileName = 'art_'.$row->artid.'.jpg';

      if (!file_exists($picPath.$fileName)) {
        $fileName = "no.jpg";
        $path = "img/";
      }
      $fileName = rawurlencode($fileName);
      return($path.$fileName);
    }

    /**
    * vraci nazev obrazku katalogu
    *
    * @param dibirow $row
    *
    * @return string cesta k obrazku
    */
    public function getCatalogPicName($row, $size='small', $returnEmptyPic=TRUE) {
      $path = "pic/catalog/".$size."/";
      $picPath = $this->wwwDir."/".$path;
      $fileName = $row->catid.'.jpg';
      if (!file_exists($picPath.$fileName)) {
        if ($returnEmptyPic) {
          $fileName = "no.jpg";
          $path = "img/";
        } else {
          return "";
        }
      }
      $fileName = rawurlencode($fileName);
      return($path.$fileName);
    }

    /**
    * vraci nazev obrazku + rozmery  src="..." width="..." height="..."
    *
    * @param dibiRow $row
    * @param string $size [list,detail,big]
    */
    public function getProPicSrcSize($row, $size, $empty=FALSE) {
      //naplni soubor i s cestou
      $fileName = $this->getProductPicName($row, $size, $empty);
      $config = $this->presenter->config;
      if (file_exists($fileName)) {
        //$image = NImage::fromFile($picPath.$fileName);
        //$w = $image->getWidth();
        //$h = $image->getHeight();
        $size = explode('x', $config["PROPICSIZE_".strtoupper($size)]);
        $w = $size[0];
        $h = $size[1];
      } else {
        $size = explode('x', $config["PROPICSIZE_".strtoupper($size)]);
        $w = $size[0];
        $h = $size[1];
        $fileName = "no.jpg";
      }

      return(' src="'.$fileName.'" width="'.$w.'" height="'.$h.'" ');
    }


    /**
    * vraci URL klic zbozi
    *
    * @param dibirow $row
    * @return URL klic
    */
    public function getProKey($row) {
      return((!empty($row->prokey) ? $row->prokey : Nette\Utils\Strings::webalize($row->proname)));
    }

    /**
    * vraci URL klic katalogu
    *
    * @param dibirow $row
    * @return URL klic
    */
    public function getCatKey($row) {
      return((!empty($row->catkey) ? $row->catkey : Nette\Utils\Strings::webalize($row->catname)));
    }

    public function getDiscountInPer($pricecom, $price) {
      if ((int)$pricecom > $price) {
        return (round(($pricecom - $price) / $pricecom * 100)."%");
      } else {
        return "";
      }
    }

    public function getUrlKey($key, $name) {
      return((!empty($key) ? $key : Nette\Utils\Strings::webalize($name)));
    }

    public function getQty($qty) {
      return $this->getUnits($qty, "kus");
    }

    /**
   * vraci jednotky podle poctu kusu
   *
   * @param integer $qty pocet
   * @param string $unitName nazev jednotky
   * @return URL klic
   */
    public function getUnits($qty, $unitName) {
      $qtyText = $qty;
      if ($unitName == 'kus') {
        $text = "kusů";
        switch ((int)$qty) {
          case 1:
            $text = "kus";
            break;
          case 2:
          case 3:
          case 4:
            $text = "kusy";
            break;
        }
        if ($qty > 5) $qtyText = ">5";
      }
      if ($unitName == 'hodinou') {
        $text = "hodinami";
        switch ((int)$qty) {
          case 1:
            $text = "hodinou";
            break;
        }
      }
      if ($unitName == 'zákazník') {
        $text = "zákazníků";
        switch ((int)$qty) {
          case 1:
            $text = "zákazník";
            break;
          case 2:
          case 3:
          case 4:
            $text = "zákazníci";
            break;
        }

      }
      return ($qtyText.' '.$text);
    }

    /**
    * vraci naformatovanou cenu
    *
    * @param mixed $price
    * @param mixed $decimals
    */
    public function formatPrice($price, $decimals=Null) {
      //if ((double)$price == 0) return "na dotaz";
      $price = (double)$price;
      if ($decimals == Null) $decimals = $this->formatPriceDecimals;
      $formated = str_replace(" ", "\xc2\xa0", number_format($price, $decimals, ",", " "));
      if ($decimals == 1) $formated .= '0';
      return $formated." ".$this->formatPriceCurrency;
    }

    public function formatNumber($number, $decimals) {
      //if ((double)$price == 0) return "na dotaz";
      $number = (double)$number;
      $formated = str_replace(" ", "\xc2\xa0", number_format($number, $decimals, ",", " "));
      return $formated;
    }

    /**
    * vraci naformatovanou cenu
    *
    * @param mixed $price
    * @param mixed $decimals
    */
    public function formatPriceByCurId($price, $curId) {
      //if ((double)$price == 0) return "na dotaz";
      $price = (double)$price;
      $priceCurrency = $this->formatPriceCurrency;
      $priceDecimals = $this->formatPriceDecimals;
      if (isset($this->presenter->neonParameters["currency"][$curId])) {
        $priceCurrency = $this->presenter->neonParameters["currency"][$curId]["code"];
        $priceDecimals = $this->presenter->neonParameters["currency"][$curId]["decimals"];
      }
      $formated = str_replace(" ", "\xc2\xa0", number_format($price, $priceDecimals, ",", " "));
      if ($priceDecimals == 1) $formated .= '0';
      return $formated." ".$priceCurrency;
    }

    public function commentStripTags($text, $isAdmin=FALSE) {
      $text = nl2br($text);
      if (!$isAdmin) {
        $text = strip_tags($text, '<br>');
      }
      return $text;
    }

    public function toUtf8 ($cp1250String) {
      return iconv('windows-1250','utf-8',$cp1250String);
    }


    public function glyph($name, $title="") {
      $glyphName = "";
      switch ($name) {
        case 'add':
          if (empty($title)) $title = "přidat položku";
          $glyphName = "plus";
          break;
        case 'edit':
          if (empty($title)) $title = "upravit položku";
          $glyphName = "pencil";
          break;
        case 'delete':
          if (empty($title)) $title = "vymazat položku";
          $glyphName = "remove";
          break;
        case 'active':
          if (empty($title)) $title = "aktivní";
          $glyphName = "ok";
          break;
        case 'blocked':
          if (empty($title)) $title = "blokovaná";
          $glyphName = "off";
          break;
        case 'front':
          if (empty($title)) $title = "Otevřít ve veřejné části";
          $glyphName = "globe";
          break;
        case 'info':
          if (empty($title)) $title = "Informace";
          $glyphName = "info-sign";
          break;
        case 'user':
          if (empty($title)) $title = "Uživatel";
          $glyphName = "user";
          break;
        case 'export':
          if (empty($title)) $title = "Export";
          $glyphName = "export";
          break;
        case 'stats':
          if (empty($title)) $title = "Statistika";
          $glyphName = "stats";
          break;
      }

      return '
      <span class="glyphicon glyphicon-'.$glyphName.'" aria-hidden="true" title="'.$title.'"></span>';
    }
}
