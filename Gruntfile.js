module.exports = function(grunt) {

  // nacitani tasku
  require('jit-grunt')(grunt);

  // pocitani rychlosti
  require('time-grunt')(grunt);

  // konfigurace projektu
  grunt.initConfig({

    // sledovani zmen v souborech
    watch: {
      grunt: {
        files: ['Gruntfile.js']
      },
      javascript: {
        files: ['www/js/*.js', '!www/js/scripts.js'],
        tasks: ['concat']
      },
      less: {
        files: ['www/css/*.less', 'www/css/**/*.less', 'www/css/**/*.css', '!www/css/styles.css', '!www/css/styles-legacy.css'],
        tasks: ['less']
      },
      iconfont: {
        files: ['www/img/icons/*.svg'],
        tasks: ['webfont']
      }
    },

    // spojovani souboru
    concat: {
      dist: {
        src: ['www/js/jquery.unveil.js', 'www/js/jquery.magnific-popup.js', 'www/js/netteForms.js', 'www/js/main.js'],
        dest: 'www/js/scripts.js'
      }
    },

    // minifikace JS
    uglify: {
      my_target: {
        files: {
          'www/js/scripts.js': ['www/js/scripts.js']
        }
      }
    },

    // LESS
    less: {
      development: {
        options: {
          paths: ["css"]
        },
        files: {
          "www/css/styles.css": "www/css/styles.less"
        }
      }
    },

    // autoprefixer
    autoprefixer: {
      dist: {
        options: {
          browsers: ['last 6 versions', 'ie 7', 'ie 8', 'ie 9']
        },
        files: {
          'www/css/styles.css': 'www/css/styles.css'
        }
      }
    },

    // legaCSSy
    legacssy: {
      dist: {
        files: {
          'www/css/styles-legacy.css': 'www/css/styles.css'
        }
      }
    },

    // minifikace CSS
    cssmin: {
      options: {
        shorthandCompacting: true
      },
      dist: {
        files: {
          'www/css/styles.css': 'www/css/styles.css',
          'www/css/styles-legacy.css': 'www/css/styles-legacy.css'
        }
      }
    },

    // optimalizace obrazku
    imagemin: {
      dynamic: {
        files: [{
          expand: true,
          cwd: 'www/img/',
          src: ['**/*.{png,jpg,gif}'],
          dest: 'www/img/'
        }]
      }
    },

    // generovani icon-fontu
    webfont: {
      icons: {
        src: 'www/img/icons/*.svg',
        dest: 'www/fonts',
        options: {
          engine: 'node',
          stylesheet: 'less',
          hashes: true,
          autoHint: false,
          relativeFontPath: '../fonts',
          font: 'icons',
          templateOptions: {
            baseClass: 'icon',
            classPrefix: 'icon--',
            mixinPrefix: 'icon-'
          }
        }
      }
    },

    // browser sync
    browserSync: {
      default_options: {
        bsFiles: {
          src: [
            "www/css/*.css",
            "www/css/*.html",
            "www/js/*.js",
            "*.html",
            "*.htm",
            "*.php",
            "**/*.latte"
          ]
        },
        options: {
          watchTask: true,
          proxy: "http://kosmetika-biolage.test/"
        }
      }
    }

  });

  // tasky
    // vytvoreni JS
    grunt.registerTask('makejs', ['concat', 'uglify']);
    // vytvoreni CSS
    grunt.registerTask('makecss', ['less', 'autoprefixer', 'legacssy', 'cssmin']);
    // deploy
    grunt.registerTask('deploy', ['makejs', 'makecss', 'newer:imagemin']);
    // defaultni task
    grunt.registerTask('default', ['browserSync', 'watch']);

};
